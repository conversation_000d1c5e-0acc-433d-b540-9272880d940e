import { StringBuilder } from '@ohos/dataorm/src/main/ets/core/StringBuilder';
import { CommonUtil, TextUtils } from '@ohos/utils/Index';
import { LevelBean } from '@ohos/zp_api/Index';


export class GeekDataHelper {
  static readonly BEFORE_1990: number = 1989; //滑轮1990年之前
  static readonly BEFORE_1990_DATE: number = 19890101; //滑轮1990年之前

  static readonly DB_RIGHT_NOW: number = -1;

  static readonly VIEW_RIGHT_NOW: string = "至今";

  static readonly DB_BEFORE_1990: number = 19890101;

  static readonly VIEW_BEFORE_1990: string = "1990以前";

  static parseYear(birthday: string): number {
    if (TextUtils.isEmpty(birthday) || birthday.length != 8) return 0;
    const yearText = birthday.substring(0, 4);
    return Number.parseInt(yearText, 0)
  }

  static parseMonth(birthday: string): number {
    if (TextUtils.isEmpty(birthday) || birthday.length != 8) return 0;
    const monthText = birthday.substring(4, 6);
    return Number.parseInt(monthText, 0)
  }


  static getDateForText(year: number, month: number): string {
    if (year <= 0 || month <= 0) return "";
    const fixText = month < 10 ? "0" : "";
    return year + fixText + month + "01";
  }

  static getDateForLong(year: number, month: number): number {
    if (year <= 0 || month <= 0) return 0;
    const fixText = month < 10 ? "0" : "";
    const result = year + fixText + month + "01";
    return Number.parseInt(result, 0)
  }

  /**
   * 判断是否是低学历
   * @param degreeCode
   * @returns
   */
  static isLowEducation(degreeCode: number): boolean {
    return degreeCode == 206 || degreeCode == 208 || degreeCode == 209 || degreeCode == 201
  }


  /**
   * 开始时间（左右滚轮数据）
   */
  static getFromYearList(): Array<LevelBean> {
    const fromArray: Array<LevelBean> = new Array<LevelBean>();
    let year = new Date().getFullYear()

    for (let i = year; i > 1990; i--) {
      const level: LevelBean = new LevelBean(i, i.toString())
      // level.subLevelModelList = new Array<LevelBean>();
      level.subLevelModelList.push(...GeekDataHelper.getMonthList())
      fromArray.push(level)
    }
    const lastLevel: LevelBean = new LevelBean(GeekDataHelper.DB_BEFORE_1990, GeekDataHelper.VIEW_BEFORE_1990)
    lastLevel.subLevelModelList.push(new LevelBean(GeekDataHelper.DB_BEFORE_1990, GeekDataHelper.VIEW_BEFORE_1990))
    fromArray.push(lastLevel)

    return fromArray;
  }

  /**
   * 结束时间（左右滚轮数据）
   */
  static getToYearList(): Array<LevelBean> {
    const toArray: Array<LevelBean> = new Array<LevelBean>();
    const nowLevelBean: LevelBean = new LevelBean(GeekDataHelper.DB_RIGHT_NOW, GeekDataHelper.VIEW_RIGHT_NOW)
    // nowLevelBean.subLevelModelList = new Array<LevelBean>();
    nowLevelBean.subLevelModelList.push(new LevelBean(GeekDataHelper.DB_RIGHT_NOW, GeekDataHelper.VIEW_RIGHT_NOW))

    toArray.push(nowLevelBean);
    toArray.push(...GeekDataHelper.getFromYearList());
    return toArray;
  }

  private static getMonthList(): Array<LevelBean> {
    const monthList: Array<LevelBean> = new Array<LevelBean>()
    for (let i = 1; i < 13; i++) {
      const sb: StringBuilder = new StringBuilder();
      if (i < 10) {
        sb.append(0);
      }
      sb.append(i);
      monthList.push(new LevelBean(i, sb.toString()));
    }
    return monthList;
  }


  /**
   * 获取下标（年）
   */
  static initYearIndex(date8: number, yearList: Array<LevelBean>): number {
    let yearCode: number = 0;
    if (date8 == GeekDataHelper.DB_RIGHT_NOW) {
      yearCode = GeekDataHelper.DB_RIGHT_NOW;
    } else if (date8 == GeekDataHelper.DB_BEFORE_1990) {
      yearCode = GeekDataHelper.DB_BEFORE_1990;
    } else {
      const strDate: string = date8.toString();
      if (strDate.length >= 4) {
        yearCode = TextUtils.getInt(strDate.substring(0, 4));
      }
    }

    const size: number = yearList.length
    for (let i = 0; i < size; i++) {
      const item: LevelBean | null = CommonUtil.getElement(yearList, i)
      if (item == null) continue;
      if (item.code == yearCode) {
        return i;
      }
    }
    return yearCode;
  }

  /**
   * 获取下标（月）
   */
  static initMonthIndex(date8: number, yearList: Array<LevelBean>): number {
    let monthCode: number = 0;
    if (date8 == GeekDataHelper.DB_RIGHT_NOW) {
      monthCode = GeekDataHelper.DB_RIGHT_NOW;
    } else if (date8 == GeekDataHelper.DB_BEFORE_1990) {
      monthCode = GeekDataHelper.DB_BEFORE_1990;
    } else {
      const strDate: string = date8.toString();
      if (strDate.length >= 6) {
        monthCode = TextUtils.getInt(strDate.substring(4, 6));
      }
    }

    const size: number = yearList.length
    for (let i = 0; i < size; i++) {
      const item: LevelBean | null = CommonUtil.getElement(yearList, i);
      if (item == null) continue;
      for (let i1 = 0; i1 < item.subLevelModelList.length; i1++) {
        const subItem: LevelBean | null = CommonUtil.getElement(item.subLevelModelList, i1);
        if (subItem == null) continue;
        if (subItem.code == monthCode) {
          return i1;
        }
      }
    }
    return monthCode;
  }


  static wheelViewToDate8(yearBean: LevelBean, monthBean: LevelBean): string {
    if (yearBean.code == GeekDataHelper.DB_RIGHT_NOW) {
      return GeekDataHelper.DB_RIGHT_NOW.toString();
    }
    if (yearBean.code == GeekDataHelper.DB_BEFORE_1990) {
      return GeekDataHelper.DB_BEFORE_1990.toString();
    }

    const sb: StringBuilder = new StringBuilder();
    sb.append(yearBean.code);
    if (monthBean.code < 10) {
      sb.append(0);
    }
    sb.append(monthBean.code);
    return sb.toString();
  }

  static date8ToText(date8: number): string {

    if (date8 == GeekDataHelper.DB_RIGHT_NOW) {
      return GeekDataHelper.VIEW_RIGHT_NOW;
    }

    if (date8 == GeekDataHelper.DB_BEFORE_1990) {
      return GeekDataHelper.VIEW_BEFORE_1990;
    }

    const sb: StringBuilder = new StringBuilder();
    const sDate8: string = date8.toString();
    let year: string = "";
    let month: string = "";
    if (sDate8.length >= 4) {
      year = sDate8.substring(0, 4);
    }

    if (sDate8.length >= 6) {
      month = sDate8.substring(4, 6);
    }

    //服务器存在部分垃圾数据,返回00,做兼容
    if ("00" === month) {
      month = "01";
    }


    if (!TextUtils.isEmpty(year) && !TextUtils.isEmpty(month)) {
      sb.append(year);
      sb.append(".");
      sb.append(month);
    } else {
      sb.append(year);
      sb.append(month);
    }
    return sb.toString();
  }
}

