
import common from '@ohos.app.ability.common'

export class AnalyticsFactory {
  private _action: string = ""
  private params:Map<string, string|number> = new Map()

  private constructor() {
  }

  public static create(): AnalyticsFactory {
    return new AnalyticsFactory()
  }

  public action(action: string): AnalyticsFactory {
    this._action = action
    return this
  }

  public param(key: string, value: number | string) {
    this.params.set(key, value)
    return this
  }

  public build(): void {

  }

  public buildSync(): void {

  }

  private uploadData():void{

  }
}