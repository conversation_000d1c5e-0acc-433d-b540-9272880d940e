import router from '@ohos.router';
import common from '@ohos.app.ability.common';
import { <PERSON><PERSON> } from '../Kernel';
import { IUserService } from '../user/IUserService';
import { ZPEvents } from '@ohos/utils/src/main/ets/constants/ZPEvents';

const STORAGE_KEY = 'SecurityFramework'

export class SecurityFramework {
  private inSecurityFramework: boolean = false

  private constructor() {
    let context = AppStorage.get<common.Context>('context')
    context?.eventHub.on(ZPEvents.Security.OPEN, (url: string) => {
      this.check()
    });

    context?.eventHub.on(ZPEvents.Security.CLOSE, () => {
      if (this.inSecurityFramework) {
        router.back()
      }
    });
  }

  public async check(): Promise<void> {
    let userService: IUserService = Kernel.getInstance().userService()
    const url: string = await userService.security().getSecurityUrl()
    if (url && !this.inSecurityFramework) {
      this.show(url)
    }
  }

  public isInSecurityFramework(): boolean {
    return this.inSecurityFramework
  }

  public onExit() {
    this.inSecurityFramework = false
  }

  private show(url: string): void {
    if (url) {
      let context = AppStorage.get<common.Context>('context')
      if (context) {
        const bundleName: string = context.applicationInfo.name;
        router.pushUrl({
          url: `@bundle:${bundleName}/webview/ets/pages/WebViewPage`,
          params: {
            "url": url,
            "sf": true
          }
        });
        this.inSecurityFramework = true

      }
    }
  }

  public static getInstance(): SecurityFramework {
    if (!AppStorage.get<SecurityFramework>(STORAGE_KEY)) {
      AppStorage.setOrCreate(STORAGE_KEY, new SecurityFramework());
    }
    return AppStorage.get<SecurityFramework>(STORAGE_KEY) as SecurityFramework;
  }
}