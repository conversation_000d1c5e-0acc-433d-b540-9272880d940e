export class ServerUserInfoBean {
  userId: number = 0
  tiny: string = ''
  large: string = ''
  weiXinSecurityUid: string = ''
  name: string = ''
  headImg: number = 0
  weixin: string = ''
  email: string = ''
  gender: number = 0
  rewardHat: string = ''
  isOpenBossProfile: boolean = false
  hometown: number = 0
  hometownName: string = ''
  activeStatus: number = 0
  handicappedPeople: boolean = false
}


export interface GetUserSecurityResponse {
  securityUrl: string
}