import { LevelBean, ServerHighlightListBean } from '@ohos/zp_api'
import { ServerPositionItemBean } from '@ohos/zp_api/src/main/ets/zp/entity/boss/resume/ServerPositionItemBean'
import { ServerProjectBean } from '@ohos/zp_api/src/main/ets/zp/entity/boss/resume/ServerProjectBean'
import { ServerUserInfoBean } from '../CommonModels'


/**
 * geek 数据信息
 */
export namespace GeekInfoHttpModel {

  export class ServerGeekAffiliationBean {
    // Define the properties of ServerGeekAffiliationBean here
  }

  export class ServerQuestionBean {
    // Define the properties of ServerQuestionBean here
  }


  export class ServerWorkBean {
    workId: number = 0

    positon: number = 0 //这里好像不对
    positionName: string = ''

    positionTitle: string = ''

    department: string = ''

    startDate: string = ''

    endDate: string = ''

    company: string = ''

    responsibility: string = ''

    workPerformance: string = ''

    workEmphasis: string = ''

    reportPositionId: number = 0

    industryCode: string = ''

    industryCatgroy: string = ''
  }

  export class ServerEduBean {
    eduId: number = 0

    degree: number = 0

    degreeName: string = ''

    eduType: number = 0

    schoolId: number = 0

    school: string = ''

    major: string = ''

    startDate: string = ''

    endDate: string = ''
    highestDegree: number = 0

    badge: string = ''
  }

  export class ServerProjectBean {
    projectId: number = 0
    name: string = ''
    url: string = ''
    roleName: string = ''
    projectDescription: string = ''
    projDescHighlightList: Array<ServerHighlightListBean> = new Array()
    performance: string = ''
    perfHighlightList: Array<ServerHighlightListBean> = new Array()
    startDate: number = 0
    endDate: number = 0
    suggestToDel: number = 0
    workYearDesc: string = ''

  }


  export class ServerSocialContactBean {
    // Define the properties of ServerSocialContactBean here
  }

  export class ServerVolunteerBean {
    // Define the properties of ServerVolunteerBean here
  }

  export class ServerTrainingBean {
    // Define the properties of ServerTrainingBean here
  }

  export class ServerClubBean {
    // Define the properties of ServerClubBean here
  }

  export class GarbageResumeBean {
    // Define the properties of GarbageResumeBean here
  }

  export class GeekFeature {
    // Define the properties of GeekFeature here
  }


  export class QuestionAnswer {
    // Define the properties of QuestionAnswer here
  }

  export class ServerBlueAdvantageConfigBean {
    // Define the properties of ServerBlueAdvantageConfigBean here
  }

  export class ServerCertificationBean {
    // Define the properties of ServerCertificationBean here
  }

  export class ServerHonorBean {
    // Define the properties of ServerHonorBean here
  }

  @Observed
  export class CodeNameFlagBean {
    // Define the properties of CodeNameFlagBean here
    code: number = 0

    name: string = ''

    flag: number = 0 //// 1-表示工作经历填写职类，加icon
    settingValue: number = 0;

    sort: number = 0;
  }

  export class ServerDesignWorkBean {
    // Define the properties of ServerDesignWorkBean here
  }

  export class ServeGeekMixedExpectBean {
    // Define the properties of ServeGeekMixedExpectBean here
    positionName: string = ''

    positionType: number = 0

    expectId: number = 0

    expectIds: Array<number> = []

    encryptExpectId: string = ''

    location: number = 0

    locationName: string = ''

    f1BannerTitle: string = ''

    f1BannerContent: string = ''

    combinePosition: string = '' // 8.4 蓝白融合，BOSS和店长职类，客户端直接传到F1推荐职位接口即可
    mixExpectType: number = 0 // 混推类型，1:多期望混推，2:店长期望混推，3:大龄牛人混推，4:本地混推，5:晋升混推，6:无障碍求职混推，7灰度人群混推，8:小城市混推, 10:多期望职类筛选混推(1109)，11:本地职类筛选混推(1110)
    // // 新增mixExpectType=12, 可切换混推期望
    subLocation: number = 0 // 816  混推区县code
    subLocationName: string = '' // 816混推区县名称
    // public List<ServeGeekMixedExpectBean> switchableList; //   type == 12时 F1分类推荐列表可切换混推期望列表
    //本地字段，mixExpectType=12, 可切换混推期望
    // public boolean supportSwitch;
  }

  export class ServerListTipCardBean {
    // Define the properties of ServerListTipCardBean here
  }

  export class ServerGeekPictureModuleBean {
    // Define the properties of ServerGeekPictureModuleBean here
  }

  export class CodeAndNameBean {
    // Define the properties of CodeAndNameBean here
  }

  export class ServerPostExperienceBean {
    // Define the properties of ServerPostExperienceBean here
  }

  export class ServerHandicappedInfoBean {
    // Define the properties of ServerHandicappedInfoBean here
  }

  export class ServerExpectBean {
    expectId: number = 0

    position: number = 0

    positionName: string = ''

    positionCategory: string = ''

    positionLv1: number = 0

    positionLv2: number = 0

    positionNameHighlightList: Array<ServerHighlightListBean> = new Array()

    location: number = 0

    locationName: string = ''

    lowSalary: number = 0

    highSalary: number = 0

    // 页面展示薪资
    salaryDesc: string = ''

    industryList: Array<LevelBean> = new Array()

    usingItem: boolean = false

    blueCollarRecommendList: Array<ServerPositionItemBean> = new Array()

    combine  ?: ServerPositionItemBean

    blueCollarExpect: boolean = false

    directionList: Array<string> = new Array()

    suggestPosition: string = ''

    f1CornerTag: string = ''

    geekTrait?: GeekTrait

    positionType: number = 0

    subLocation: number = 0

    subLocationName: string = ''

    /*817 加密期望id*/
    encryptExpectId: string = ''

    //#10.01
    //新增 zpData -> suggestExpect -> sugReason  // 推荐理由
    sugReason: string = ''

    // 新增 zpData -> geekDetail -> expectPositionList -> sugExpect  // 是否推荐期望
    sugExpect: boolean = false

    // #1018
    // 新增 zpData -> geekDetail -> expectInternList -> interestLocationList  // 1018.052学生支持多意愿城市
    interestLocationList: Array<LevelBean> = []

    recommendReason: string = ''

    potentialJobInterest: string = ''

    isGeekPartime: boolean = false
  }

  // TODO: 后期完善
  export class GeekTrait {
  }


  @Observed
  export class ServerMyGeekDetailBean {
    topItemUrl: string = ''

    userInfo?: ServerUserInfoBean

    email: string = ''

    gender: number = 0

    geekAffiliation?: ServerGeekAffiliationBean

    walletAmount: number = 0

    couponCount: number = 0

    couponListUrl: Array<string> = new Array()

    avatarUrl: string = ''

    beanCount: number = 0

    userDescription: string = ''

    professionalSkill: string = ''

    workDate8: number = 0

    degree: number = 0

    degreeCategory: string = ''

    freshGraduate: number = 0

    applyStatus: number = 0

    applyStatusDesc: string = ''

    applyStatusContent: string = ''

    resumeStatus: number = 0

    hasResume: number = 0

    unFreezeEmail: string = ''

    explainUrl: string = ''

    shareUrl: string = ''

    birthday: string = ''

    ageDesc: string = ''

    activeTimeDesc: string = ''

    workEduDesc: string = ''

    couponIntroduce: string = ''

    workYearsDesc: string = ''

    questAnswerUrl: string = ''

    answerCount: number = 0

    geekQuestInfoV2?: ServerQuestionBean

    expectPositionList: Array<ServerExpectBean> = new Array()

    expectInternList: Array<ServerExpectBean> = new Array()

    expectPartTimeList: Array<ServerExpectBean> = new Array()

    workExperienceList: Array<ServerWorkBean> = new Array()

    eduExperienceList: Array<ServerEduBean> = new Array()

    projectExperienceList: Array<ServerProjectBean> = new Array()

    socialContactList: Array<ServerSocialContactBean> = new Array()

    volunteerList: Array<ServerVolunteerBean> = new Array()

    trainingExpList: Array<ServerTrainingBean> = new Array()

    clubExpList: Array<ServerClubBean> = new Array()

    garbage?: GarbageResumeBean

    supportAnnexType: boolean = false

    shareText: string = ''

    bindExpect: string = ''

    showUserDescLabel: boolean = false;

    geekFeature?: GeekFeature

    completeStatus: number = 0

    completeType: number = 0

    doneWorkPositionList: Array<LevelBean> = new Array()

    questionAnswer?: QuestionAnswer

    geekCharacterLabelList: Array<ServerBlueAdvantageConfigBean> = new Array()

    geekSkillLabelList: Array<ServerBlueAdvantageConfigBean> = new Array()

    certificationList: Array<ServerCertificationBean> = new Array()

    honorList: Array<ServerHonorBean> = new Array()

    f1TabsConfig: Array<CodeNameFlagBean> = new Array()

    f1RcdTabConfig?: CodeNameFlagBean

    showDesignWorks: boolean = false

    designWorksList: Array<ServerDesignWorkBean> = new Array()

    showExpectSuggestIcon: boolean = false

    geekMixedExpect?: ServeGeekMixedExpectBean

    geekPartTimeCombineExpect?: ServeGeekMixedExpectBean

    tipCards: Array<ServerListTipCardBean> = new Array()

    designWorksImage?: ServerGeekPictureModuleBean

    designWorksVideo?: ServerGeekPictureModuleBean

    partTimeDirections: Array<CodeAndNameBean> = new Array()

    geekStuCombineExpect?: ServeGeekMixedExpectBean

    postExperienceList: Array<ServerPostExperienceBean> = new Array()

    handicappedInfo?: ServerHandicappedInfoBean
  }

  export class WxNotifySetting {
    wxNotify: boolean = false

    wxNotifyGuide: boolean = false

    notifyStatus: boolean = false

    chatNotifyStatus: number = 0

    interviewNotifyStatus: number = 0

    resumeNotifyStatus: number = 0

    interestNotifyStatus: number = 0

    hunterRecommendNotifyStatus: number = 0
  }

  export class QuickCompTipBean {
    status: number = 0
  }


  @Observed
  export class GeekInfoServiceModel {
    geekDetail?: ServerMyGeekDetailBean

    registryWhiteStyle: number = 0

    wxNotify: boolean = false

    wxNotifyGuide: boolean = false

    wxNotifySetting?: WxNotifySetting

    wxNotifyGrayGuide: boolean = false

    quickCompTip?: QuickCompTipBean
  }

  export interface WorkExpSaveResponse {
    workId: number

    isBlueCollarPosition: boolean
  }

  export interface EduExpUpdateResponse {
    eduId: number

    shareUrl: string

    shareText: string
  }
}

