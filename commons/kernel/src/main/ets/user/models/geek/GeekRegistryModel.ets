/**
 * C端 首善模型
 */
import { CheckUtils } from '@ohos/utils'
import { LevelBean } from '@ohos/zp_api'

export class GeekRegistryModel {
  name: string = ''
  gender: number = 0
  birthday: string = ''
  birthdayYear: number = 0
  birthdayMonth: number = 0
  //身份
  graduate: number = 0

  //职场人----------------------------------------------------------------
  //求职状态0选中的code
  selectedWorkStatusCode: number = -1
  //无障碍求职
  handicappedPeople: boolean = false
  //参加工作时间
  workTime: number = 0
  workTimeYear: number = 0
  workTimeMonth: number = 0
  //工作经历中的职位
  menuCode: number = 0 //一级菜单
  workId : number = 0
  jobCode: number = 0
  jobName: string = ''
  customReportId: number = 0 //自定义
  customReportName: string = '' //自定义
  selectedMenuCode: string = '' //一级菜单
  isBluePosition: boolean = false //是否是蓝领

  //公司名称
  companyName: string = ''
  //工作经历-时间
  jobStartWorkTimeYear: number = 0
  jobStartWorkTimeMonth: number = 0
  jobEndWorkTimeYear: number = 0
  jobEndWorkTimeMonth: number = 0
  //技能标签
  workSkills: string = ''
  customWorkSkills: string = ''
  //工作内容
  workResponsibility: string = ''
  //----------------------------------------------------------------
  //学校
  eduId: number = 0
  school: string = ''
  // 1全日制、2非全日制
  eduType: number = 0
  //学校id
  schoolId: number = 0
  //学历code
  degreeCode: number = 0
  //学历
  degreeName: string = ''
  //专业code
  majorCode: number = 0
  //专业code
  majorName: string = ''
  //入学时间 格式 20200302
  schoolStartDate: number = 0
  //  毕业时间 格式 20200302
  schoolEndDate: number = 0

  //职场人-求职期望
  expectData: GeekLocalExpectData = new GeekLocalExpectData()

  //职场人-求职期望
  studentExpectData: GeekLocalExpectData = new GeekLocalExpectData()

  //个人优势
  advantage: string = ''

  //个人头像
  avatarUrl: string = '' // 头像url
  avatarDefaultIndex: number = -1; // 默认头像索引
}
export class GeekLocalExpectData {
  cityCode: number = 0
  cityName: string = ''
  cities: Array<LevelBean> = []
  positions: Array<LevelBean> = []
  lowSalary: number = 0
  highSalary: number = 0
  parseData(expectPositionList: import("./GeekInfoHttpModel").GeekInfoHttpModel.ServerExpectBean[]) {
    if(CheckUtils.isEmptyArr(expectPositionList)){
      return
    }
    const firstExpect = expectPositionList[0]
    this.cityCode = firstExpect.location
    this.cityName = firstExpect.locationName
    this.lowSalary = firstExpect.lowSalary
    this.highSalary = firstExpect.highSalary
    expectPositionList.forEach(element => {
      this.positions.push(new LevelBean(element.position, element.positionName))
    });
  }
  parseStudentData(expectPositionList: import("./GeekInfoHttpModel").GeekInfoHttpModel.ServerExpectBean[]) {
    if(CheckUtils.isEmptyArr(expectPositionList)){
      return
    }
    const firstExpect = expectPositionList[0]
    this.cityCode = firstExpect.location
    this.cityName = firstExpect.locationName
    this.cities = firstExpect.interestLocationList??[]
    this.lowSalary = firstExpect.lowSalary
    this.highSalary = firstExpect.highSalary
    expectPositionList.forEach(element => {
      this.positions.push(new LevelBean(element.position, element.positionName))
    });
  }
}


