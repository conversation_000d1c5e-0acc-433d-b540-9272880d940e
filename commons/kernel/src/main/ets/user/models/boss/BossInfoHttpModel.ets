import { ServerBrandInfoBean } from '@ohos/zp_api'
import { ServerUserInfoBean } from '../CommonModels'

/**
 * boss 数据信息
 */
export namespace BossInfoHttpModel{
  export class EvaluateStarBean {
    startNum: string = ''
    defaultStarNum: string = ''
  }

  export class ServerUserExtraBean {
    comId: number = 0
    companyName: string = ''
    comName: string = ''
    officeWebsite: string = ''
    description: string = ''
    email: string = ''
    title: string = ''
    comCertificate: number = 0
    showComName: boolean = false
    resumeEmail: string = ''
    titleBubbleWord: string = ''
    titleBubbleLinkWord: string = ''
    titleBubbleUrl: string = ''
    titleStateWord: string = ''
    titleUrl: string = ''
    titleStateDefault: number = 0
    titleBubbleRejectSenior: string = ''
    limitedLevel: number = 0
    limitedDetailUrl: string = ''
    titleAuditStatus: number = 0
  }

  export class ServerBossCertificationBean {
    email: string = ''
    certification: number = 0
  }

  export class ServerHunterInfoBean {
    isHeadhunter: boolean = false
  }

  export class ServerMyBossDetailBean {
    activeTimeDesc: string = ''
    userInfo?: ServerUserInfoBean
    userExtra?: ServerUserExtraBean
    certificationInfo?: ServerBossCertificationBean
    headhunterInfo?: ServerHunterInfoBean
  }

  export class ServerJobBean {
    // Define properties of ServerJobBean here
  }

  export class WorkAddressBean {
    // Define properties of WorkAddressBean here
  }

  export class ServerHighLightDialog {
    // Define properties of ServerHighLightDialog here
  }

  export class WxNotifySetting {
    // Define properties of WxNotifySetting here
  }


  @Observed
  export class BossInfoServiceModel {
    topItemUrl: string = ''
    weixin: string = ''
    walletAmount: number = 0
    couponCount: number = 0
    couponListUrl: string[] = []
    beanCount: number = 0
    walletTitle: string = ''
    lureKeywords: string = ''
    linkout: string = ''
    contactGeekCount: number = 0
    certifyUrl: string = ''
    vipLevel: number = 0
    vipStatus: number = 0
    vipPayUrl: string = ''
    vipEndDate: string = ''
    avatarUrl: string = ''
    markType: number = 0
    hasPassword: boolean = false
    companyActiveUrl: string = ''
    h5Url: string = ''
    interviewCount: number = 0
    unusedItemCount: number = 0
    favourGeekCount: number = 0
    influenceCount: number = 0
    influenceUrl: string = ''
    couponIntroduce: string = ''
    medalUrl: string = ''
    bindWeiXin: boolean = false
    openId: string = ''
    wxNickname: string = ''
    registryWhiteStyle: number = 0
    wxNotify: boolean = false
    wxNotifyGuide: boolean = false
    showRedPoint: number = 0
    showWorkAddress: number = 0
    showRecruitmentBar: boolean = false
    showGroupLure: boolean = false
    showMode: number = 0
    isRecruit: boolean = false
    avatarStickerUrl: string = ''
    avatarStickerDesc: string = ''
    delayDeadLine: number = 0
    intermediaryIdentity: number = 0
    ySalaryGray: number = 0
    licenseManage: string = ''
    innerJobUpdateIn2Phase: boolean = false
    salarySwitchVal: number = 0
    proxyBoss: boolean = false
    bossGolderIdentity: number = 0
    optimizationType: number = 0
    brand?: ServerBrandInfoBean;
    userDetail?: ServerMyBossDetailBean;
    delayDeadLineDialog?: ServerHighLightDialog;
    wxNotifySetting?: WxNotifySetting;
    evaluationStar?: EvaluateStarBean;
    workAddress?: WorkAddressBean;
    jobDetail?: ServerJobBean[];
  }

}
