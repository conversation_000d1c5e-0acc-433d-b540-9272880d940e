import { URLConfig } from '@ohos/http';
import { BATCH_SYNC, POST_SYNC } from '@ohos/http/src/main/ets/axios/service';
import { ZPBatchParams } from '@ohos/http/src/main/ets/axios/bean/ReqParams';
import { isSuccess, SuccessResponse } from '@ohos/http/src/main/ets/axios/bean/HttpResponse';
import { Logger, ZPObj, ZPStore } from '@ohos/utils';
import TextUtils from '@ohos/http/src/main/ets/utils/TextUtils';
import { AbsSubService } from './service/AbsSubService';
import List from '@ohos.util.List';
import { AccountLifecycleCallback } from '../service/AccountService';
import { Account } from '../account/Account';
import { IUserService } from './IUserService';
import { ZPRole } from '../constants/ZPRole';
import { <PERSON><PERSON> } from '../Kernel';
import { getUserInfoKey, UserInfoConstants } from './constants/UserInfoConstants';
import BossInfoService from './service/subservice/BossInfoService';
import GeekInfoService from './service/subservice/GeekInfoService';
import { BossInfoServiceImpl } from './service/subservice/BossInfoServiceImpl';
import { GeekInfoServiceImpl } from './service/subservice/GeekInfoServiceImpl';
import { LoginError } from '../exception/LoginError';
import SecurityService from './service/subservice/SecurityService';
import { SecurityServiceImpl } from './service/subservice/SecurityServiceImpl';


const TAG: string = '[UserService]';


export class UserServiceImpl implements IUserService, AccountLifecycleCallback {
  private subService: Map<number, List<AbsSubService<object>>> = new Map<number, List<AbsSubService<object>>>()
  bossInfoService: BossInfoService
  geekInfoService: GeekInfoService
  securityService: SecurityService

  constructor(kernel: Kernel) {
    kernel.registerAccountLifecycleCallbacks([this])
    //geek
    this.geekInfoService = new GeekInfoServiceImpl(kernel, this)

    //boss
    this.bossInfoService = new BossInfoServiceImpl(kernel, this)

    //安全
    this.securityService = new SecurityServiceImpl(kernel, this)
  }


  private getSubServiceList(zpRole: ZPRole): List<AbsSubService<object>> {
    let list = this.subService.get(zpRole.valueOf())
    if (list == null) {
      list = new List<AbsSubService<object>>()
      this.subService.set(zpRole.valueOf(), list);
    }
    return list
  }

  /**
   * 添加子服务
   * @param zpRole
   * @param subService
   */
  addSubService<T>(zpRole: ZPRole, subService: AbsSubService<T>) {
    const list = this.getSubServiceList(zpRole)
    list.add(subService as AbsSubService<Object>)
  }

  onAccountInit(account: Account): void {
    //nothing
  }

  async onAccountRelease(account: Account) {
    //nothing
    await this.logout(account)
  }

  async onIdentityInit(identity: number) {
    // throw new LoginError('未登录');
    await this.syncUserInfo()
  }

  async onIdentityRelease(identity: number) {
    await this.clearRoleInfo(identity)
  }

  /**
   * 同步用户数据
   * @returns true 成功 false 失败
   */
  async syncUserInfo(): Promise<boolean> {
    if (!Kernel.getInstance().isLoggedIn()) {
      Logger.info(TAG, "sync not login")
      return false
    }
    const account = Kernel.getInstance().getAccount()

    if (account == null) {
      Logger.info(TAG, "sync account  is empty")
      return false
    }

    const identity = account.identity
    const saveKey = getUserInfoKey(account.uid, identity)

    switch (identity) {
      case ZPRole.GEEK.valueOf():
        const geekResponse = await BATCH_SYNC<Record<string, Object>>(
          [
            ZPBatchParams.obj(URLConfig.URL_ZPGEEK_APP_F3_TIP_QUERY),
            ZPBatchParams.obj(URLConfig.URL_ZPGEEK_APP_GEEK_EXPECTPOSITION_SUGGEST_REDDOT_QUERY),
            ZPBatchParams.obj(URLConfig.URL_GET_USER_GEEK_DETAIL),
            ZPBatchParams.obj(URLConfig.URL_GET_USER_DYNAMIC_BAR),
            ZPBatchParams.obj(URLConfig.URL_GET_USER_BOTTOM_BTNS),
            ZPBatchParams.obj(URLConfig.URL_GEEK_INTERVIEW_FIT_COUNT),
            ZPBatchParams.obj(URLConfig.URL_GET_SECURITY_URL),
            ZPBatchParams.obj(URLConfig.URL_USER_INFO_CHECK),
            ZPBatchParams.obj(URLConfig.URL_GEEK_PANEL_QUERY),
            ZPBatchParams.obj(URLConfig.URL_ZPGEEK_RESUME_SUGGEST_F3_CARD_LIST),
            ZPBatchParams.obj(URLConfig.URL_GEEK_GET_ITEM_MALL_F4),
            ZPBatchParams.obj(URLConfig.URL_ZPITEM_GEEK_VIP_INFO),
            ZPBatchParams.obj(URLConfig.URL_GUIDE_RESUME_REFRESH),
            ZPBatchParams.obj(URLConfig.URL_ZPGEEK_APP_IDENTITY_SWITCH_ENTRANCE),
            ZPBatchParams.obj(URLConfig.URL_STUDENT_FEATRUE),
          ]
        )
        if (!isSuccess(geekResponse.code)) {
          Logger.debug(TAG, 'request http geek error code = ' + geekResponse.code + " : message =" + geekResponse.message)
          return false
        }
        const geekData = geekResponse.zpData
        if (geekData == null) {
          Logger.debug(TAG, 'request http geek error data = null')
          return false
        }

        const geekRecord: Record<string, Object> = {};

        const geekSubService = this.getSubServiceList(ZPRole.GEEK)

        geekSubService.forEach((service) => {
          const model = service.parseHttp(geekData)
          geekRecord[service.httpResponseKey()] = model
        })

        this.getSubServiceList(ZPRole.All).forEach((service) => {
          const model = service.parseHttp(geekData)
          geekRecord[service.httpResponseKey()] = model
        })

        return await ZPStore.setValueMap(saveKey, geekRecord)

      case ZPRole.BOSS.valueOf():
        const bossResponse = await BATCH_SYNC<Record<string, Object>>(
          [
            ZPBatchParams.obj(URLConfig.URL_GET_BOSS_JOB_LIST),
            ZPBatchParams.obj(URLConfig.URL_GET_BOSS_DETAIL),
            ZPBatchParams.obj(URLConfig.URL_GET_USER_DYNAMIC_BAR),
            ZPBatchParams.obj(URLConfig.URL_GET_USER_BOTTOM_BTNS),
            ZPBatchParams.obj(URLConfig.URL_GET_BOSS_JOB_SORT_LIST),
            ZPBatchParams.obj(URLConfig.URL_USER_QUICK_REPLY),
            ZPBatchParams.obj(URLConfig.URL_GET_USER_F1_PAGE_GUIDE),
            ZPBatchParams.obj(URLConfig.URL_GET_SECURITY_URL),
            ZPBatchParams.obj(URLConfig.URL_ZPITEM_BOSS_GET_ITEM_MALL_F_3),
            ZPBatchParams.obj(URLConfig.URL_USER_UNPAID_ORDER_LIST),
            ZPBatchParams.obj(URLConfig.URL_APP_USER_RECHARGE_GUIDE),
            ZPBatchParams.obj(URLConfig.URL_ZPITEM_DELIVER_SCHEDULE),
          ]
        )
        if (!isSuccess(bossResponse.code)) {
          Logger.debug(TAG, 'request http boss error code = ' + bossResponse.code + " : message =" + bossResponse.message)
          return false
        }
        const bossData = bossResponse.zpData
        if (bossData == null) {
          Logger.debug(TAG, 'request http error boss data = null')
          return false
        }

        const bossRecord: Record<string, Object> = {};

        const bossSubService = this.getSubServiceList(ZPRole.BOSS)

        bossSubService.forEach((service) => {
          const model = service.parseHttp(bossData)
          bossRecord[service.httpResponseKey()] = model
        })

        this.getSubServiceList(ZPRole.All).forEach((service) => {
          const model = service.parseHttp(bossData)
          bossRecord[service.httpResponseKey()] = model
        })

        return await ZPStore.setValueMap(saveKey, bossRecord)
      default:
        Logger.info(TAG, "sync default error")
        return false
    }
  }

  private async logout(account: Account) {
    try {
      await POST_SYNC<SuccessResponse>(URLConfig.URL_LOGOUT,
        ZPObj.objMap<string, string>()
          .build()
      )
    } catch (exception) {
      Logger.error(TAG, "logout error" + exception)
    }

  }

  /**
   * 清空全部数据
   * @returns true 成功 false 失败
   */
  async clearRoleInfo(identity: number): Promise<boolean> {

    const clearListKey = new List<string>()
    this.subService.get(identity)?.forEach((service) => {
      clearListKey.add(service.httpResponseKey())
    })

    const result = await ZPStore.deleteValueList(clearListKey.convertToArray())

    Logger.info(TAG, "clearUserInfo successful result :" + result)
    return result
  }

  /**
   * 检查 用户数据是否完善
   * @returns
   */
  async checkInfoComplete(): Promise<boolean> {
    const account = Kernel.getInstance().getAccount()
    if (account == null) {
      Logger.info(TAG, "account is null")
      return false
    }

    switch (account.identity) {
      case ZPRole.GEEK.valueOf():
        const geekInfo = await this.geek().get()

        const geekUserInfo = geekInfo?.geekDetail?.userInfo

        if (geekUserInfo == null) {
          Logger.info(TAG, "牛人信息不完整")
          return false
        }
        if (TextUtils.isEmpty(geekUserInfo.tiny) && (geekUserInfo.headImg <= 0 || geekUserInfo.headImg > 16)) {
          Logger.info(TAG, "头像信息不完整")
          return false
        }

        if (TextUtils.isEmpty(geekUserInfo.name)) {
          Logger.info(TAG, "用户名不完整")
          return false
        }
        if (geekUserInfo.gender < 0 || geekUserInfo.gender > 2) {
          Logger.info(TAG, "性别信息不完整")
          return false
        }

        const graduate = geekInfo?.geekDetail?.freshGraduate

        if (graduate == UserInfoConstants.FRESH_GRADUATE_FIND_JOB
          || graduate == UserInfoConstants.NONE_WORK_EXPERIENCE
          || graduate == UserInfoConstants.AT_SCHOOL_INTERN
          || graduate == UserInfoConstants.SOCIAL_RECRUITMENT
          || graduate == UserInfoConstants.STUDENT_RECORD) {

          const eduExperienceList = geekInfo?.geekDetail?.eduExperienceList ?? []
          if (eduExperienceList == null || eduExperienceList.length < 1) {
            Logger.info(TAG, "牛人教育经历不完整")
            return false;
          }
          if (graduate == UserInfoConstants.SOCIAL_RECRUITMENT) {
            const workExperienceList = geekInfo?.geekDetail?.workExperienceList ?? []
            if (workExperienceList == null || workExperienceList.length < 1) {
              Logger.info(TAG, "牛人工作经历不完整")
              return false;
            }
          }
        } else {
          Logger.info(TAG, "是否为应届生信息不正确")
          return false;
        }


        if (TextUtils.isEmpty(geekInfo?.geekDetail?.userDescription)) {
          Logger.info(TAG, "牛人优势不完整")
          return false
        }

        return true
      case ZPRole.BOSS.valueOf():
        const bossInfo = await this.boss().get()

        const bossUserInfo = bossInfo?.userDetail?.userInfo
        const bossUserInfoExtra = bossInfo?.userDetail?.userExtra

        if (bossUserInfo == null || bossUserInfoExtra == null) {
          Logger.info(TAG, "Boss信息不完整")
          return false
        }
        if (TextUtils.isEmpty(bossUserInfo.tiny) && (bossUserInfo.headImg <= 0 || bossUserInfo.headImg > 16)) {
          Logger.info(TAG, "头像信息不完整")
          return false
        }
        if (TextUtils.isEmpty(bossUserInfo.name)) {
          Logger.info(TAG, "用户名不完整")
          return false
        }

        if (TextUtils.isEmpty(bossUserInfoExtra.title)) {
          Logger.info(TAG, "我的职位不完整")
          return false
        }

        if (TextUtils.isEmpty(bossUserInfoExtra.comName)) {
          Logger.info(TAG, "公司全称不完整")
          return false
        }

        if (bossInfo?.brand == null) {
          Logger.info(TAG, "品牌不完整")
          return false
        }
        Logger.info(TAG, "checkInfoComplete finish is true")
        return true
      default:
        Logger.info(TAG, "checkInfoComplete error")
        return false
    }
  }

  boss(): BossInfoService {
    return this.bossInfoService
  }

  geek(): GeekInfoService {
    return this.geekInfoService
  }


  security(): SecurityService {
    return this.securityService
  }

}

