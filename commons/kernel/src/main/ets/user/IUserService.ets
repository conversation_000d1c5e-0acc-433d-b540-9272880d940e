import BossInfoService from './service/subservice/BossInfoService';
import GeekInfoService from './service/subservice/GeekInfoService';
import SecurityService from './service/subservice/SecurityService';

export interface IUserService {
  /**
   * 检查是否已完善信息
   * @returns true 完成 false 未完成
   */
  checkInfoComplete(): Promise<boolean>

  /**
   * boss 数据
   * @returns  IBossInfoService
   */
  boss(): BossInfoService


  /**
   * geek 数据
   * @returns  IBossInfoService
   */
  geek(): GeekInfoService

  /**
   * 安全
   * @returns  SecurityService
   */
  security() : SecurityService

  /**
   * 同步用户信息
   * @returns
   */
  syncUserInfo(): Promise<boolean>
}