import { IUserSubService } from '../IUserSubService';
import { GeekInfoHttpModel } from '../../models/geek/GeekInfoHttpModel';
import { GeekRegistryModel } from '../../models/geek/GeekRegistryModel';
import { LevelBean } from '@ohos/zp_api';


export interface GeekInfoService extends IUserSubService<GeekInfoHttpModel.GeekInfoServiceModel> {

  getGeekDetail(): Promise<GeekInfoHttpModel.ServerMyGeekDetailBean | null>

  /**
   * 获取首善模型
   * @returns
   */
  getRegistryModel(): Promise<GeekRegistryModel | null>

  /**
   * 是否完成工作经历
   * @returns
   */
  isCompletedWork(): Promise<boolean>

  /**
   * 是否完成教育经历
   * @returns
   */
  isCompletedEducation(): Promise<boolean>

  /**
   * 获取期望列表
   */
  getGeekExpectList(positionType: number): Promise<Array<GeekInfoHttpModel.ServerExpectBean>>

  getGeekFulltimeExpectList(): Promise<Array<GeekInfoHttpModel.ServerExpectBean>>

  getGeekPartTimeExpectList(): Promise<Array<GeekInfoHttpModel.ServerExpectBean>>

  getGeekPartimeTabExpect(): Promise<GeekInfoHttpModel.ServerExpectBean | undefined>

  getStuExpectTypes(filter:boolean): Promise<Array<GeekInfoHttpModel.CodeNameFlagBean>>

  getStuExpectList(): Promise<Array<GeekInfoHttpModel.ServerExpectBean>>

  /**
   * 更新用户基础数据
   * @param name
   * @param gender
   * @param year
   * @param month
   * @returns true 成功 false 失败
   */
  updateUserInfo(name: string, gender: number, year: number, month: number): Promise<boolean>

  /**
   * 更新用户求职身份
   * @param graduate 身份
   * @param selectedWorkStatusCode 选中求职状态
   * @param handicappedPeople 是否是无障碍求职
   * @returns
   */
  updateRole(graduate: number, selectedWorkStatusCode: number, handicappedPeople: boolean, workTime: number): Promise<boolean>

  /**
   * 更新工作时间
   * @param workTimeYear 年
   * @param workTimeMonth 月
   * @returns
   */
  updateWorkTime(workTime: number): Promise<boolean>

  /**
   * 更新工作经历
   * @param jobCode id
   * @param jobName 昵称
   * @param customReportId 自定义id
   * @returns
   */
  updateWork(menuCode: number, jobCode: number, jobName: string, customReportId: number, customReportName: string, isBlueCollarPosition: boolean): Promise<boolean>

  /**
   * 更新公司名称
   * @param name 名称
   * @returns
   */
  updateCompanyName(name: string): Promise<boolean>

  /**
   * 入职时间
   * @returns
   */
  updateJobStartWorkTime(startWorkTimeYear: number, startWorkTimeMonth: number, endWorkTimeYear: number, endWorkTimeMonth: number): Promise<boolean>

  /**
   * 技能标签
   * @param workSkills
   * @returns
   */
  updateWorkSkills(workSkills: string, customWorkSkills: string): Promise<boolean>

  /**
   * 更新工作内容
   * @param workSkills
   * @returns
   */
  updateWorkResponsibility(workResponsibility: string): Promise<boolean>

  /**
   * 更新学历
   * @param workSkills
   * @returns
   */
  updateDegreeCode(degreeCode: number, eduType: number): Promise<boolean>

  /**
   * 更新学校
   * @param workSkills
   * @returns
   */
  updateSchool(name: string, code: number): Promise<boolean>

  /**
   * 更新专业
   * @param workSkills
   * @returns
   */
  updateMajor(name: string, code: number): Promise<boolean>

  /**
   * 更新毕业时间
   * @param workSkills
   * @returns
   */
  updateSchoolTime(startTime: number, endTime: number): Promise<boolean>

  /**
   * 职场人-更新求职期望(期望城市，期望职位)
   * @param cities,positions
   * @returns
   */
  updateExpectData(cities: Array<LevelBean>, positions: Array<LevelBean>): Promise<boolean>

  /**
   * 职场人-更新期望薪资(期望城市，期望职位)
   * @param lowSalary,highSalary
   * @returns
   */
  updateExpectSalary(lowSalary: number, highSalary: number): Promise<boolean>

  /**
   * 学生-更新求职期望-期望职位
   * @param positions
   * @returns
   */
  updateStudentExpectWork(positions: Array<LevelBean>): Promise<boolean>

  /**
   * 学生-更新期望(期望城市，期望薪资)
   */
  updateStudentExpectData(cities: Array<LevelBean>, lowSalary: number, highSalary: number): Promise<boolean>

  /**
   * 更新个人优势
   * @param advantage
   * @returns
   */
  updateExpectAdvantage(advantage: string): Promise<boolean>

  /**
   * 更新个人头像
   * @param advantage
   * @returns
   */
  updateExpectAvatar(avatarUrl: string, avatarDefaultIndex: number): Promise<boolean>

  /**
   * 更新求职状态
   * @param selectedWorkStatusCode
   * @returns
   */
  updateWorkStatus(selectedWorkStatusCode: number): Promise<boolean>

  /**
   * 更新头像
   */
  updateAvatar(tinyAvatar: string, largeAvatar: string): Promise<boolean>
  /**
   * 更新名字
   */
  updateName(name: string): Promise<boolean>
  /**
   * 更新性别
   */
  updateUserGender(gender: number): Promise<boolean>

}

export default GeekInfoService;