import { ZPRole } from '../../../constants/ZPRole';
import { AbsSubService } from '../AbsSubService';
import GeekInfoService from './GeekInfoService';
import { GeekInfoHttpModel } from '../../models/geek/GeekInfoHttpModel';
import { GeekRegistryModel, Kernel } from '../../../../../../Index';
import { GeekDataHelper } from '../../../utils/GeekDataHelper';
import { CheckUtils, TextUtils, ToastUtils, ZPObj, ZPStore } from '@ohos/utils';
import { localCodeKey, UserInfoConstants } from '../../constants/UserInfoConstants';
import { LevelBean } from '@ohos/zp_api';
import { GET_SYNC, POST_SYNC } from '@ohos/http/src/main/ets/axios/service';
import { isSuccess, SuccessResponse } from '@ohos/http/src/main/ets/axios/bean/HttpResponse';
import { URLConfig } from '@ohos/http';
import { GeekLocalExpectData } from '../../models/geek/GeekRegistryModel';
import { CompleteStepForwardResponse } from '../../http/CompleteStepForwardResponse';


export class GeekInfoServiceImpl extends AbsSubService<GeekInfoHttpModel.GeekInfoServiceModel> implements GeekInfoService {
  httpResponseKey(): string {
    return "zpgeek.cvapp.geek.baseinfo.query"
  }

  role(): ZPRole {
    return ZPRole.GEEK
  }

  async getGeekDetail(): Promise<GeekInfoHttpModel.ServerMyGeekDetailBean | null> {
    const model = await this.get()

    const detail = model?.geekDetail

    return detail ?? null
  }


  async getRegistryModel(): Promise<GeekRegistryModel | null> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return null
    }
    const userInfo = detail?.userInfo

    if (userInfo == null) {
      return null
    }
    const resultModel = new GeekRegistryModel()

    resultModel.name = userInfo.name
    resultModel.gender = detail.gender
    resultModel.birthday = detail.birthday
    resultModel.birthdayYear = GeekDataHelper.parseYear(detail.birthday)
    resultModel.birthdayMonth = GeekDataHelper.parseMonth(detail.birthday)

    //身份
    resultModel.graduate = detail.freshGraduate == -1 ? UserInfoConstants.SOCIAL_RECRUITMENT : detail.freshGraduate
    //求职状态 - 选中的code
    resultModel.selectedWorkStatusCode = detail.applyStatus
    //无障碍求职
    resultModel.handicappedPeople = userInfo.handicappedPeople

    //参加工作时间
    resultModel.workTime = detail.workDate8
    resultModel.workTimeYear = GeekDataHelper.parseYear(detail.workDate8.toString())
    resultModel.workTimeMonth = GeekDataHelper.parseMonth(detail.workDate8.toString())

    //工作经历
    resultModel.menuCode = await ZPStore.getValue<number>(localCodeKey(account.uid, "menuCode")) ?? 0

    if (detail.workExperienceList.length > 0) {
      //TODO 这里我看boss是 只取第一个
      const work = detail.workExperienceList[0]

      resultModel.workId = work.workId
      resultModel.jobCode = work.positon
      resultModel.jobName = work.positionName

      resultModel.customReportId = work.reportPositionId ?? 0
      resultModel.customReportName = await ZPStore.getValue<string>(localCodeKey(account.uid, "customReportName")) ?? ''

      resultModel.isBluePosition = await ZPStore.getValue<boolean>(localCodeKey(account.uid, "isBlueCollarPosition")) ?? false

      //公司名称
      resultModel.companyName = work.company ?? ''

      //工作时间
      resultModel.jobStartWorkTimeYear = GeekDataHelper.parseYear(work.startDate ?? '')
      resultModel.jobStartWorkTimeMonth = GeekDataHelper.parseMonth(work.startDate ?? '')
      resultModel.jobEndWorkTimeYear = TextUtils.isEmpty(work.endDate) ? -1 : GeekDataHelper.parseYear(work.endDate ?? '')
      resultModel.jobEndWorkTimeMonth = GeekDataHelper.parseMonth(work.endDate ?? '')

      //技能标签
      resultModel.workSkills = work.workEmphasis ?? ''
      resultModel.customWorkSkills = await ZPStore.getValue<string>(localCodeKey(account.uid, "customWorkSkills")) ?? ''

      //工作内容
      resultModel.workResponsibility = work.responsibility
    } else if (detail.doneWorkPositionList != null && detail.doneWorkPositionList.length > 0) {
      const doneWork = detail.doneWorkPositionList[0]
      resultModel.jobCode = doneWork.code
      resultModel.jobName = doneWork.name
    }

    if (detail.eduExperienceList.length > 0) {
      const edu = detail.eduExperienceList[0]
      resultModel.eduId = edu.eduId ?? 0

      //最高学历
      resultModel.degreeCode = edu.degree ?? 0
      resultModel.degreeName = edu.degreeName ?? ''
      //最高学历类型
      resultModel.eduType = edu.eduType ?? 0

      //学校
      resultModel.school = edu.school ?? ''
      resultModel.schoolId = edu.schoolId ?? 0

      //专业
      resultModel.majorName = edu.major ?? ''
      resultModel.majorCode = await ZPStore.getValue<number>(localCodeKey(account.uid, "majorCode")) ?? 0

      //毕业时间
      resultModel.schoolStartDate = Number.parseInt(edu.startDate)
      resultModel.schoolEndDate = Number.parseInt(edu.endDate)
    }

    //职场人期望
    const expectData = new GeekLocalExpectData()
    expectData.parseData(detail.expectPositionList)
    resultModel.expectData = expectData
    //学生期望
    const studentExpectData = new GeekLocalExpectData()
    studentExpectData.parseStudentData(detail.expectInternList)
    resultModel.studentExpectData = studentExpectData

    //个人优势
    resultModel.advantage = detail.userDescription
    //个人头像
    resultModel.avatarUrl = detail.avatarUrl
    resultModel.avatarDefaultIndex = await ZPStore.getValue<number>(localCodeKey(account.uid, "avatarDefaultIndex")) ?? -1

    return resultModel
  }

  async isCompletedWork(): Promise<boolean> {
    const account = Kernel.getInstance().getAccount()

    if (account == null) {
      return false
    }
    return await ZPStore.getValue<boolean>(localCodeKey(account.uid, "isCompletedWork")) ?? false
  }

  async isCompletedEducation(): Promise<boolean> {
    const account = Kernel.getInstance().getAccount()

    if (account == null) {
      return false
    }

    return await ZPStore.getValue<boolean>(localCodeKey(account.uid, "isCompletedEducation")) ?? false
  }


  public static readonly POSITION_TYPE_FULLTIME: number = 0

  public static readonly POSITION_TYPE_PARTIME: number = 1

  /**
   * 获取职场人全职期望列表
   * */
  async getGeekFulltimeExpectList(): Promise<Array<GeekInfoHttpModel.ServerExpectBean>> {
    return this.getGeekExpectList(GeekInfoServiceImpl.POSITION_TYPE_FULLTIME)
  }

  /**
   * 获取职场人兼职期望列表
   * */
  async getGeekPartTimeExpectList(): Promise<Array<GeekInfoHttpModel.ServerExpectBean>> {
    return this.getGeekExpectList(GeekInfoServiceImpl.POSITION_TYPE_PARTIME)
  }

  async getGeekExpectList(positionType: number): Promise<Array<GeekInfoHttpModel.ServerExpectBean>> {
    const model = await Kernel.getInstance().userService().geek().getGeekDetail()
    let list = Array<GeekInfoHttpModel.ServerExpectBean>()
    if (model == null) {
      return list
    }
    const graduate = model.freshGraduate ?? 0

    if (graduate == UserInfoConstants.SOCIAL_RECRUITMENT) {
      let expectlist = model.expectPositionList
      if (!CheckUtils.isEmptyArr(expectlist)) {
        expectlist.forEach((item: GeekInfoHttpModel.ServerExpectBean) => {
          if (item.positionType == positionType) {
            list.push(item)
          }
        })
      }
    }
    return list
  }


  /*获取职场人兼职期望*/
  async getGeekPartimeTabExpect(): Promise<GeekInfoHttpModel.ServerExpectBean | undefined> {


    const model = await Kernel.getInstance().userService().geek().getGeekDetail()
    if (model) {
      const partimeExpect = model.geekPartTimeCombineExpect
      if (partimeExpect) {
        return this.convertToExpect(partimeExpect, true)
      }
    }
    return undefined
  }

  convertToExpect(mixedExpectBean: GeekInfoHttpModel.ServeGeekMixedExpectBean, isPartime: boolean): GeekInfoHttpModel.ServerExpectBean | undefined {

    if (mixedExpectBean) {
      let jobIntentBean: GeekInfoHttpModel.ServerExpectBean = new GeekInfoHttpModel.ServerExpectBean()
      jobIntentBean.expectId = mixedExpectBean.expectId;
      jobIntentBean.encryptExpectId = mixedExpectBean.encryptExpectId;
      jobIntentBean.locationName = mixedExpectBean.locationName;
      jobIntentBean.positionName = mixedExpectBean.positionName;
      jobIntentBean.positionType = mixedExpectBean.positionType;
      jobIntentBean.location = mixedExpectBean.location;
      jobIntentBean.positionType = mixedExpectBean.positionType;
      jobIntentBean.isGeekPartime = true;

      return jobIntentBean;
    }
    return undefined;
  }


  /*获取学生期望类型 - 全职/兼职/实习*/
  async getStuExpectTypes(filter: boolean): Promise<Array<GeekInfoHttpModel.CodeNameFlagBean>> {

    let list = Array<GeekInfoHttpModel.CodeNameFlagBean>()
    const model = await Kernel.getInstance().userService().geek().getGeekDetail()
    if (model) {
      const tabList = model.f1TabsConfig
      if (!CheckUtils.isEmptyArr(tabList)) {
        tabList.forEach((tabItem: GeekInfoHttpModel.CodeNameFlagBean) => {

          if (filter) {
            if (tabItem.flag == 1) {
              list.push(tabItem)
            }
          } else {
            list.push(tabItem)
          }

        })
      }
    }
    return list
  }


  async getStuExpectList(): Promise<Array<GeekInfoHttpModel.ServerExpectBean>> {
    const model = await Kernel.getInstance().userService().geek().getGeekDetail()
    let list = Array<GeekInfoHttpModel.ServerExpectBean>()
    if (model == null) {
      return list
    }
    const graduate = model.freshGraduate ?? 0

    if (graduate == UserInfoConstants.STUDENT_RECORD) {
      let expectlist = model.expectInternList
      if (!CheckUtils.isEmptyArr(expectlist)) {
        expectlist.forEach((item: GeekInfoHttpModel.ServerExpectBean) => {
          list.push(item)
        })
      }
    }
    return list
  }


  async updateUserInfo(name: string, gender: number, year: number, month: number): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail

    if (detail == null) {
      return false
    }
    const userInfo = detail?.userInfo

    if (userInfo == null) {
      return false
    }

    const birthday = GeekDataHelper.getDateForText(year, month)

    const response = await POST_SYNC<SuccessResponse>(URLConfig.URL_GEEK_ADD_BASE_INFO,
      ZPObj.objMap<string, string>()
        .set("name", name)
        .set("gender", gender.toString())
        .set("birthday", birthday)
        .build()
    )
    if (!isSuccess(response.code)) {
      ToastUtils.showToast(response.message ?? '')
      return false
    }

    userInfo.name = name
    detail.gender = gender
    detail.birthday = birthday


    return await this.update(model)
  }

  async updateUserGender( gender: number ): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    if (detail == null) {
      return false
    }
    detail.gender = gender
    return await this.update(model)
  }

  async updateRole(graduate: number, selectedWorkStatusCode: number, handicappedPeople: boolean, workTime: number): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }
    const userInfo = detail?.userInfo

    if (userInfo == null) {
      return false
    }


    const requestMap = ZPObj.objMap<string, string>()
      .set("freshGraduate", graduate.toString())
      .set("applyStatus", selectedWorkStatusCode.toString())
      .set("handicapped", handicappedPeople ? '2' : '1')

    if (workTime > 0) {
      requestMap.set("workDate8", workTime.toString())
    }
    const response = await POST_SYNC<SuccessResponse>(URLConfig.URL_GEEK_UPDATE_BASE_INFO, requestMap.build())

    if (!isSuccess(response.code)) {
      ToastUtils.showToast(response.message ?? '')
      return false
    }

    //身份
    detail.freshGraduate = graduate
    //求职状态 - 选中的code
    detail.applyStatus = selectedWorkStatusCode
    //无障碍求职
    userInfo.handicappedPeople = handicappedPeople
    //时间
    detail.workDate8 = workTime

    return await this.update(model)
  }

  //更新求职状态
  async updateWorkStatus(selectedWorkStatusCode: number): Promise<boolean> {

    const requestMap = ZPObj.objMap<string, string>()
      .set("applyStatus", selectedWorkStatusCode.toString())

    const response = await POST_SYNC<SuccessResponse>(URLConfig.URL_GEEK_UPDATE_BASE_INFO, requestMap.build())

    if (!isSuccess(response.code)) {
      ToastUtils.showToast(response.message ?? '')
      return false
    }
    const model = await this.get()
    const detail = model?.geekDetail
    if (detail == null) {
      return false
    }
    detail.applyStatus = selectedWorkStatusCode
    return await this.update(model)
  }

  async updateName(name: string): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }


    const response = await POST_SYNC<SuccessResponse>(URLConfig.URL_GEEK_UPDATE_BASE_INFO,
      ZPObj.objMap<string, string>()
        .set("name", name)
        .build())

    if (!isSuccess(response.code)) {
      ToastUtils.showToast(response.message ?? '')
      return false
    }
    if(detail.userInfo){
      detail.userInfo.name = name
    }
    return await this.update(model)
  }

  async updateWorkTime(workTime: number): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }


    const response = await POST_SYNC<SuccessResponse>(URLConfig.URL_GEEK_UPDATE_BASE_INFO,
      ZPObj.objMap<string, string>()
        .set("workDate8", workTime.toString())
        .build())

    if (!isSuccess(response.code)) {
      ToastUtils.showToast(response.message ?? '')
      return false
    }


    detail.workDate8 = workTime


    return await this.update(model)
  }

  async updateWork(menuCode: number, jobCode: number, jobName: string, customReportId: number, customReportName: string, isBlueCollarPosition: boolean): Promise<boolean> {

    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }

    if (account == null) {
      return false
    }

    const work = detail.workExperienceList.length > 0 ? detail.workExperienceList[0] : new GeekInfoHttpModel.ServerWorkBean()

    if (work.positon != jobCode) {
      work.workEmphasis = ''
    }

    await ZPStore.setValue(localCodeKey(account.uid, "menuCode"), menuCode)

    work.positon = jobCode
    work.positionName = jobName

    work.reportPositionId = customReportId


    //如果是蓝领，走简单流程，通过接口判断是否可以跳过，如果可以跳过 调用接口更新，并走简化流程
    if (isBlueCollarPosition) {
      const checkResponse = await GET_SYNC<CompleteStepForwardResponse>(URLConfig.URL_ZPGEEK_APP_COMPLETE_STEPFORWARD, {
        step: 2, // 2-工作经历 3-教育经历 5-个人优势
        positionCode: jobCode,
      })

      if (!isSuccess(checkResponse.code)) {
        ToastUtils.showToast(checkResponse.message ?? '')
        return false
      }
      const skip = checkResponse.zpData?.skip ?? false

      if (skip) {
        const response = await POST_SYNC<GeekInfoHttpModel.WorkExpSaveResponse>(URLConfig.URL_ZPGEEK_APP_DONEWORK_SAVE,
          ZPObj.objMap<string, string>()
            .set("position", work.positon.toString())
            .build()
        )
        if (!isSuccess(response.code)) {
          ToastUtils.showToast(response.message ?? '')
          return false
        }
        await ZPStore.setValue(localCodeKey(account.uid, "isBlueCollarPosition"), true)
      }
    }

    await ZPStore.setValue(localCodeKey(account.uid, "customReportName"), customReportName)

    detail.workExperienceList[0] = work

    return await this.update(model)
  }

  async updateCompanyName(name: string): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }
    const work = detail.workExperienceList.length > 0 ? detail.workExperienceList[0] : new GeekInfoHttpModel.ServerWorkBean()

    work.company = name

    detail.workExperienceList[0] = work

    return await this.update(model)
  }

  async updateJobStartWorkTime(startWorkTimeYear: number, startWorkTimeMonth: number, endWorkTimeYear: number, endWorkTimeMonth: number): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }
    const work = detail.workExperienceList.length > 0 ? detail.workExperienceList[0] : new GeekInfoHttpModel.ServerWorkBean()

    work.startDate = GeekDataHelper.getDateForText(startWorkTimeYear, startWorkTimeMonth)
    work.endDate = GeekDataHelper.getDateForText(endWorkTimeYear, endWorkTimeMonth)

    detail.workExperienceList[0] = work

    return await this.update(model)
  }

  async updateWorkSkills(workSkills: string, customWorkSkills: string): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }

    const work = detail.workExperienceList.length > 0 ? detail.workExperienceList[0] : new GeekInfoHttpModel.ServerWorkBean()

    work.workEmphasis = workSkills
    await ZPStore.setValue(localCodeKey(account.uid, "customWorkSkills"), customWorkSkills)

    detail.workExperienceList[0] = work

    return await this.update(model)
  }

  async updateWorkResponsibility(workResponsibility: string): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }

    const work = detail.workExperienceList.length > 0 ? detail.workExperienceList[0] : new GeekInfoHttpModel.ServerWorkBean()

    //工作内容
    work.responsibility = workResponsibility

    const result = await this.updateGeekWork(detail, work)

    if (result == null) {
      return false
    }

    work.workId = result.workId

    detail.workExperienceList[0] = work

    await ZPStore.setValue(localCodeKey(account.uid, "isCompletedWork"), true)

    return await this.update(model)
  }

  private async updateGeekWork(detail: GeekInfoHttpModel.ServerMyGeekDetailBean, model: GeekInfoHttpModel.ServerWorkBean): Promise<GeekInfoHttpModel.WorkExpSaveResponse | null> {

    const map = ZPObj.objMap<string, string>()
    map.set("workEmphasis", model.workEmphasis)
    map.set("workId", model.workId.toString())
    map.set("position", model.positon.toString())
    map.set("positionName", model.positionName)
    map.set("customPositionId", model.reportPositionId.toString())
    map.set("graduate", detail.freshGraduate.toString())
    map.set("startDate", model.startDate)
    if (!TextUtils.isEmpty(model.endDate)) {
      map.set("endDate", model.endDate)
    }
    map.set("workDate8", detail.workDate8.toString())
    map.set("responsibility", model.responsibility)
    map.set("company", model.company)

    console.log(map.toString())

    const response = await POST_SYNC<GeekInfoHttpModel.WorkExpSaveResponse>(URLConfig.URL_GEEK_UPDATE_WORK_EXPERIENCE, map.build())

    if (!isSuccess(response.code)) {
      ToastUtils.showToast(response.message ?? '')
      return null
    }
    return response.zpData ?? null
  }

  async updateDegreeCode(degreeCode: number, eduType: number): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }

    if (account == null) {
      return false
    }

    const edu = detail.eduExperienceList.length > 0 ? detail.eduExperienceList[0] : new GeekInfoHttpModel.ServerEduBean()

    // 中专中技、高中、初中及以下教育模块只需填写学历和时间段，则把学校和专业清除
    if (GeekDataHelper.isLowEducation(degreeCode)) {
      edu.schoolId = 0
      edu.school = ''
      edu.major = ''
      await ZPStore.setValue(localCodeKey(account.uid, "majorCode"), 0)
    }
    edu.degree = degreeCode
    edu.eduType = eduType

    detail.eduExperienceList[0] = edu

    return await this.update(model)
  }

  async updateSchool(name: string, code: number): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }

    if (account == null) {
      return false
    }

    const edu = detail.eduExperienceList.length > 0 ? detail.eduExperienceList[0] : new GeekInfoHttpModel.ServerEduBean()
    edu.schoolId = code
    edu.school = name

    detail.eduExperienceList[0] = edu

    return await this.update(model)
  }

  async updateMajor(name: string, code: number): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }

    if (account == null) {
      return false
    }

    const edu = detail.eduExperienceList.length > 0 ? detail.eduExperienceList[0] : new GeekInfoHttpModel.ServerEduBean()
    edu.major = name
    await ZPStore.setValue(localCodeKey(account.uid, "majorCode"), code)

    detail.eduExperienceList[0] = edu

    return await this.update(model)
  }

  async updateSchoolTime(startTime: number, endTime: number): Promise<boolean> {
    const model = await this.get()

    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()

    if (detail == null || account == null) {
      return false
    }

    if (account == null) {
      return false
    }

    const edu = detail.eduExperienceList.length > 0 ? detail.eduExperienceList[0] : new GeekInfoHttpModel.ServerEduBean()
    edu.startDate = GeekDataHelper.getDateForLong(startTime, 1).toString()
    edu.endDate = GeekDataHelper.getDateForLong(endTime, 1).toString()

    const result = await this.updateGeekEdu(edu)

    if (result == null) {
      return false
    }

    edu.eduId = result.eduId

    detail.eduExperienceList[0] = edu

    await ZPStore.setValue(localCodeKey(account.uid, "isCompletedEducation"), true)

    return await this.update(model)

  }

  private async updateGeekEdu(model: GeekInfoHttpModel.ServerEduBean): Promise<GeekInfoHttpModel.EduExpUpdateResponse | null> {

    const map = ZPObj.objMap<string, string>()
    map.set("eduId", model.eduId.toString())
    map.set("school", model.school)
    map.set("schoolId", model.schoolId.toString())
    map.set("degree", model.degree.toString())
    map.set("major", model.major)
    map.set("eduType", model.eduType.toString())
    map.set("startDate", model.startDate.toString())
    map.set("endDate", model.endDate.toString())

    const response = await POST_SYNC<GeekInfoHttpModel.EduExpUpdateResponse>(URLConfig.URL_GEEK_UPDATE_EDU_EXPERIENCE, map.build())

    if (!isSuccess(response.code)) {
      ToastUtils.showToast(response.message ?? '')
      return null
    }
    return response.zpData ?? null
  }


  async updateExpectData(cities: LevelBean[], positions: LevelBean[]): Promise<boolean> {
    if (CheckUtils.isEmptyArr(positions)) {
      return false
    }
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()
    if (detail == null || account == null) {
      return false
    }
    let cityCode = 0
    let cityName = ''
    if (CheckUtils.isNotEmptyArr(cities)) {
      cityCode = cities[0].code
      cityName = cities[0].name
    }

    let lowSalary = 0
    let highSalary = 0
    const expectList = detail.expectPositionList
    if (CheckUtils.isNotEmptyArr(expectList)) {
      lowSalary = expectList[0].lowSalary
      highSalary = expectList[0].highSalary
    }

    const newExpectList = new Array<GeekInfoHttpModel.ServerExpectBean>()
    positions.forEach(element => {
      const bean = new GeekInfoHttpModel.ServerExpectBean()
      bean.position = element.code
      bean.positionName = element.name
      bean.location = cityCode
      bean.locationName = cityName
      bean.lowSalary = lowSalary
      bean.highSalary = highSalary
      newExpectList.push(bean)
    });
    detail.expectPositionList = newExpectList
    return await this.update(model)
  }

  async updateExpectSalary(lowSalary: number, highSalary: number): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()
    if (detail == null || account == null) {
      return false
    }
    const expectList = detail.expectPositionList
    if (CheckUtils.isEmptyArr(expectList)) {
      return false
    }
    expectList.forEach(element => {
      element.lowSalary = lowSalary
      element.highSalary = highSalary
    });
    return await this.update(model)
  }

  async updateStudentExpectWork(positions: LevelBean[]): Promise<boolean> {

    if (CheckUtils.isEmptyArr(positions)) {
      return false
    }
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()
    if (detail == null || account == null) {
      return false
    }
    let cityCode = 0
    let cityName = ''
    let lowSalary = 0
    let highSalary = 0
    let otherCityList = new Array<LevelBean>()
    const expectList = detail.expectInternList
    if (CheckUtils.isNotEmptyArr(expectList)) {
      cityCode = expectList[0].location
      cityName = expectList[0].locationName
      lowSalary = expectList[0].lowSalary
      highSalary = expectList[0].highSalary
      otherCityList = expectList[0].interestLocationList
    }

    const newExpectList = new Array<GeekInfoHttpModel.ServerExpectBean>()
    positions.forEach(element => {
      const bean = new GeekInfoHttpModel.ServerExpectBean()
      bean.position = element.code
      bean.positionName = element.name
      bean.location = cityCode
      bean.locationName = cityName
      bean.lowSalary = lowSalary
      bean.highSalary = highSalary
      bean.interestLocationList = otherCityList
      newExpectList.push(bean)
    });
    detail.expectInternList = newExpectList
    return await this.update(model)
  }

  async updateStudentExpectData(cities: Array<LevelBean>, lowSalary: number, highSalary: number): Promise<boolean> {

    if (CheckUtils.isEmptyArr(cities)) {
      return false
    }
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()
    if (detail == null || account == null) {
      return false
    }
    const expectList = detail.expectInternList

    if (CheckUtils.isEmptyArr(expectList)) {
      return false
    }
    let cityCode = 0
    let cityName = ''
    const otherCityList = new Array<LevelBean>()
    cities.forEach((element, index) => {
      if (index == 0) {
        cityCode = element.code
        cityName = element.name
      } else {
        otherCityList.push(element)
      }
    });

    expectList.forEach(element => {
      element.location = cityCode
      element.locationName = cityName
      element.lowSalary = lowSalary
      element.highSalary = highSalary
      element.interestLocationList = otherCityList
    });
    return await this.update(model)
  }


  async updateExpectAdvantage(advantage: string): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()
    if (detail == null || account == null) {
      return false
    }
    detail.userDescription = advantage

    return await this.update(model)
  }

  async updateExpectAvatar(avatarUrl: string, avatarDefaultIndex: number): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()
    if (detail == null || account == null) {
      return false
    }
    detail.avatarUrl = avatarUrl
    const avatarDefaultSuccess = await ZPStore.setValue(localCodeKey(account.uid, "avatarDefaultIndex"), avatarDefaultIndex)
    const modelSuccess = await this.update(model)
    return avatarDefaultSuccess && modelSuccess
  }

  async updateAvatar(tinyAvatar: string, largeAvatar: string): Promise<boolean> {
    const model = await this.get()
    const detail = model?.geekDetail
    const account = Kernel.getInstance().getAccount()
    if (detail == null || detail.userInfo == null || account == null) {
      return false
    }
    detail.userInfo.tiny = tinyAvatar
    detail.userInfo.large = largeAvatar
    const modelSuccess = await this.update(model)
    return modelSuccess
  }


}