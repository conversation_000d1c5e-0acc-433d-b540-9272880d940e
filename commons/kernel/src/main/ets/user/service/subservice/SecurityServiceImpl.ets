import { TextUtils } from '@ohos/utils'
import common from '@ohos.app.ability.common'
import { ZPRole } from '../../../constants/ZPRole'
import { GetUserSecurityResponse } from '../../models/CommonModels'
import { AbsSubService } from '../AbsSubService'
import SecurityService from './SecurityService'
import { ZPEvents } from '@ohos/utils/src/main/ets/constants/ZPEvents';


export class SecurityServiceImpl extends AbsSubService<GetUserSecurityResponse> implements SecurityService {

  httpResponseKey(): string {
    return "certification.security.get"
  }

  role(): ZPRole {
    return ZPRole.All
  }

  async getSecurityUrl(): Promise<string> {
    const model =  await this.get()
    return model?.securityUrl ?? ''
  }

  updateUrl(url: string): void {
    let context = AppStorage.get<common.Context>('context')
    if (TextUtils.isEmpty(url)) {
      context?.eventHub.emit(ZPEvents.Security.CLOSE)
    } else {
      context?.eventHub.emit(ZPEvents.Security.OPEN, url)
    }
  }


}