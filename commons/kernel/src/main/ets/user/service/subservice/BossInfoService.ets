import { BossInfoHttpModel } from '../../models/boss/BossInfoHttpModel';
import { IUserSubService } from '../IUserSubService';
import { BrandComJoinCommonResponse, ServerCompositeRecEntryBean, ServerJobBean } from '@ohos/zp_api'

export interface BossInfoService extends IUserSubService<BossInfoHttpModel.BossInfoServiceModel> {
  updateAvatar(avatarIndex: number, tinyAvatarUrl: string|null, largeAvatarUrl: string|null): Promise<boolean>
  updateInfo(model: BossInfoHttpModel.BossInfoServiceModel): Promise<boolean>
  updateComInfo(brandCom: BrandComJoinCommonResponse): Promise<boolean>

  /**
   * 获取全部职位列表
   */
  getJobList(): Array<ServerJobBean>

  /**
   * 获取热招职位id列表
   */
  getHotJobIds(): string

  /**
   * 获取未付费（待开放）职位id列表
   */
  getWaitOpenJobIds(): string
}

export default BossInfoService;