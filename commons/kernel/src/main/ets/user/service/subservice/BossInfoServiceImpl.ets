import { CheckUtils, ZPEvents } from '@ohos/utils/Index';
import { BrandComJoinCommonResponse, GetBossJobListResponse,
  GetJobSortListResponse,
  ServerJobBean } from '@ohos/zp_api';
import { ZPRole } from '../../../constants/ZPRole';
import { getZpData } from '../../constants/UserInfoConstants';
import { BossInfoHttpModel } from '../../models/boss/BossInfoHttpModel';
import { AbsSubService } from '../AbsSubService';
import BossInfoService from './BossInfoService';

export class BossInfoServiceImpl extends AbsSubService<BossInfoHttpModel.BossInfoServiceModel> implements BossInfoService {
  private eventHub = getContext().eventHub
  private jobList: Array<ServerJobBean> = new Array()

  // TODO: 存SP
  private hotJobIds: string = '' // 热招职位的id列表
  private waitOpenJobIds: string = '' // 未付费（待开放）职位的id列表

  httpResponseKey(): string {
    return "zpboss.app.boss.get.detail"
  }

  role(): ZPRole {
    return ZPRole.BOSS
  }

  parseHttp(data: Record<string, Object>): BossInfoHttpModel.BossInfoServiceModel {
    // 1、职位列表
    let jobListResp  = getZpData<GetBossJobListResponse>('zpjob.job.local.data', data)
    if (jobListResp && CheckUtils.isNotEmptyArr(jobListResp.jobList)) {
      this.jobList = jobListResp.jobList
    }

    // 5、职位排序
    let jobSortListResp = getZpData<GetJobSortListResponse>('zpjob.job.sort.list', data)
    if (jobSortListResp) {
      this.hotJobIds = jobSortListResp.hotList
      this.waitOpenJobIds = jobSortListResp.waitOpenList
      this.eventHub.emit(ZPEvents.BossJobListChangeEvent.getName())
    }

    return super.parseHttp(data)
  }

  async updateAvatar(avatarIndex: number, tinyAvatarUrl: string|null, largeAvatarUrl: string|null): Promise<boolean> {
    const model: BossInfoHttpModel.BossInfoServiceModel|null = await this.get()
    let userInfo = model?.userDetail?.userInfo
    if (!userInfo) {
      return false
    }

    userInfo.tiny = tinyAvatarUrl ? tinyAvatarUrl : ''
    userInfo.large = largeAvatarUrl ? largeAvatarUrl : ''
    userInfo.headImg = avatarIndex

    return await this.update(model)
  }

  async updateInfo(model: BossInfoHttpModel.BossInfoServiceModel): Promise<boolean> {
    return await this.update(model)
  }

  async updateComInfo(brandCom: BrandComJoinCommonResponse): Promise<boolean> {
    const model: BossInfoHttpModel.BossInfoServiceModel|null = await this.get()
    if (!model) {
      return false
    }

    // 更新品牌
    if (brandCom.brand) {
      model.brand = brandCom.brand
    }

    if (model.userDetail) {
      let userExtra = model.userDetail.userExtra
      if (userExtra && brandCom.company) { // 更新公司
        userExtra.comName = brandCom.company.name
        userExtra.comId = brandCom.company.comId
        userExtra.comCertificate = brandCom.company.comCertificate
      }

      // 更新Boss的认证状态
      let certInfo = model.userDetail.certificationInfo
      if (brandCom.changeCert && certInfo) {
        certInfo.certification = 0
      }
    }

    return await this.update(model)
  }

  getJobList(): Array<ServerJobBean> {
    return this.jobList
  }

  getHotJobIds(): string {
    return this.hotJobIds
  }

  getWaitOpenJobIds(): string {
    return this.waitOpenJobIds
  }
}