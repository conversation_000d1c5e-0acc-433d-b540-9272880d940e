import TextUtils from '@ohos/http/src/main/ets/utils/TextUtils';
import { ZPStore } from '@ohos/utils';
import { Account } from '../../account/Account';
import { ZPRole } from '../../constants/ZPRole';
import { <PERSON>el } from '../../Kernel';
import { AccountLifecycleCallback } from '../../service/AccountService';
import { getUserInfoKey, getZpData } from '../constants/UserInfoConstants';
import { UserServiceImpl } from '../UserServiceImpl';
import { IUserSubService } from './IUserSubService';
import HashMap from '@ohos.util.HashMap';

const TAG: string = '[BaseSubService]';

export abstract class AbsSubService<T> implements IUserSubService<T>, AccountLifecycleCallback {
  constructor(kernel: Kernel, userServiceImpl: UserServiceImpl) {
    kernel.registerAccountLifecycleCallbacks([this])
    userServiceImpl.addSubService<T>(this.role(), this)
  }

  /**
   * 关键数据
   */
  private model?: T

  /**
   * 解析数据
   * @param map 代表着 是否从zpdata
   * @returns
   */
  parseHttp(data: Record<string, Object>): T {
    const zpData = getZpData<T>(this.httpResponseKey(), data)

    if (zpData == null) {
      throw new Error("parseHttp 解析失败");
    }
    this.model = zpData as T
    return this.model
  }

  /**
   * 需要 服务 和 网络请求数据绑定
   * @returns
   */
  abstract httpResponseKey(): string

  abstract role(): ZPRole

  onAccountInit(account: Account) {
  }

  onAccountRelease(account: Account) {
  }

  onIdentityInit(identity: number) {
  }

  onSwitchIdentity(identity: number, isChangeIdentity: boolean) {
  }

  onIdentityRelease(identity: number) {
  }


  async get(): Promise<T | null> {
    if (this.model == null) {
      const checkModel = await this.checkModel()
      if (checkModel != null) {
        this.model = checkModel
      }
    }

    return this.model as T | null
  }

  async update(info: T | null): Promise<boolean> {
    if (info == null) {
      return false
    }
    try {
      const account = this.checkInit()
      if (account == null) {
        return false
      }
      const saveKey = this.getRoelSaveKey(account)
      const values = await ZPStore.getValue<Record<string, Object>>(saveKey)
      if (values == null) {
        console.debug(TAG, "update() ZPStore is empty  saveKey " + saveKey)
        return false
      }
      values[this.httpResponseKey()] = info

      const result = await ZPStore.setValueMap(saveKey, values)
      this.model = info

      return result
    } catch (e) {
      console.debug(TAG, "update()  ZPStore error " + e)
      return false
    }
  }


  /**
   * 检查基础数据
   * @returns
   */
  private checkInit(): Account | null {

    if (TextUtils.isEmpty(this.httpResponseKey())) {
      console.debug(TAG, "checkInit httpResponseKey() is empty")
      return null
    }
    if (!Kernel.getInstance().isLoggedIn()) {
      console.debug(TAG, "checkModel not login")
      return null
    }
    const account = Kernel.getInstance().getAccount()
    if (account == null) {
      console.debug(TAG, "checkModel account is null")
      return null
    }
    return account
  }

  protected async checkModel(): Promise<T | null> {
    try {
      const account = this.checkInit()

      if (account == null) {
        return null
      }
      const saveKey = this.getRoelSaveKey(account)
      const values = await ZPStore.getValue<HashMap<string, Object>>(saveKey)
      if (values == null) {
        console.debug(TAG, "checkModel ZPStore is empty  saveKey " + saveKey)
        return null
      }
      return values[this.httpResponseKey()] as T
    } catch (e) {
      console.debug(TAG, "checkModel error " + e)
      return null
    }
  }

  private getRoelSaveKey(account: Account) : string {
    switch (this.role()) {
      case ZPRole.GEEK:
      case ZPRole.BOSS:
        return getUserInfoKey(account.uid, this.role().valueOf())
      case ZPRole.All:
        return getUserInfoKey(account.uid, account.identity)
    }
  }
}