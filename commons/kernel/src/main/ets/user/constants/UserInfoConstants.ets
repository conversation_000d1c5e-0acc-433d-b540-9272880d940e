export class UserInfoConstants {
  //社招  也就是职场人
  static readonly SOCIAL_RECRUITMENT: number = 0
  //无工作经验
  static readonly NONE_WORK_EXPERIENCE: number = -1
  //应届生找工作
  static readonly FRESH_GRADUATE_FIND_JOB: number = 1
  //在校生.找实习
  static readonly AT_SCHOOL_INTERN: number = 2
  //学生档案
  static readonly STUDENT_RECORD: number = 3
  /**
   * 用户数据 key
   */
  static readonly userInfoKey: string = "userInfoKey";
  /**
   * 用户数据 - 求职状态
   */
  static readonly selectedWorkStatusCodeKey: string = "selectedWorkStatusCode";
  /**
   * 其他职位 code
   */
  static readonly workOther: number = 200101;
}

export function getUserInfoKey(uid: number, identity: number): string {
  return UserInfoConstants.userInfoKey + "_" + identity + "_" + uid
}

export function localCodeKey(uid: number, key: string): string {
  return key + "_" + uid
}


export function getZpData<T>(key: string, value: object): T {
  const map = value[key] as Record<string, Object>
  return map['zpData'] as T
}