import { GET, isNonBusinessError, URLConfig, ZPHttpError } from '@ohos/http';
import { Logger } from '@ohos/utils';
import { ContactBaseInfoListResponse, ServerAddFriendBean } from '@ohos/zp_api';
import { Account } from '../account/Account';
import { ContactEntity } from '../chat/db/entity/ContactEntity';
import { DatabaseService } from '../chat/service/DatabaseService';
import { AccountLifecycleCallback } from './AccountService';
import { MqttService } from './MqttService';


const TAG = '[ContactService]'

export default class ContactService implements AccountLifecycleCallback {
  private dataService: DatabaseService
  private mqttService: MqttService

  constructor(mqttService: MqttService) {
    this.dataService = DatabaseService.getInstance()
    this.mqttService = mqttService
  }

  onAccountInit(account: Account): void {
    this.mqttService.onAccountInit(account)
  }

  onIdentityInit(identity: number) {
    this.syncContactList(identity);
  }

  onIdentityRelease(identity: number) {
    this.mqttService.onIdentityRelease(identity)
  }

  onAccountRelease(account: Account) {
    this.mqttService.onAccountRelease(account)
  }

  /**
   * 同步联系人列表
   *
   * TODO：
   *  1、分页拉取：先拉联系人id列表，再分页拉联系人列表
   *  2、与本地diff
   *  3、失败后重试
   */
  private syncContactList(identity: number) {
    GET<ContactBaseInfoListResponse>(URLConfig.URL_FRIEND_BASE_INFO_LIST)
      .then(async (resp) => {
        if (resp && resp.zpData && resp.zpData.baseInfoList) {
          let contactDao = await this.dataService.getContactDao()
          if (contactDao) {
            let dbContactList = new Array<ContactEntity>()
            resp.zpData.baseInfoList.forEach(item => dbContactList.push(ContactService.toEntity(item)))
            contactDao.upsertAllBaseInfo(dbContactList)
            this.mqttService.onIdentityInit(identity)
          }
        }
      })
      .catch((reason: ZPHttpError) => {
        if (typeof reason.errorCode === 'number') {
          let code = reason.errorCode as number
          if (!isNonBusinessError(code)) {
            Logger.error(0, TAG, 'get contact baseInfo failed: ' + reason.errorReason)
            setTimeout(() => {
              this.syncContactList(identity)
            }, 500)
          }
        }
      })
  }

  private static toEntity(friendBean: ServerAddFriendBean): ContactEntity {
    let contact = new ContactEntity()
    contact.friendId = friendBean.friendId
    contact.friendSource = friendBean.friendSource
    contact.securityId = friendBean.securityId
    contact.name = friendBean.name
    contact.tinyUrl = friendBean.tinyUrl
    contact.datetime = friendBean.datetime
    contact.jobId = friendBean.jobId
    contact.jobName = friendBean.jobName
    contact.invalidJob = friendBean.invalidJob
    contact.jobSource = friendBean.jobSource
    contact.expectId = friendBean.expectId
    contact.sourceTitle = friendBean.sourceTitle
    contact.isTop = friendBean.isTop
    contact.isRejectUser = friendBean.isRejectUser
    contact.rejectDesc = friendBean.rejectDesc
    contact.goldGeekStatus = friendBean.goldGeekStatus
    contact.itemType = friendBean.itemType
    contact.headline = friendBean.headline
    contact.noDisturb = friendBean.noDisturb
    contact.isFiltered = friendBean.isFiltered
    contact.filterReasonList = friendBean.filterReasonList
    contact.teamMemberSize = friendBean.teamMemberSize
    contact.itemSource = friendBean.itemSource
    contact.isStar = friendBean.isStar
    contact.iconFlag = friendBean.iconFlag

    //boss联系人详情
    contact.expectPositionName = friendBean.expectPositionName

    //牛人的联系人详情
    contact.title = friendBean.title
    contact.certification = friendBean.certification
    contact.company = friendBean.company
    contact.isHeadhunter = friendBean.isHeadhunter
    contact.highGeek = friendBean.highGeek
    contact.positionName = friendBean.positionName
    contact.salaryDesc = friendBean.salaryDesc
    contact.workplace = friendBean.workplace
    contact.isAgentRecruit = friendBean.isAgentRecruit
    contact.isHunter = friendBean.isHunter
    contact.isAgency = friendBean.isAgency

    // contact.localInfo.avatarIndex = friendBean.headImg

    return contact
  }
}