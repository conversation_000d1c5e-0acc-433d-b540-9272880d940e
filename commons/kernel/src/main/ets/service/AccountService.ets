import { Account } from '../account/Account';
import ArrayList from '@ohos.util.ArrayList';
import { AccountStore } from '../account/AccountStore';
import common from '@ohos.app.ability.common';
import { ZPRole } from '../constants/ZPRole';
import { LoginError } from '../exception/LoginError';

export interface AccountLifecycleCallback {
  onAccountInit(account: Account): void

  onIdentityInit(identity: number): void

  onIdentityRelease(identity: number): void

  onAccountRelease(account: Account): void
}

export class AccountService {
  private mAccount: Account | null = null
  private mAccountLifecycleCallbacks: ArrayList<AccountLifecycleCallback> = new ArrayList<AccountLifecycleCallback>()
  private mAccountStore: AccountStore

  constructor(context: common.Context) {
    this.mAccountStore = new AccountStore(context)
    this.mAccount = this.mAccountStore.getAccount()
  }

  public getAccount(): Account | null {
    return this.mAccount;
  }

  public registerAccountLifecycleCallback(callback: AccountLifecycleCallback): void {
    this.mAccountLifecycleCallbacks.add(callback);
  }

  public unregisterAccountLifecycleCallback(callback: AccountLifecycleCallback): void {
    this.mAccountLifecycleCallbacks.remove(callback)
  }

  private async notifyAccountInit() {
    for (const item of this.mAccountLifecycleCallbacks) {
      if (this.mAccount == null) {
        throw new LoginError('未登录');
      }
      await item.onAccountInit(this.mAccount)
      console.log("notifyAccountInit");
    }
  }

  private async notifyIdentityInit(identity: number) {
    for (const item of this.mAccountLifecycleCallbacks) {
      await item.onIdentityInit(identity)
      console.log("notifyIdentityInit");
    }
  }

  private async notifyIdentityRelease(identity: number) {
    for (const item of this.mAccountLifecycleCallbacks) {
      await item.onIdentityRelease(identity)
      console.log("notifyIdentityRelease");
    }
  }

  private async notifyAccountRelease() {
    for (const item of this.mAccountLifecycleCallbacks) {
      if (this.mAccount == null) {
        throw new LoginError('未登录');
      }
      await item.onAccountRelease(this.mAccount)
      console.log("notifyAccountRelease");
    }
  }

  public async login(account: Account) {
    if (!account) {
      throw new LoginError('Invalid account');
    }

    const origAccount = this.mAccount;

    if (origAccount && account.uid !== origAccount.uid) {
      await this.logout();
    }

    await this.saveAccount(account);
    this.mAccount = account;

    await this.onLogin();
  }

  public async logout() {
    const account = this.mAccount;

    if (account) {
      if (account.identity >= 0) {
        await this.notifyIdentityRelease(account.identity);
      }

      await this.notifyAccountRelease();
      this.mAccount = null;
      await this.mAccountStore.clearAccount()
    }
  }

  public async switchIdentity(role: ZPRole): Promise<void> {
    const account = this.mAccount;
    if (!account) {
      throw new LoginError('未登录');
    }

    const curIdentity = account.identity;

    if (curIdentity === role.valueOf()) {
      return;
    } else {
      await this.notifyIdentityRelease(curIdentity);
    }
    account.identity = role.valueOf()
    await this.saveAccount(account);
    await this.notifyIdentityInit(account.identity);
  }

  public async onLogin() {
    const account = this.mAccount;
    if (account) {
      await this.notifyAccountInit();
      if (account.identity >= 0) {
        await this.notifyIdentityInit(account.identity);
      }
    }
  }

  private async saveAccount(account: Account): Promise<void> {
    await this.mAccountStore.saveAccount(account);
  }

  public isLoggedIn(): boolean {
    return this.mAccount != null && this.mAccount.uid > 0
  }
}
