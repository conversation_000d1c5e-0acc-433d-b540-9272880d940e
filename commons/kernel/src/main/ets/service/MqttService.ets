import { HostConfig } from '@ohos/http';
import { Md5Utils } from '@ohos/utils';
import { Account } from '../account/Account';
import ConnectHandler from '../chat/ConnectHandler';
import ReceiverHandler from '../chat/ReceiverHandler';
import { <PERSON><PERSON> } from '../Kernel';
import MqttConfig, { PROTOCOL_VERSION } from '../mqtt/MqttConfig';
import MqttConnection from '../mqtt/MqttConnection';
import UserInfo from '../mqtt/UserInfo';
import { AccountLifecycleCallback } from './AccountService';

export class MqttService implements AccountLifecycleCallback {
  public constructor() {
    let address = HostConfig.getConfig()
    let mqttConfig: MqttConfig = new MqttConfig(`tcp://${address.mqttAddr}:${address.mqttPort}`, 300, 1, "chat");
    MqttConnection.init(mqttConfig, new ConnectHandler(), new ReceiverHandler());
  }

  onAccountInit(account: Account): void {
  }

  onIdentityInit(identity: number): void {
    let clientId = this.getClientId()
    let userName = this.getUsername()
    let password = this.getPassword()
    if (clientId && userName && password) {
      let user: UserInfo = new UserInfo(clientId, userName, password);
      MqttConnection.connect(user);
    }
  }

  onIdentityRelease(identity: number): void {
  }

  onAccountRelease(account: Account): void {
  }

  // 获取客户端ID
  private getClientId(): string|null {
    let clientId: string|null = this.getUsername();
    if (clientId) {
      clientId = Md5Utils.calculateMD5(clientId);
      clientId = clientId.substring(8, 24);
    }
    return clientId;
  }

  // 获取登录用户名
  private getUsername(): string|null {
    let account = Kernel.getInstance().getAccount()

    let result: string|null = null;
    if (account) {
      const uid: string = account.uid.toString();
      const role: string = account.identity.toString();
      const protoVersion: string = PROTOCOL_VERSION;
      const appVersion: string = "11.24"; // Placeholder, replace with actual logic
      result = `${uid}-${role}-${protoVersion}-${appVersion}`;
    }
    return result;
  }

  // 获取登录密码
  private getPassword(): string|null {
    let account = Kernel.getInstance().getAccount();

    let result: string|null = null;
    if (account) {
      const secret: string = account.secretKey;
      const time: string = Date.now().toString();
      let md5: string = Md5Utils.calculateMD5(secret + time);
      md5 = md5.substring(8, 24);
      result = md5 + time;
    }
    return result;
  }
}