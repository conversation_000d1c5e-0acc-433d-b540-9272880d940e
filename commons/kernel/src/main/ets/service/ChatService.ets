import { Account } from '../account/Account';
import MessageRepository from '../chat/MessageRepositoty';
import { DatabaseService } from '../chat/service/DatabaseService';
import { AccountLifecycleCallback } from './AccountService';

export default class ChatService implements AccountLifecycleCallback {
  private dataService: DatabaseService

  constructor() {
    this.dataService = DatabaseService.getInstance()
  }

  onAccountInit(account: Account): void {
  }

  async onIdentityInit(identity: number) {
    let messageDao = await this.dataService.getMessageDao()
    if (!messageDao) {
      return
    }
    let dbMaxMid = await messageDao.getSingleMaxMid()
    MessageRepository.getInstance().saveMaxMessageId(true, dbMaxMid)
  }

  onIdentityRelease(identity: number) {
  }

  onAccountRelease() {
  }
}