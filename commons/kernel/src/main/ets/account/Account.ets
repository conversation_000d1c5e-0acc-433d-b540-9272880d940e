export enum AccountIdentity {
  IDENTITY_NONE = -1,
  IDENTITY_GEEK,
  IDENTITY_BOSS
}

export class Account {
  uid: number = -1;
  identity: number;
  t: string;
  wt: string;
  register: boolean;
  secretKey: string;
  //@since 814
  phone: string;
  //@since 814
  regionCode: string;
  //@since 815
  t2: string;
  //@since 1201
  zpAt: string

  constructor(uid: number, identity: number, t: string, wt: string, register: boolean, secretKey: string, phone: string, regionCode: string, t2: string, zpAt: string) {
    this.uid = uid;
    this.identity = identity;
    this.t = t;
    this.wt = wt;
    this.register = register;
    this.secretKey = secretKey;
    this.phone = phone;
    this.regionCode = regionCode;
    this.t2 = t2;
    this.zpAt =zpAt
  }

  isBoss() {
    return this.identity == AccountIdentity.IDENTITY_BOSS
  }

  isGeek() {
    return this.identity == AccountIdentity.IDENTITY_GEEK
  }
}