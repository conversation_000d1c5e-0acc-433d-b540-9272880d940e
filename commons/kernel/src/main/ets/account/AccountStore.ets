import common from '@ohos.app.ability.common';
import dataPreferences from '@ohos.data.preferences';
import { ValueType } from '@ohos.data.ValuesBucket';
import { AccountIdentity, Account } from './Account'

const ACCOUNT_UID = "acc_uid";
const ACCOUNT_IDENTITY = "acc_identity";
const ACCOUNT_T = "acc_t";
const ACCOUNT_T2 = "acc_t2";
const ACCOUNT_WT = "acc_wt";
const ACCOUNT_SECRET_KEY = "acc_secretkey";
const ACCOUNT_REGISTER_KEY = "acc_register";
const ACCOUNT_PHONE = "acc_phone";
const ACCOUNT_LAST_PHONE = "acc_last_phone";
const ACCOUNT_REGION_CODE = "acc_region_code";
const ACCOUNT_LAST_REGION_CODE = "acc_last_region_code";
const ACCOUNT_VERSION_CODE = "acc_app_version_code";
const ACCOUNT_LOGIN_STATUS = "acc_login_status";
const ACCOUNT_ZPAT = "acc_zpat";

export class AccountStore {
  protected sharedPreferences: dataPreferences.Preferences

  constructor(context: common.Context) {
    this.sharedPreferences = dataPreferences.getPreferencesSync(context, {name: 'default'})
  }

  private getSync<T extends dataPreferences.ValueType>(key: string, defaultValue: T): T {
    return this.sharedPreferences.getSync(key, defaultValue) as T
  }

  private async putAsync(key: string, value: ValueType) {
    await this.sharedPreferences.put(key, value)
    await this.sharedPreferences.flush()
  }

  private async putAllAsync(keyValues: Record<string, ValueType>) {
    Object.entries(keyValues).forEach(async (keyValue) => {
      await this.sharedPreferences.put(keyValue[0], keyValue[1])
    })
    await this.sharedPreferences.flush()
  }

  private async deleteAllAsync(keys: Array<string>) {
    for (let key of keys) {
      await this.sharedPreferences.delete(key)
    }
    await this.sharedPreferences.flush()
  }

  public async saveAccount(account: Account) {
    let keyValues: Record<string, ValueType> = {}
    keyValues[ACCOUNT_UID] = account.uid
    keyValues[ACCOUNT_IDENTITY] = account.identity
    keyValues[ACCOUNT_T] = account.t
    keyValues[ACCOUNT_T2] = account.t2
    keyValues[ACCOUNT_WT] = account.wt
    keyValues[ACCOUNT_PHONE] = account.phone
    keyValues[ACCOUNT_LAST_PHONE] = account.phone
    keyValues[ACCOUNT_REGION_CODE] = account.regionCode
    keyValues[ACCOUNT_LAST_REGION_CODE] = account.regionCode
    keyValues[ACCOUNT_SECRET_KEY] = account.secretKey
    keyValues[ACCOUNT_REGISTER_KEY] = account.register
    keyValues[ACCOUNT_ZPAT] = account.zpAt

    this.putAllAsync(keyValues)
  }

  public getAccount(): Account|null {
    let account: Account | null = null

    let uid: number = this.getUid()
    if (uid > 0) {
        let identity: number = this.getIdentity()
        let t: string = this.getToken()
        let t2: string = this.getToken2()
        if ((!t || t.length == 0) && (!t2 || t2.length == 0)) return null
        let wt: string = this.getWebToken()
        let sk: string = this.getSecretKey()
        let phone: string = this.getPhone()
        let regionCode: string = this.getRegionCode()
        let register: boolean = this.isRegister()
        let zpAt: string = this.getZpAt()
        account = new Account(uid, identity, t, wt, register, sk, phone, regionCode, t2, zpAt)
    }
    //读取数据兼容
    if (account != null) {
      // TODO
      // setLastPhone(account.getRegionCode(), account.getPhone());
    }

    return account;
  }

  /**
   * 清理登陆信息 不包括 身份信息
   */
  public async clearAccount() {
    let keys: Array<string> = [
      ACCOUNT_UID,
      ACCOUNT_T,
      ACCOUNT_T2,
      ACCOUNT_PHONE,
      ACCOUNT_REGION_CODE,
      ACCOUNT_WT,
      ACCOUNT_REGISTER_KEY,
      ACCOUNT_SECRET_KEY,
      ACCOUNT_LOGIN_STATUS,
      ACCOUNT_ZPAT
    ]

    await this.deleteAllAsync(keys)
  }

  public getUid(): number {
    return this.getSync(ACCOUNT_UID, -1)
  }

  public getIdentity(): number {
    return this.getSync(ACCOUNT_IDENTITY, AccountIdentity.IDENTITY_GEEK)
  }

  public async setIdentity(identity: number) {
    await this.putAsync(ACCOUNT_IDENTITY, identity)
  }

  getToken(): string {
    return this.getSync(ACCOUNT_T, "")
  }

  public async setT2(token2: string, zpAt: string) {
    await this.putAsync(ACCOUNT_T2, token2)
    await this.putAsync(ACCOUNT_ZPAT, zpAt)
  }

  public async setZPAt(zpAt: string) {
    await this.putAsync(ACCOUNT_ZPAT, zpAt)
  }

  getToken2(): string {
    return this.getSync(ACCOUNT_T2, "")
  }

  getWebToken(): string {
    return this.getSync(ACCOUNT_WT, "")
  }

  getZpAt(): string {
    return this.getSync(ACCOUNT_ZPAT, "");
  }

  getSecretKey(): string {
    return this.getSync(ACCOUNT_SECRET_KEY, "")
  }

  isRegister(): boolean {
    return this.getSync(ACCOUNT_REGISTER_KEY, false)
  }

  getPhone(): string {
    return this.getSync(ACCOUNT_PHONE, "")
  }

  public async setPhone(regionCode: string, phone: string) {
    let keyValues: Record<string, ValueType> = {}
    keyValues[ACCOUNT_PHONE] = phone
    keyValues[ACCOUNT_REGION_CODE] = regionCode
    keyValues[ACCOUNT_LAST_PHONE] = phone
    keyValues[ACCOUNT_LAST_REGION_CODE] = regionCode

    await this.putAllAsync(keyValues)
  }

  public async setUserLoginStatus(status: number) {
    await this.putAsync(ACCOUNT_LOGIN_STATUS, status)
  }

  public getUserLoginStatus(): number {
    return this.getSync(ACCOUNT_LOGIN_STATUS, 0)
  }

  public async setRegionCode(regionCode: string) {
    await this.putAsync(ACCOUNT_REGION_CODE, regionCode)
  }

  public getRegionCode(): string {
    return this.getSync(ACCOUNT_REGION_CODE, "+86")
  }
}