import { AccountLifecycleCallback, AccountService } from './service/AccountService';
import common from '@ohos.app.ability.common';
import { Account, AccountIdentity } from './account/Account';
import { ZPRole } from './constants/ZPRole';
import { IUserService } from './user/IUserService';
import { UserServiceImpl } from './user/UserServiceImpl';
import { LoginError } from './exception/LoginError';
import { MqttService } from './service/MqttService';
import ContactService from './service/ContactService';
import ChatService from './service/ChatService';


const STORAGE_KEY = 'Kernel'

export class Kernel {
  private accountService: AccountService;
  private userIService: IUserService
  private mqttService: MqttService
  private contactService: ContactService
  private chatService: ChatService

  private constructor(context: common.Context) {
    this.accountService = new AccountService(context);
    this.userIService = new UserServiceImpl(this);
    this.mqttService = new MqttService()
    this.contactService = new ContactService(this.mqttService)
    this.chatService = new ChatService()
    this.registerAccountLifecycleCallbacks([this.chatService, this.contactService])
    this.accountService.onLogin()
  }

  public static getInstance(): Kernel {
    if (!AppStorage.get<Kernel>(STORAGE_KEY)) {
      let context = AppStorage.get<common.Context>('context')
      AppStorage.setOrCreate(STORAGE_KEY, new Kernel(context));
    }
    return AppStorage.get<Kernel>(STORAGE_KEY) as Kernel;
  }

  public async login(account: Account): Promise<boolean> {
    try {
      await this.accountService.login(account)
      return true
    } catch (error) {
      console.error(error.message)
      this.handleError(error)
      return false
    }
  }

  public async logout(): Promise<boolean> {
    try {
      await this.accountService.logout()
      return true
    } catch (error) {
      console.error(error.message)
      this.handleError(error)
      return false
    }
  }

  public async switchIdentity(role: ZPRole): Promise<boolean> {
    try {
      const account = this.getAccount()
      if (account == null) {
        return false
      }
      await this.accountService.switchIdentity(role)
      return true
    } catch (error) {
      console.error(error.message)
      this.handleError(error)
      return false
    }
  }

  public getAccount(): Account | null {
    return this.accountService.getAccount()
  }

  public getUid(): number {
    return this.getAccount()?.uid ?? -1
  }

  public isSelfUid(uid?: number | null): boolean {
    return uid !== undefined && uid !== null && uid === this.getUid()
  }

  public isBossRole() {
    return this.getAccount()?.identity === AccountIdentity.IDENTITY_BOSS
  }

  public isGeekRole() {
    return this.getAccount()?.identity === AccountIdentity.IDENTITY_GEEK
  }

  public isLoggedIn(): boolean {
    return this.accountService.isLoggedIn()
  }

  public registerAccountLifecycleCallbacks(callbacks: AccountLifecycleCallback[]) {
    callbacks.forEach((callback) => this.accountService.registerAccountLifecycleCallback(callback))
  }

  public unregisterAccountLifecycleCallback(callback: AccountLifecycleCallback) {
    this.accountService.unregisterAccountLifecycleCallback(callback)
  }

  private handleError(error: Error) {
    if (error instanceof LoginError) {
      //nothing
      console.error(error.stack)
    }
  }

  public userService(): IUserService {
    return this.userIService
  }
}