import ConnectionHandler from './ConnectionHandler';
import MqttConfig, { IConnectionListener, IReceiveListener, ISendCallback } from './MqttConfig';
import UserInfo from './UserInfo';

const KEY = 'ConnectionHandler'

export default class MqttConnection {
  private static getConnectionHandler() {
    return AppStorage.get<ConnectionHandler>(KEY)
  }

  static init(mqttConfig: MqttConfig, connectionListener: IConnectionListener, receiveListener: IReceiveListener) {
    if (!AppStorage.get<ConnectionHandler>(KEY)) {
      AppStorage.setOrCreate(KEY, new ConnectionHandler(mqttConfig, connectionListener, receiveListener));
    }
  }

  static connect(userinfo: UserInfo): void {
    let connectionHandler = MqttConnection.getConnectionHandler()
    if (connectionHandler) {
      connectionHandler.connect(userinfo);
    }
  }

  static publish(playData: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, callback?: ISendCallback) {
    let connectionHandler = MqttConnection.getConnectionHandler()
    if (connectionHandler) {
      connectionHandler.publish(playData, callback);
    }
  }

  static async isConnected(): Promise<boolean> {
    let connectionHandler = MqttConnection.getConnectionHandler()
    if (connectionHandler) {
      return await connectionHandler.isConnected()
    }

    return false
  }
}