import { MqttQos } from '@ohos/mqtt';

export const PROTOCOL_VERSION =  "1.3";

export default class MqttConfig {
  url: string;
  connectTimeout: number;
  qos: MqttQos;
  topic: string;

  constructor(url: string, connectTimeout: number, qos: MqttQos, topic: string) {
    this.url = url;
    this.connectTimeout = connectTimeout;
    this.qos = qos;
    this.topic = topic;
  }
}

export interface ISendCallback {
  onSuccess(mid: number): void;
  onFailure(): void;
}

export interface IReceiveListener {
  onReceive(playData: ArrayBuffer): void;
}

export interface IConnectionListener {
  onConnectionConnected(): void;
  onConnectionFailed(): void;
  onConnectionConnecting(): void;
  onConnectionDisconnected(code: number): void;
}