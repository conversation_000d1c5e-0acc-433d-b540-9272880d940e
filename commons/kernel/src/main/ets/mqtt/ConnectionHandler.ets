import { MqttAsync, MqttClient, MqttConnectOptions, MqttPublishOptions, MqttSubscribeOptions } from '@ohos/mqtt';
import { Logger } from '@ohos/utils';
import MqttConfig, { IConnectionListener, IReceiveListener, ISendCallback } from './MqttConfig';
import UserInfo from './UserInfo';

/**
 *
 */
export class ConnectionHandler {
  private mqttAsyncClient: MqttClient|null = null
  private mqttConfig: MqttConfig;
  connectedCount: number = 0;
  private connectionListener: IConnectionListener;
  private receiveListener: IReceiveListener;
  private seq:number = 10;

  public constructor(mqttConfig: MqttConfig, connectionListener: IConnectionListener, receiveListener: IReceiveListener) {
    this.mqttConfig = mqttConfig;
    this.connectionListener = connectionListener;
    this.receiveListener = receiveListener;
  }

  public connect(userinfo: UserInfo): void {
    if (!this.mqttAsyncClient) {
      this.createClient(userinfo);
    }

    if (!this.mqttAsyncClient) {
      return
    }

    // if (this.isConnected()) {
    //   Logger.info("ConnectionHandler isConnected");
    //   return;
    // }

    let options: MqttConnectOptions = {
      userName: userinfo.userName,
      password: userinfo.password,
      connectTimeout: this.mqttConfig.connectTimeout,
      MQTTVersion: 3,
      automaticReconnect: true
    };
    this.connectionListener.onConnectionConnecting()
    Logger.info("connect options=[%s]", options);
    this.mqttAsyncClient.connect(options, (err, data) => {
      if (!err) {
        if (data.message == "Connect Success") {
          this.connectedCount++;
          this.connectionListener.onConnectionConnected()
          // this.subscribe();
        }
      } else {
        let errMsg = JSON.stringify(err)
        this.connectionListener.onConnectionFailed()
      }
    });
  }

  public publish(payload: ArrayBuffer, callback?: ISendCallback): void {
    if (!this.mqttAsyncClient) {
      return
    }

    let publishOption: MqttPublishOptions = {
      topic: this.mqttConfig.topic,
      qos: this.mqttConfig.qos,
      payload: payload,
    }
    this.mqttAsyncClient.publish(publishOption, (err, data) => {
      if (!err && data['code'] == 0) {
        callback?.onSuccess(data.mid)
        Logger.info("publish success: " + JSON.stringify(data));
      } else {
        callback?.onFailure()
        Logger.error("publish onFailure: " + JSON.stringify(data));
      }
    });
  }

  createClient(userinfo: UserInfo): void {
    Logger.info("createClient");
    this.mqttAsyncClient = MqttAsync.createMqtt({
      url: this.mqttConfig.url,
      clientId: userinfo.clientId,
      persistenceType: 1,
    });
    this.mqttAsyncClient.setMqttTrace(5) // TODO: 使用MQTTASYNC_TRACE_LEVELS.MQTTASYNC_TRACE_ERROR会报undefined异常
    this.messageArrived();
  }

  isConnected() {
    if (!this.mqttAsyncClient) {
      return false
    }

    return this.mqttAsyncClient.isConnected().then((data) => {
      return data;
    })
  }

  subscribe() {
    if (!this.mqttAsyncClient) {
      return
    }

    let subscribeOption: MqttSubscribeOptions = {
      topic: this.mqttConfig.topic,
      qos: 1
    }
    this.mqttAsyncClient.subscribe(subscribeOption, (err, data) => {
      if (!err) {

      } else {

      }
    });
  }

  messageArrived() {
    if (this.mqttAsyncClient == null) {
      return;
    }
    this.mqttAsyncClient.messageArrived((err, data) => {
      if (!err) {
        Logger.info("messageArrived");
        this.receiveListener.onReceive(data.payload);
      } else {
        Logger.error("messageArrived err=[%s]", err);
      }
    });
  }

  async reconnect() {
    if (this.mqttAsyncClient == null) {
      return;
    }
    if (!(await this.isConnected())) {
      if (this.connectedCount == 0) {
        return;
      }
      this.mqttAsyncClient.reconnect().then((data) => {
      });
    }
  }

  connectLost() {
    if (!this.mqttAsyncClient) {
      return
    }

    this.mqttAsyncClient.connectLost((err, data) => {
      if (!err) {
        Logger.error("connectLost err=[%s]", err);
        this.reconnect();
      } else {
        this.connectionListener.onConnectionDisconnected(0);
      }
    });
  }
}

export default ConnectionHandler;
