import util from '@ohos.util';
import { DaoMaster, DaoSession, Database, OpenHelper } from '@ohos/dataorm';
import common from '@ohos.app.ability.common';
import { ContactEntity } from './entity/ContactEntity';
import { ContactDao } from './dao/ContactDao';
import { MessageEntity } from './entity/MessageEntity';
import {MessageDao} from './dao/MessageDao';
import { Account } from '../../account/Account';
import { Kernel } from '../../Kernel';

const TAG = '[ChatDataBase]'

export class ChatDataBase {
  private static instance: ChatDataBase

  private context: common.Context
  private daoSession: DaoSession|null = null
  private contactDao: ContactDao|null = null
  private messageDao: MessageDao|null = null

  private constructor(context: common.Context) {
    this.context = context
  }

  static getInstance() {
    if (!ChatDataBase.instance) {
      let context = AppStorage.get<common.Context>('context')
      if (context) {
        let db = new ChatDataBase(context)
        ChatDataBase.instance = db
      }
    }
    return ChatDataBase.instance;
  }

  private async createDataBase(): Promise<DaoSession|null> {
    if (this.daoSession) {
      return this.daoSession
    }

    let account: Account|null = Kernel.getInstance().getAccount()
    if (!account) {
      return null
    }
    let uid = account.uid
    let identity = account.identity

    let chatDbName = util.format("chat-%d-%d.db", uid, identity)
    let helper: OpenHelper = new DbOpenHelper(this.context, chatDbName);
    helper.setEntities(ContactEntity, MessageEntity);
    let db: Database = await helper.getWritableDb();
    this.daoSession = new DaoMaster(db).newSession();
    return this.daoSession
  }

  public async getContactDao(): Promise<ContactDao|null> {
    if (!this.contactDao) {
      let daoSession = await this.createDataBase()
      if (daoSession) {
        this.contactDao = new ContactDao(daoSession.getBaseDao(ContactEntity))
      }
    }

    return this.contactDao
  }

  public async getMessageDao(): Promise<MessageDao|null> {
    if (!this.messageDao) {
      let daoSession = await this.createDataBase()
      if (daoSession) {
        this.messageDao = new MessageDao(daoSession.getBaseDao(MessageEntity))
      }
    }

    return this.messageDao
  }
}

class DbOpenHelper extends OpenHelper {
  onUpgradeDatabase(db: Database,oldVersion: number,newVersion: number): Promise<void> {
    return Promise.resolve()
  }
}