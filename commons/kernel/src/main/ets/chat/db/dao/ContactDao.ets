import { BaseDao, Property } from '@ohos/dataorm'
import { ContactEntity } from '../entity/ContactEntity'
import { GlobalContext } from '@ohos/dataorm';
import { ContactStatus } from '../entity/ContactBaseInfo';
import { Logger, ZPEvents } from '@ohos/utils/Index';
import { common } from '@kit.AbilityKit';
import { MessageStatus } from '../../logic/message/Constants';


export class ContactDao {
  private eventHub: common.EventHub = (getContext(this) as common.UIAbilityContext).eventHub

  private dao: BaseDao<ContactEntity, number>
  private properties: Record<string, Property>

  constructor(dao: BaseDao<ContactEntity, number>) {
    this.dao = dao
    let entityClass = GlobalContext.getContext().getValue(GlobalContext.KEY_CLS) as Record<string, Object>
    this.properties = entityClass['ContactEntity'] as Record<string, Property>
  }

  /**
   * 发出同步联系人的事件
   *
   * TODO：限频（500ms一次）
   */
  private sendSyncAllEvent() {
    this.eventHub.emit(ZPEvents.Contact.SyncAll)
  }

  async upsertAllBaseInfo(contactInfoBeans: Array<ContactEntity>) {
    let count = 0
    for (let item of contactInfoBeans) {
      try {
        let rowId = await this.dao.insert(item)
        Logger.debug(`insert contact ok: rowId=${rowId}, friendId=${item.friendId}, friendSource=${item.friendSource}`)
        count++
      } catch (error) {
        try {
          let dbContact = await this.getByFriendIdAndSource(item.friendId, item.friendSource)
          if (dbContact) {
            item.id = dbContact.id
            await this.dao.updateAsync(item)
            Logger.debug(`update contact ok: friendId=${item.friendId}, friendSource=${item.friendSource}`)
            count++
          }
        } catch (error) {
          Logger.error(`upsert contact error: friendId=${item.friendId}, friendSource=${item.friendSource}, ${JSON.stringify(error)}}`)
        }
      }
    }

    if (count > 0) {
      this.sendSyncAllEvent()
    }
  }

  /**
   * 更新联系人全量数据：本地 + 服务端 (Full + Base)
   */
  async upsert(contact: ContactEntity): Promise<void> {
    let rowId = await this.dao.saveInTxAAsync(contact)
    Logger.debug(`upsert friendSource = ${contact.friendSource}, friendId = ${contact.friendId}, rowId = ${rowId}`)
    this.sendSyncAllEvent()
  }

  async updateLastMessageState(friendId: number, friendSource: number, state: number) {
    await this.dao.execSql('UPDATE Contact SET local_msgState=? WHERE friendId=? AND friendSource=?', [state, friendId, friendSource])
    this.sendSyncAllEvent()
  }

  async setUnreadCount(friendId: number, friendSource: number, unreadCount: number) {
    let sql = `UPDATE Contact SET local_noneReadCount=${unreadCount} WHERE friendId=${friendId} AND friendSource=${friendSource}`
    await this.dao.execSql(sql)
    this.sendSyncAllEvent()
  }

  async setAllMessagesRead(friendId: number, friendSource: number, unreadMid: number) {
    let sql = `UPDATE Contact
      SET local_noneReadCount=0, local_msgState = CASE WHEN local_mid=${unreadMid} THEN ${MessageStatus.None} END
      WHERE friendId=${friendId} AND friendSource=${friendSource}`
    await this.dao.execSql(sql)
    this.sendSyncAllEvent()
  }

  async listAll(): Promise<Array<ContactEntity>> {
    let query = this.dao.queryBuilder().where(this.properties['status'].gt(ContactStatus.Del)) // 可用联系人
    return await query.list()
  }

  /**
   * 根据好友ID获取联系人
   */
  async getByFriendIdAndSource(friendId: number, friendSource: number): Promise<ContactEntity |null> {
    let query = this.dao.queryBuilder()
      .where(this.properties['friendId'].eq(friendId))
      .where(this.properties['friendSource'].eq(friendSource))
    let list: Array<ContactEntity> = await query.list()
    return list.length > 0 ? list[0] : null
  }
}