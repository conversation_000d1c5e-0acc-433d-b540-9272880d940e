import { BaseDao, Property, GlobalContext } from '@ohos/dataorm'
import { MessageStatus, MessageType } from '../../logic/message/Constants'
import { MessageEntity } from '../entity/MessageEntity'
import rdb from '@ohos.data.relationalStore'
import { Logger } from '@ohos/utils'


export class MessageDao {
  private dao: BaseDao<MessageEntity, number>

  constructor(dao: BaseDao<MessageEntity, number>) {
    this.dao = dao
  }

  private getProperties() {
    let entityClass = GlobalContext.getContext().getValue(GlobalContext.KEY_CLS) as Record<string, Object>;
    return entityClass['MessageEntity'] as Record<string, Property>;
  }

  public async save(bean: MessageEntity): Promise<number> {
    return await this.dao.insert(bean)
  }

  public async saveList(list: Array<MessageEntity>): Promise<number> {
    if (list.length == 0) {
      return 0
    }

    let count = 0
    for (let entity of list) {
      try {
        Logger.info('insert message ' + JSON.stringify(entity))
        let rowId = await this.dao.insert(entity)
        count++
        Logger.info('insert message success: rowId = ' + rowId)
      } catch (error) {
        Logger.error('insert message failed: error = ' + JSON.stringify(error))
      }
    }

    return count
  }

  public updateMidByCmid(mid: number, cmid: number) {
    return this.dao.execSql('UPDATE Message SET mid=?, smid=? WHERE cmid=?', [mid, mid, cmid])
  }

  public updateStatusByCmid(status: number, cmid: number) {
    return this.dao.execSql('UPDATE Message SET status=? WHERE cmid=?', [status, cmid])
  }

  public updateMidAndStatusByCmid(cmid: number, newMid: number, status: number) {
    return this.dao.execSql(`UPDATE Message SET mid=${newMid}, smid=${newMid}, status=${status} WHERE cmid=${cmid}`)
  }

  public updateStatusAndTimeById(status: number, time: number, id: number) {
    return this.dao.execSql('UPDATE Message SET status=?, time=? WHERE id=?', [status, time, id])
  }

  public async getByCmid(cmid: number): Promise<MessageEntity |null> {
    let properties = this.getProperties()
    let query = this.dao.queryBuilder().where(properties['cmid'].eq(cmid))
    let entities = await query.list()
    return entities.length > 0 ? entities[0] : null
  }

  public async getByMidAndSender(sender: number, friendId: number, friendSource: number, mid: number) {
    let properties = this.getProperties()
    let queryBuilder = this.dao.queryBuilder()
      .where(properties['sender'].eq(sender))
      .where(properties['friendId'].eq(friendId))
      .where(properties['friendSource'].eq(friendSource))
      .where(properties['mid'].eq(mid))
      .limit(1)
    let entities = await queryBuilder.list()
    return entities.length == 1 ? entities[0] : null
  }

  public async countByMid(msgId: number): Promise<number> {
    let cursor: rdb.ResultSet = await this.dao.rawQuery("SELECT count(*) AS total FROM Message WHERE mid=?", [msgId])
    if (cursor && cursor.goToFirstRow()) {
      return cursor.getLong(cursor.getColumnIndex('total'))
    } else {
      return 0
    }
  }

  public async getSingleMaxMid(): Promise<number> {
    let cursor: rdb.ResultSet = await this.dao.rawQuery("SELECT max(mid) AS maxId FROM Message where friendType = ?", [MessageType.Single])
    if (cursor && cursor.goToFirstRow()) {
      return cursor.getLong(cursor.getColumnIndex('maxId'))
    } else {
      return 0
    }
  }

  async listSingleChatMessages(friendUserId: number, friendSource: number): Promise<Array<MessageEntity>> {
    let properties = this.getProperties()
    let query = this.dao.queryBuilder()
      .where(properties['friendId'].eq(friendUserId))
      .where(properties['friendSource'].eq(friendSource))
      .where(properties['friendType'].eq(MessageType.Single))
      .orderDesc(properties['smid'])
      .orderDesc(properties['id'])
      .build()

    let entities = await query.list()

    return entities
  }

  /**
   * 把这个联系人发给我的所有（成功的）消息置为已读
   *
   */
  public async setAllMessageSentToMeRead(friendId: number, friendSource: number, maxMid: number) {
    let sql = `UPDATE Message
      SET status=${MessageStatus.Read}
      WHERE
        sender=${friendId}
        AND friendSource=${friendSource}
        AND status=${MessageStatus.Success}
        AND mid<=${maxMid}`
    return this.dao.execSql(sql)
  }

  public async setAllMessageReadByFromAndTo(senderId: number, friendId: number, friendSource: number, maxMid: number) {
    let sql = `UPDATE Message
      SET status=${MessageStatus.Read}
      WHERE
        sender=${senderId}
        AND friendId=${friendId}
        AND friendSource=${friendSource}
        AND status=${MessageStatus.Success}
        AND mid<=${maxMid}`
    return this.dao.execSql(sql)
  }

  public async countEqStatusGeMid(status: number, senderId: number, friendId: number, friendSource: number, mid: number) {
    let sql = `SELECT count(*) AS total FROM Message
      WHERE
        friendId=${friendId}
        AND friendSource=${friendSource}
        AND sender=${senderId}
        AND status=${status}
        AND mid > ${mid}`
    let cursor: rdb.ResultSet = await this.dao.rawQuery(sql)
    if (cursor && cursor.goToFirstRow()) {
      return cursor.getLong(cursor.getColumnIndex('total'))
    } else {
      return 0
    }
  }
}