import { Columns, ColumnType, NotNull } from '@ohos/dataorm/Index';
import { MessageStatus } from '../../logic/message/Constants';


export class MessageField {
  @Columns({types: ColumnType.num})
  public mid: number = 0

  @Columns({types: ColumnType.num})
  public cmid: number = -1;

  @Columns({types: ColumnType.num})
  public quoteMid: number = 0

  /**
   * 最后一条聊天
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  public summary?: string|null = null

  /**
   * 消息状态
   */
  @Columns({types: ColumnType.num})
  public msgState: number = MessageStatus.None

  @Columns({types: ColumnType.num})
  public chatTime: number = 0
}