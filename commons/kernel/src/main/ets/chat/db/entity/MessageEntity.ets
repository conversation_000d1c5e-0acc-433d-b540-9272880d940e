import { Entity, Columns, ColumnType, Id, NotNull, Index } from '@ohos/dataorm';
import { MessageMediaType, MessageStatus, MessageType } from '../../logic/message/Constants';
import { FriendSource } from './ContactBaseInfo';


@Entity("Message", [{ value: 'friendId, friendSource', unique: false }])
export class MessageEntity {
  @Id({isPrimaryKey: true, autoincrement: true})
  @Columns({types: ColumnType.num})
  public id?: number = undefined

  @Columns({types: ColumnType.num})
  @Index()
  public mid: number = 0 // 消息ID

  @Columns({types: ColumnType.num})
  public friendId: number = 0 // 会话ID：对方的UID 或者 groupId

  @Columns({types: ColumnType.num})
  public friendSource: number = FriendSource.BOSS // 会话来源

  @Columns({types: ColumnType.num})
  public friendType: number = FriendType.Single // 聊天类型

  @Columns({types: ColumnType.num})
  public mediaType: number = MessageMediaType.None // 消息的媒体类型

  @Columns({types: ColumnType.num})
  public msgType: number = MessageType.None // 消息类型

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public securityId?: string|null = null

  @Columns({types: ColumnType.num})
  public sender: number = 0 // 发送者ID

  @Columns({types: ColumnType.num})
  public time: number = 0 // 发送时间

  @Columns({types: ColumnType.num})
  @Index()
  public cmid: number = 0 // 客户端消息id

  @Columns({types: ColumnType.num})
  public smid: number = 0 // sort消息ID

  @Columns({types: ColumnType.num})
  public templateId: number = TemplateId.None // 消息内容模板ID

  @Columns({types: ColumnType.num})
  public status: number = MessageStatus.None // 对方发来的消息只有：成功（送达）、已读、撤回三种状态

  @Columns({types: ColumnType.num})
  public recStatus: number = MessageReceiveStatus.Unread // 消息接收时的状态

  @Columns({types: ColumnType.num})
  public badged: number = BadgeStatus.Badged // 是否记录消息数

  @Columns({types: ColumnType.num})
  public isShow: boolean = false // 是否显示UI

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public extend?: string|null = null // 8.21版本链接卡片需要

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public content?: (string|null) = null // 消息内容

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public headTitle?: (string|null) = null

  @Columns({types: ColumnType.num})
  public bizType: number = 0 // 业务

  @Columns({types: ColumnType.str})
  public bizId?: string|null = null

  @Columns({types: ColumnType.num})
  public taskId: number = 0

  /**
   * 特殊标记位
   *
   * 用低2位表示客户端用户的身份：0-牛人，1-BOSS，2-预留，3-预留
   * 用17位表示是否priority：1-是，0-否
   */
  @Columns({types: ColumnType.num})
  public flag: number = -1

  @Columns({types: ColumnType.str})
  public version?: string|null = null // 协议版本号

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public extMsg?: string|null = null

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public extMapField?: string|null = null // 扩展2

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public extStr?: string|null = null // 扩展1

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public extStr2?: string|null = null // 扩展2

  @NotNull(false)
  @Columns({types: ColumnType.str})
  public extStr3?: string|null = null // 扩展3

  @NotNull(false)
  @Columns({types: ColumnType.blob})
  public data?: Uint8Array|null = null // 扩展字段

  public isBadged() {
    return this.badged != BadgeStatus.NotBadged
  }

  public hasBeenRead() {
    return this.status == MessageStatus.Read
  }

  public setRead() {
    this.status = MessageStatus.Read
  }

  public isWithdrawn() {
    return this.status == MessageStatus.Withdrawn
  }

  /**
   * 聊天框内可以发出的消息
   */
  public canBeSent() {
    return this.mediaType == MessageMediaType.Text
      || this.mediaType == MessageMediaType.Sound
      || this.mediaType == MessageMediaType.Image
      || this.mediaType == MessageMediaType.Sticker
      || (
        this.mediaType == MessageMediaType.Dialog
        // TODO
      )
  }
}

/**
 * 消息内容模板ID
 */
export enum TemplateId {
  None = 0,
  Normal,                 // 正常
  Center,                 // 居中
  CenterGrey,             // 居中灰色
  ZhipinSkill,            // 直聘技巧样式
  ExchangePhoneOrWechat,  // 交换电话或微信样式
  RedEnvelope,            // 红包灰色居中
  IceBreakWord,           // 破冰语
  ReminderSettings,       // 提醒设置
  GeekScheduleInterview,  // 牛人约面试
  NotStored,              // 不入库的文本消息 (和1唯一区别就是不入库)
  BossPresetAnswer = 12,  // BOSS预留回答
}

/**
 * 聊天类型
 */
export enum FriendType {
  None,   // 单聊
  Single, // 单聊
  Group,  // 群聊
}

/**
 * 是否记录未读消息数
 */
export enum BadgeStatus {
  Badged,    // 记录消息数
  NotBadged  // 不计消息数
}

/**
 * 消息接收时的状态
 */
export enum MessageReceiveStatus {
  Unread,   // 未读消息
  Received, // 已接收
  Read      // 已读
}