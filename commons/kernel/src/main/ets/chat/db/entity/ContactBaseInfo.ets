import { Id, Columns, ColumnType, Index } from '@ohos/dataorm';

export class ContactBaseInfo {
  @Id({isPrimaryKey: true, autoincrement: true})
  @Columns({types: ColumnType.num})
  public id?: number = undefined

  /**
   * 好友ID
   */
  @Columns({types: ColumnType.num})
  friendId: number = -1

  /**
   * 好友来源 0:zp 1:dz
   */
  @Columns({types: ColumnType.num})
  friendSource: number = -1

  /**
   * 包含业务含义的加密ID
   */
  @Columns({types: ColumnType.str})
  securityId: string = ''

  /**
   * 好友姓名
   */
  @Columns({types: ColumnType.str})
  name: string = ''

  /**
   * 好友的头像
   */
  @Columns({types: ColumnType.str})
  tinyUrl: string = ''

  /**
   * Boss职位ID
   */
  @Columns({types: ColumnType.num})
  jobId: number = -1

  /**
   * 关联职位source start at 10.04 仅 c端填充该字段
   */
  @Columns({types: ColumnType.num})
  jobSource: number = -1

  /**
   * Boss职位名称
   */
  @Columns({types: ColumnType.str})
  jobName: string = ''

  /**
   * 牛人期望ID
   */
  @Columns({types: ColumnType.num})
  expectId: number = -1

  /**
   * 来源
   */
  @Columns({types: ColumnType.str})
  sourceTitle: string = ''

  /**
   * 该好友是否置顶
   */
  @Columns({types: ColumnType.num})
  isTop: boolean = false

  /**
   * 是否被标记不合适
   */
  @Columns({types: ColumnType.num})
  isRejectUser: boolean = false

  /**
   * 不合适
   */
  @Columns({types: ColumnType.str})
  rejectDesc: string = ''

  /**
   * 金牛标志  0 否 1是
   */
  @Columns({types: ColumnType.num})
  goldGeekStatus: number = -1

  /**
   * 来源类型
   */
  @Columns({types: ColumnType.num})
  itemType: number = -1

  /**
   * 新招呼文案 字段 start at 910
   * wiki 地址:https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=117261682
   */
  @Columns({types: ColumnType.str})
  headline: string = ''

  /**
   * 是否被标记为星标
   */
  @Columns({types: ColumnType.num})
  isStar: boolean = false

  /**
   * 免打扰 1 开启 0 关闭
   *
   * @see <a href="https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=174079943">1014.59 【B/C】聊天列表页抽屉消息管控</a>
   */
  @Columns({types: ColumnType.num})
  noDisturb: number = -1

  /**
   * 被过滤的 7.06 vip职位筛选
   */
  @Columns({types: ColumnType.num})
  isFiltered: boolean = false

  @Columns({types: ColumnType.str})
  filterReasonList: string = ''

  /**
   * 大于1表示该牛人与其他牛人组队求职
   * 1105.85【B&C】提供部分蓝领岗位组队求职功能
   */
  @Columns({types: ColumnType.num})
  teamMemberSize: number = -1

  /**
   * 1106.92 道具来源 是否是道具来源 0否 1 是
   */
  @Columns({types: ColumnType.num})
  itemSource: number = -1

  /**
   * 时间
   */
  @Columns({types: ColumnType.num})
  datetime: number = -1

  /**
   * https://zhishu.zhipin.com/wiki/R7m0kdanM7q
   * 多位 位运算表示 (iconFlag & 1) != 0 则代表top300  后续新增icon 则添加多位 如 2 4 8 16 多位或上
   */
  iconFlag: number = -1

  /**-------------------------------------------以上是公用的-------------------------------------------------------------------**//*
  /**
   * 牛人期望名称
   */
  @Columns({types: ColumnType.str})
  expectPositionName: string = ''


  /**-------------------------------------------以上是BOSS-------------------------------------------------------------------**//*

  /**
   * Boss职务
   */
  @Columns({types: ColumnType.str})
  title: string = ''

  /**
   * Boss认证状态
   */
  @Columns({types: ColumnType.num})
  certification: number = -1

  /**
   * Boss所属品牌的名称
   */
  @Columns({types: ColumnType.str})
  company: string = ''

  /**
   * 是否是猎头
   */
  @Columns({types: ColumnType.num})
  isHeadhunter: boolean = false

  /**
   * 是否高端牛人免打扰 7.18需求
   */
  @Columns({types: ColumnType.num})
  highGeek: boolean = false

  /**
   * 职类名称
   */
  @Columns({types: ColumnType.str})
  positionName: string = ''

  /**
   * 薪资待遇
   */
  @Columns({types: ColumnType.str})
  salaryDesc: string = ''

  /**
   * 工作地点
   */
  @Columns({types: ColumnType.str})
  workplace: string = ''

  /**
   * 720 代招
   */
  @Columns({types: ColumnType.num})
  isAgentRecruit: boolean = false

  /**
   * 是否是猎头（仅适用于猎头/中介开关的区分）
   */
  @Columns({types: ColumnType.num})
  isHunter: boolean = false

  /**
   * 是否是中介（仅适用于猎头/中介开关的区分）
   */
  @Columns({types: ColumnType.num})
  isAgency: boolean = false

  @Columns({types: ColumnType.num})
  invalidJob: number = -1 //职位状态 0 正常 1停止招聘 c端逻辑

  /*==================================Local=====================================*/
  /**
   * <p>
   * {@link Constants#STATUS_NORMAL
   */
  @Columns({types: ColumnType.num})
  @Index()
  status: number = ContactStatus.Normal

  /**
   * 接口更新，插入或者更新时间
   */
  @Columns({types: ColumnType.num})
  createdAt: number = Date.now()
  /*==================================Local=====================================*/

  /**
   * 人才经纪人
   */
  public isRecruiter(): boolean {
    return this.isAgentRecruit || this.isHeadhunter
  }
}

/**
 * 好友来源
 */
export enum FriendSource {
  BOSS,
  DIAN_ZHANG
}

/**
 * 联系人状态
 */
export enum ContactStatus {
  Del = -1, // 删除
  Normal,   // 正常
  Hide,     // 隐藏
  Rejected  // 拉黑
}

/**
 * 金牛标志
 */
export enum GoldGeekStatus {
  NotGoldGeek, // 非金牛
  GoldGeek     // 金牛
}

/**
 * 职位状态
 */
export enum JobStatus {
  Normal, // 正常
  Closed, // 停止招聘 c端逻辑
}
