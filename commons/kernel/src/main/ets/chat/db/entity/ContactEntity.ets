import { Embedded, Entity } from '@ohos/dataorm';
import { TextUtils } from '@ohos/utils/Index';
import { ContactFullInfo } from './ContactFullInfo';
import { ContactLocalInfo } from './ContactLocalInfo';

@Entity("Contact", [{ value: 'friendId, friendSource', unique: true }])
export class ContactEntity extends ContactFullInfo {
  @Embedded({prefix: 'local_', targetClass: ContactLocalInfo})
  localInfo: ContactLocalInfo = new ContactLocalInfo()

  /**
   * 获得当前联系人的分组类型
   *
   * @return GroupType
   */
  getGroupType(): number {
    //双聊
    if (this.localInfo.haveContactedEachOther()) {
      //有面试 约了面试且接受的联系人，不包括尚未接受和超时未接受和取消的情况；
      if (this.interviewStatus == 1
        || this.interviewStatus == 4
        || this.interviewStatus == 7
        || this.interviewStatus == 17) {
        return GroupType.HAVE_INTERVIEW;
      }
      //交换信息 有达成行为（简历、电话、微信）的联系人中， 除掉有面试的；
      if (!TextUtils.isEmpty(this.phoneNumber)
        || !TextUtils.isEmpty(this.wxNumber)
        || !TextUtils.isEmpty(this.resumeUrl)
        || this.exchangeResumeStatus == ExchangeResumeStatus.Exchanged) {
        return GroupType.EXCHANGE_MESSAGE;
      }
    }

    //仅沟通 我主动沟通 + 双聊联系人中， 除掉有交换、有面试、有录用的
    if (this.localInfo.haveSentMsgToFriend() || this.localInfo.haveContactedEachOther()) {
      return GroupType.ONLY_CONTACT;
    }
    //新招呼 对方发起的新招呼（与现有逻辑一致）//////  (fridendStage == 0 && lastChatTime > 0)  兜底处理
    let lastChatTime = this.localInfo.messageField.chatTime
    if (this.localInfo.hasSentMsgToMe() || (this.localInfo.haveNotContacted() && lastChatTime > 0)) {
      return GroupType.NEW_GREETING;
    }

    return GroupType.ALL;
  }

  getShowTime() {
    let showTime: number = 0;

    let lastChatTime: number = this.localInfo.messageField.chatTime
    let updateTime = this.datetime
    if (lastChatTime > 0) {
      showTime = lastChatTime;
    } else if (updateTime > 0) {
      showTime = updateTime;
    }

    return showTime;
  }

  isGrayUnread(): boolean {
    if (this.isTop) {
      return false
    }

    return this.isWeekAgeGray()
  }

  isWeekAgeGray(): boolean {
    let showTime = this.getShowTime();
    if (showTime <= 0) { //产品要求 没有时间 不算 一周前的
      return false;
    }
    // TODO
    // return NotifyUtils.notifyDownplayingMessage() && LDate.isWeekAge(showTime);
    return false
  }

  setIsContactHelperState(add: boolean) {
    this.localInfo.setIsContactHelperState(add)
  }

  getAvatarIndex() {
    return this.localInfo.avatarIndex
  }

  getUnreadCount(): number {
    return this.localInfo.noneReadCount
  }

  incUnreadCount(inc: number) {
    this.localInfo.noneReadCount += inc
  }

  getLastCmid() {
    return this.localInfo.messageField.cmid
  }

  getLastMid() {
    return this.localInfo.messageField.mid
  }

  haveContactedEachOther() {
    return this.localInfo.haveContactedEachOther()
  }

  isInitiativeConservation() {
    return this.localInfo.isInitiativeConservation()
  }

  isPassiveConservation() {
    return this.localInfo.isPassiveConservation()
  }
}

/**
 * 交换附件简历状态
 */
enum ExchangeResumeStatus {
  NotExchanged, // 未交换
  Exchanged     // 已交换
}

/**
 * 联系人分组类型
 */
export enum GroupType {
  ALL = 0,
  NEW_GREETING,
  ONLY_CONTACT,
  EXCHANGE_MESSAGE,
  HAVE_INTERVIEW
}