export class LocalField {
  /*联系人字段*/
  directCallStatus?: number // 1 电话未接通  2 未接电话  3 通话成功
  exchangePhoneTime?: number // 申请交换电话的时间
  exchangeWxNumberTime?: number // 申请交换微信号的时间
  exchangeAnnexResumeTime?: number // 交换附件简历的时间
  exchangeInterviewTime?: number // 面试邀请的时间
  hasJumpToChat?: boolean // 消息列表是否已经显示过面试状态【面试超时】【面试取消】  currentInterviewStatus
  lastRefreshTime?: number
  videoInterviewShortMsg?: string // 预约14：00视频
  videoInterviewLongMsg?: string // 预约14：00进行视频面试
  lid?: string
  switch1?: number
  messageType17Title?: string
  interviewScheduleStatus?: number
  interviewScheduleProtocol?: string
  interviewScheduleTime?: number

  /*联系人字段*/

  /*群聊字段*/
  atFromMessageId?: number
  fromSource?: string //该字段接口没有返回，只依赖action消息->小尾巴
  /*群聊字段*/
}

/**
 * 通话状态
 */
export enum CallStatus {
  NotAnswered = 1, // 电话未接通
  Missed,          // 未接电话
  Successful       // 通话成功
}