import { ContactBaseInfo } from './ContactBaseInfo';
import {WarningTipsBean} from '@ohos/zp_api';
import { Logger, TextUtils } from '@ohos/utils'

import { NotNull, Columns, ColumnType, Convert, PropertyConverter } from '@ohos/dataorm';


export class WarningTipsBeanConvert extends PropertyConverter<Array<WarningTipsBean>, string> {
  convertToEntityProperty(databaseValue: string): Array<WarningTipsBean> {
    let result: Array<WarningTipsBean> = new Array()
    try {
      let jsonArray: Array<WarningTipsBean> = JSON.parse(databaseValue) as Array<WarningTipsBean>
      jsonArray.forEach(jsonObj => result.push(new WarningTipsBean(jsonObj.type, jsonObj.content, jsonObj.exposureAction)))
    } catch (e) {
      Logger.error('WarningTipsBeanConvert err: ' + JSON.stringify(e))
    }
    return result;
  }

  convertToDatabaseValue(entityProperty: Array<WarningTipsBean>): string {
    return JSON.stringify(entityProperty);
  }
}

const SERVER_LABEL_SPLITTER = "#&#" // 服务器约定的分隔符：#&#
const LOCAL_LABEL_SPLITTER = ","    // 客户端本地约定的分隔符：","

const LABEL_SPLITTERS = [SERVER_LABEL_SPLITTER, LOCAL_LABEL_SPLITTER]

export class ContactFullInfo extends ContactBaseInfo {
  /**
   * 好友的手机
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  phoneNumber: string = ''

  /**
   * 境外号码
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  regionCode: string = ''

  @NotNull(false)
  @Convert({converter: WarningTipsBeanConvert, columnType: ColumnType.str})
  warningTipsBeans: Array<WarningTipsBean> = []

  /**
   * 金牌面试官
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  goldInterviewer: string = ''

  /**
   * 好友的微信
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  wxNumber: string = ''

  /**
   * 是否被拉黑
   */
  @Columns({types: ColumnType.num})
  // @Convert({converter: BooleanToIntConverter, columnType: ColumnType.num})
  isBlack: boolean = false

  /**
   * 是否已冻结
   */
  @Columns({types: ColumnType.num})
  // @Convert({converter: BooleanToIntConverter, columnType: ColumnType.num})
  isFreeze: boolean = false

  /**
   * 面试状态
   */
  @Columns({types: ColumnType.num})
  interviewStatus: number = -1

  @NotNull(false)
  @Columns({types: ColumnType.str})
  interviewStatusDesc: string = ''

  @NotNull(false)
  @Columns({types: ColumnType.str})
  interviewUrl: string = ''

  @NotNull(false)
  @Columns({types: ColumnType.str})
  interviewDateStr: string = ''

  @NotNull(false)
  @Columns({types: ColumnType.str})
  interviewTimeStr: string = ''

  /**
   * 交换附件简历状态 0 未交换，1 交换
   */
  @Columns({types: ColumnType.num})
  exchangeResumeStatus: number = 0

  @NotNull(false)
  @Columns({types: ColumnType.str})
  resumeUrl: string = ''

  /**
   * 电话预授权状态 (c对b预授权)0 禁用 1.待授权 2.已授权
   */
  @Columns({types: ColumnType.num})
  phoneAuthStatus: number = 0

  /**
   * 微信预授权状态 (c对b预授权) 0 禁用 1.待授权 2.已授权
   */
  @Columns({types: ColumnType.num})
  weiXinAuthStatus: number = 0

  /**
   * 电话预授权状态 (b对c预授权)0 禁用 1.待授权 2.已授权
   */
  @Columns({types: ColumnType.num})
  bPhoneAuthStatus: number = 0

  /**
   * 微信预授权状态 (b对c预授权) 0 禁用 1.待授权 2.已授权
   */
  @Columns({types: ColumnType.num})
  bWeiXinAuthStatus: number = 0

  /**
   * 简历预授权状态 （c预授权）0 禁用 1.待授权 2.已授权
   */
  @Columns({types: ColumnType.num})
  resumeAuthStatus: number = 0

  /**
   * 拉黑原因描述7.13新增
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  blackDesc: string = ''

  /**
   * 8.05 add
   */
  @Columns({types: ColumnType.num})
  // @Convert({converter: BooleanToIntConverter, columnType: ColumnType.num})
  isWxRemind: boolean = false

  /**
   * 视频简历跳转地址  8.06
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  videoResumeUrl: string = ''

  /**
   * 兼职安全策略-交换微信样式 0不收起 1收起
   */
  @Columns({types: ColumnType.num})
  partJobStrategy: number = 0

  /**
   * 加好友时间
   */
  @Columns({types: ColumnType.num})
  addTime: number = 0

  /**
   * 最后一次删除好友时间
   */
  @Columns({types: ColumnType.num})
  delTime: number = 0

  /**
   * 好友备注 仅c填充
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  note: string = ''

  /**
   * 好友标签 仅c填充
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  labels: string = ''

  @NotNull(false)
  @Columns({types: ColumnType.str})
  starTypes: string = ''// 收藏类型列表 1 职位收藏 2 boss收藏 3 公司收藏

  @Columns({types: ColumnType.num})
  isPreFreeze: boolean = false //是否预冻结，目前只有合规冻结

  @NotNull(false)
  @Columns({types: ColumnType.str})
  freezeInfo: string = '' // 冻结文案

  @NotNull(false)
  @Columns({types: ColumnType.str})
  preFreezeInfo: string = '' // 预冻结文案

  @Columns({types: ColumnType.str})
  workYear: string = '' //工作年限 xxx年 如果是学生 该字段为xx应届生等信息

  @Columns({types: ColumnType.str})
  degree: string = '' //学历

  @Columns({types: ColumnType.str})
  expectSalary: string = '' //期望薪资

  @Columns({types: ColumnType.str})
  workDesc: string = '' //牛人最后一份工作经历 如果是学生 填学校信息

  getLabelArray(): string[] {
    if (TextUtils.isEmpty(this.labels)) {
      return []
    }

    for (let splitter of LABEL_SPLITTERS) {
      if (this.labels.includes(splitter)) {
        return this.labels.split(splitter)
      }
    }

    return [this.labels]
  }
}
