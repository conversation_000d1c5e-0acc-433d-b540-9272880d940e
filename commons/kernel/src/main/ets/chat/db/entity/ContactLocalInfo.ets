import { Columns, ColumnType, Embedded, NotNull, Transient } from '@ohos/dataorm/Index';
import { LocalField } from './LocalField';
import { MessageField } from './MessageField';

/**
 * 联系人本地数据
 */
export class ContactLocalInfo {
  /**
   * 默认头像index
   */
  @Columns({types: ColumnType.num})
  public avatarIndex: number = 0

  /**
   * 未读数
   */
  @Columns({types: ColumnType.num})
  public noneReadCount: number = 0

  /**
   * 0为默认,未发送过消息
   * 1 好友发来消息
   * 2 我发送
   */
  @Columns({types: ColumnType.num})
  public friendStage: number = 0

  /**
   * 公司
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  public friendCompanies: string|null = null

  /**
   * 草稿
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  public draft: string|null = null

  /**
   * 联系人消息的交换状态 618
   */
  @Columns({types: ColumnType.num})
  public exchangeType: number = 0

  /**
   * 适合存储 boolean 类型的数据
   */
  @Columns({types: ColumnType.num})
  public stateType: number = 0

  /**
   * 拓展参数， 不需要直接查询的
   */
  @NotNull(false)
  @Columns({types: ColumnType.str})
  public extStr: string|null = null

  @Columns({types: ColumnType.num})
  public updateTime: number = 0

  @Embedded({prefix: 'local_', targetClass: MessageField})
  public messageField: MessageField = new MessageField() // 最后一条消息的信息

  /**
   * 拓展数据：转成Json存入extStr
   */
  @Transient()
  private localField?: LocalField

  getLocalField() {
    if (!this.localField && this.extStr) {
      this.localField = JSON.parse(this.extStr);
    }
    return this.localField;
  }

  private isStateType(stateType: number): boolean {
    return (this.stateType & stateType) != 0;
  }

  private setStateType(stateType: number, add: boolean) {
    if (add) {
      this.stateType |= stateType
    } else {
      this.stateType &= ~stateType
    }
  }

  isContactHelperState() {
    return this.isStateType(StateType.IS_CONTACT_HELPER)
  }

  isPlatformResearchState() {
    return this.isStateType(StateType.IS_PLAT_FROM_RESEARCH)
  }

  isAts() {
    return this.isStateType(StateType.IS_ATS)
  }

  setIsContactHelperState(add: boolean) {
    this.setStateType(StateType.IS_CONTACT_HELPER, add)
  }

  setIsReceiveFriendGreetMsgState(add: boolean) {
    this.setStateType(StateType.IS_RECEIVE_FRIEND_GREET_MSG, add)
  }

  private isInFriendStage(stage: number) {
    return (this.friendStage & stage) == stage
  }

  /**
   * 设置好友的状态
   */
  private addFriendStage(stage: number) {
    this.friendStage |= stage
  }

  haveNotContacted(): boolean {
    return this.isInFriendStage(FriendStage.Unknown)
  }

  /**
   * 我是否给好友发送过文本、语音、图片消息
   */
  haveSentMsgToFriend(): boolean {
    return this.isInFriendStage(FriendStage.HaveSentMessageToFriend)
  }

  setHaveSentToFriend() {
    this.addFriendStage(FriendStage.HaveSentMessageToFriend)
  }

  /**
   * 是否双聊状态
   */
  haveContactedEachOther(): boolean {
    return this.isInFriendStage(FriendStage.HaveContactedEachOther)
  }

  setHaveContactedEachOther() {
    this.addFriendStage(FriendStage.HaveContactedEachOther)
  }

  /**
   * 好友是否给我发送过文本、语音、图片消息
   */
  hasSentMsgToMe(): boolean {
    return this.isInFriendStage(FriendStage.HasSentMessageToMe)
  }

  setHasSentMsgToMe() {
    this.addFriendStage(FriendStage.HasSentMessageToMe)
  }

  // 主动开聊：我给好友发送过消息 & 不是双聊
  isInitiativeConservation(): boolean {
    return !this.haveContactedEachOther() && this.haveSentMsgToFriend()
  }

  // 被动开聊：好友给我发送过消息 & 不是双聊
  isPassiveConservation(): boolean {
    return !this.haveContactedEachOther() && !this.haveSentMsgToFriend()
  }
}

/**
 * 交换类型
 */
export enum ExchangeType {
  SINGLE_CHAT_FROM_FRIEND = 1,
  ASK_RESUME,
  ASK_WEI_XIN,
  ASK_PHONE,
  BOSS_ASK_GEEK_INTERVIEW, //B约C面试
  NONE,
  CHAT_WITH_EACH_OTHER_FROM_FRIEND
}

enum StateType {
  IS_CONTACT_HELPER = 0,                // 809版本新增沟通助手 只有Boss身份有
  IS_RECEIVE_FRIEND_GREET_MSG = 1,      // 接受到好友的新招呼信息
  IS_PLAT_FROM_RESEARCH = 1 << 2,       // 平台调研
  IS_ATS = 1 << 3,                      // 校招ATS
  IS_IMPROVE_MESSAGE_EXPOSURE = 1 << 4, // 提升消息曝光
  IS_GROUP_FROM_SOURCE = 1 << 5         // 好友是否回复,引力波需要筛选
}

/**
 * 好友状态（聊天状态）
 */
enum FriendStage {
  Unknown = 0,
  HasSentMessageToMe = 0x0001,      // 新招呼
  HaveSentMessageToFriend = 0x0002, // 仅沟通
  HaveContactedEachOther = 0x0004   // 双聊状态
}