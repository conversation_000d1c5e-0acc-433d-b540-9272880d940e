import { TextUtils } from '@ohos/utils/Index';
import { TextMessage } from '../../../../Index';
import { ContactEntity } from './db/entity/ContactEntity';
import { TemplateId } from './db/entity/MessageEntity';
import { MessageMediaType, MessageStatus } from './logic/message/Constants';
import { ChatMessage, ChatMessageBodyType } from './logic/message/model/ChatMessage';

export class ContactUtils {
  /**
   * 获取单聊联系人的最后一条消息文本
   */
  static getSingleChatLastText(contact: ContactEntity, message: ChatMessage<ChatMessageBodyType>, isFromRevocation: boolean): string|null|undefined {
    let contactLastChatText = contact.localInfo.messageField.summary
    if (!message.isNeedToSave()) {
      return contactLastChatText
    }

    if (message.templateId == TemplateId.BossPresetAnswer && message.isSentFromMe()) {
      contact.setIsContactHelperState(message instanceof TextMessage)
    }

    // 撤回消息：如果是聊天的最后一条消息，不显示消息状态
    if (isFromRevocation) {
      // 判断撤回的是否是聊天里最后一条消息
      if (contact.localInfo.messageField.cmid == message.cmid) {
        contact.localInfo.messageField.msgState = MessageStatus.None // F2撤回消息不显示送达状态
        return message.getDescription(contactLastChatText);
      } else {
        return contactLastChatText
      }
    }

    return message.getDescription(contactLastChatText);
  }

  static updateContactByMessage(contact: ContactEntity, msg: ChatMessage<ChatMessageBodyType>) {
    if (TextUtils.isNotEmpty(msg.friendName)) {
      contact.name = msg.friendName ?? ''
    }

    // 显示新招呼
    if (msg.showNewGreet()) {
      contact.localInfo.setIsReceiveFriendGreetMsgState(true)
    }

    if (msg instanceof TextMessage) {
      let textMsg = msg as TextMessage
      let phone = textMsg.getPhone()
      if (TextUtils.isNotEmpty(phone)) {
        contact.phoneNumber = phone ?? ''
      }

      let reginCode = textMsg.getPhoneRegionCode()
      if (TextUtils.isNotEmpty(reginCode)) {
        contact.regionCode = reginCode ?? ''
      }

      let wechat = textMsg.getWechatNumber()
      if (TextUtils.isNotEmpty(wechat)) {
        contact.wxNumber = wechat ?? ''
      }
    }

    if (msg.getMediaType() != MessageMediaType.Action) {
      // 更新聊天状态
      if (msg.isTwoWayChatSupported()) {
        if (msg.isSentToMe()) {
          contact.localInfo.setHasSentMsgToMe()
          if (!contact.localInfo.haveContactedEachOther() && contact.localInfo.haveSentMsgToFriend()) {
            contact.localInfo.setHaveContactedEachOther()
          }
        } else {
          contact.localInfo.setHaveSentToFriend()
          if (!contact.localInfo.haveContactedEachOther() && contact.localInfo.hasSentMsgToMe()) {
            contact.localInfo.setHaveContactedEachOther()
          }
        }
      }

      // 更新未读消息数量
      if (msg.isSentToMe() && msg.isBadged() && !msg.isWithdrawn() && !msg.hasBeenRead()) {
        contact.incUnreadCount(1)
      }

      if (msg.isNeedUpdateContactLastMessage()) {
        if (contact.localInfo.messageField.mid < msg.mid) {
          contact.localInfo.messageField.mid = msg.mid
          contact.localInfo.messageField.chatTime = msg.time
          contact.datetime = msg.time
          contact.localInfo.messageField.summary = ContactUtils.getSingleChatLastText(contact, msg, false)
        }

        if (msg.isSentToMe()) {
          contact.localInfo.messageField.msgState = MessageStatus.None
          contact.localInfo.messageField.cmid = -1
        } else {
          contact.localInfo.messageField.msgState = msg.hasSendStatus() ? msg.status : MessageStatus.None
          contact.localInfo.messageField.cmid = msg.cmid
        }
      }
    }

    // 更新securityId
    if (TextUtils.isEmpty(contact.securityId) && TextUtils.isNotEmpty(msg.securityId)) {
      contact.securityId = msg.securityId ?? ''
    }
  }
}