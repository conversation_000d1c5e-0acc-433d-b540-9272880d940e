import common from '@ohos.app.ability.common';
import {Sender<PERSON><PERSON><PERSON>} from './SenderHandler';
import AbstractSpRepo from './AbstractSpRepo';
import { Logger } from '@ohos/utils';
import { <PERSON><PERSON> } from '../Kernel';


const MESSAGE_MAX_ID: string = 'MESSAGE_MAX_ID'

export default class MessageRepository extends AbstractSpRepo {
  private static instance: MessageRepository
  private spFileName: string = ''

  getName(): string {
    return this.spFileName
  }

  private constructor(context: common.Context) {
    super(context)
    let account = Kernel.getInstance().getAccount()
    if (account) {
      this.spFileName = [account.uid, account.identity, 'msg_max_file'].join('_');
      this.getSharedPreferences()
    } else {
      throw new Error('Not logged in')
    }
  }

  public static getInstance(): MessageRepository  {
    if (!MessageRepository.instance) {
      let context = AppStorage.get<common.Context>('context')
      if (context) {
        MessageRepository.instance = new MessageRepository(context)
      }
    }

    return MessageRepository.instance
  }

  public async getMaxMessageId(): Promise<number> {
    return await this.getValue(MESSAGE_MAX_ID, -1)
  }

  public async saveMaxMessageId(mustSave: boolean, id: number) {
    let tempId = await this.getMaxMessageId();
    Logger.info(`saveMaxMessageId current maxId: ${tempId}, setId: ${id}, ps: ${SenderHandler.mPresenceMessageSendSuccess}, ms: ${mustSave}`)
    if (mustSave) {
      await this.putMaxMessageId(id)
    } else {
      if (SenderHandler.mPresenceMessageSendSuccess) {
        if (id > tempId) {
          await this.putMaxMessageId(id)
        }
      }
    }
  }

  // 保存最大的消息ID
  private async putMaxMessageId(id: number) {
    if (id < 0) id = 0;
    await this.putValue(MESSAGE_MAX_ID, id)
    await this.sharedPreferences.flush()
  }
}