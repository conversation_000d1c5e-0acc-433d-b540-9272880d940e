import { IReceiveListener } from '../mqtt/MqttConfig';
import Protocol from './pb/chatprotocol'
import { MqttConfig } from './MqttConfig';
import MessageDecoder from './MessageDecoder';
import { SingleChatMessageHandler } from './messageHandler/SingleChatMessageHandler';
import { ChatBean } from './logic/ChatBean';
import RResponseHandler from './messageHandler/RResponseHandler';
import RHistoryMessageHandler from './messageHandler/RHistoryMessageHandler';
import RMessageSyncHandler from './messageHandler/RMessageSyncHandler';
import { Logger } from '@ohos/utils';
import { Kernel } from '../Kernel';
import { ChatMessage, ChatMessageBodyType } from './logic/message/model/ChatMessage';
import { MessageFactory } from './MessageFactory';
import { FriendType } from './db/entity/MessageEntity';
import { MidSyncMessage } from './logic/message/model/MidSyncMessage';
import { ReadMessage } from './logic/message/model/ReadMessage';
import { ReadMessageHandler } from './messageHandler/ReadMessageHandler';


export default class ReceiverHandler implements IReceiveListener {
  private singleChatMessageHandler: SingleChatMessageHandler = new SingleChatMessageHandler()
  private mResponseHandler: RResponseHandler
  private mSyncHandler: RMessageSyncHandler = new RMessageSyncHandler()
  private readMsgHandler = new ReadMessageHandler()

  constructor() {
    this.mResponseHandler = new RResponseHandler(new RHistoryMessageHandler(this))
  }

  onReceive(playdata: ArrayBuffer): void {
    let uint8Array = new Uint8Array(playdata);
    try {
      let protocol = Protocol.TechwolfChatProtocol.decode(uint8Array);
      Logger.info("protocol = [%s]", JSON.stringify(protocol));

      let account = Kernel.getInstance().getAccount()
      if (!account) {
        return
      }

      let uid = account.uid
      let role = account.identity

      switch (protocol.type) {
        case MqttConfig.CHAT_TYPE_MESSAGE:
          this.handleMessage(protocol.messages, protocol.domain)
          break

        case MqttConfig.CHAT_TYPE_IQ_RESPONSE:
          let iqResponseTempBean = new ChatBean()
          let iqResponseMessageList: Array<ChatBean>|null = MessageDecoder.toIqResponse(protocol, iqResponseTempBean);
          this.mResponseHandler.handle(protocol.domain, iqResponseTempBean, iqResponseMessageList, uid, role);
          break

        case MqttConfig.CHAT_TYPE_SEND_MESSAGE_ID_RESPONSE:
          if (protocol.messageSync.length > 0) {
            let midRespMessages: MidSyncMessage[] = []
            for (let pbMsg of protocol.messageSync) {
              let msg = new MidSyncMessage()
              msg.fromProtobuf(pbMsg)
              msg.friendType = protocol.domain
              midRespMessages.push(msg)
            }
            this.mSyncHandler.handle(midRespMessages)
          }
          break

        case MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ:
          if (protocol.messageRead.length > 0) {
            let readMsgs: ReadMessage[] = []
            for (let pbMsg of protocol.messageRead) {
              let msg = new ReadMessage()
              msg.fromProtobuf(pbMsg)
              msg.friendType = protocol.domain
              readMsgs.push(msg)
            }
            this.readMsgHandler.handle(readMsgs)
          }
          break

        case MqttConfig.CHAT_TYPE_DATA_SYNC:
          break

        default:
          Logger.info("接收到一条未知类型消息");
          break;
      }
    } catch (e) {
      Logger.error("handle push msg error=" + JSON.stringify(e))
    }
  }

  handleMessage(pbMessages: Protocol.ITechwolfMessage[], domain: number) {
    let messageList = new Array<ChatMessage<ChatMessageBodyType>>()
    for (let pbMessage of pbMessages) {
      let message = MessageFactory.createChatMessage(pbMessage.body.type)
      if (message) {
        message.fromProtobuf(pbMessage)
        message.friendType = domain
        messageList.push(message)
      }
    }

    if (domain == FriendType.Single || domain == FriendType.None) { // 单聊
      this.singleChatMessageHandler.handle(messageList, true)
    } else { // 群聊
    }
  }
}