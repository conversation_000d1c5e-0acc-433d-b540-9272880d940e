import {ChatDataBase} from '../db/ChatDataBase'
import {ContactDao} from '../db/dao/ContactDao'
import { MessageDao } from '../db/dao/MessageDao'


export class DatabaseService {
  private static instance: DatabaseService

  private chatDataBase: ChatDataBase|null = null

  private constructor() {
    this.chatDataBase = ChatDataBase.getInstance()
  }

  public static getInstance() {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  getContactDao(): Promise<ContactDao|null> {
    if (!this.chatDataBase) {
      return Promise.resolve(null)
    } else {
      return this.chatDataBase.getContactDao()
    }
  }

  getMessageDao(): Promise<MessageDao|null> {
    if (!this.chatDataBase) {
      return Promise.resolve(null)
    } else {
      return this.chatDataBase.getMessageDao()
    }
  }
}