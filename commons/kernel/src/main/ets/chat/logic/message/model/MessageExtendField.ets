import { Long } from '@ohos/protobufjs'
import { TextUtils } from '@ohos/utils/Index'
import { FriendSource } from '../../../db/entity/ContactBaseInfo'
import { MqttConfig } from '../../../MqttConfig'
import Protocol from '../../../pb/chatprotocol'
import { IProtobufConverter } from '../IProtobufConverter'


export class MessageExtendField {
  /**
   * 颜色风格
   */
  style: number = 0

  /**
   * 引用消息mid
   */
  quoteId: number|null = null

  constructor(style: number=0, quoteId: (number|null)=null) {
    this.style = style
    this.quoteId = quoteId
  }
}

export class MessageUser implements IProtobufConverter<Protocol.ITechwolfUser>{
  uid?: (number|Long)
  headImg?: (number|null)
  name?: (string|null)
  avatar?: (string|null)
  company?: (string|null)
  source?: (number|null)
  certification?: (number|null)

  public fromProtobuf(techwolfUser: Protocol.ITechwolfUser): MessageUser|null {
    if (techwolfUser != null) {
      let messageUser: MessageUser = new MessageUser();
      messageUser.avatar = techwolfUser.avatar
      messageUser.name = techwolfUser.name
      messageUser.headImg = techwolfUser.headImg
      messageUser.company = techwolfUser.company
      messageUser.uid = techwolfUser.uid
      messageUser.source = techwolfUser.source
      messageUser.certification = techwolfUser.certification
      return messageUser;
    }
    return null;
  }

  public toProtobuf(): Protocol.TechwolfUser {
    let user: Protocol.TechwolfUser = Protocol.TechwolfUser.create()
    user.uid = this.uid == 1000 ? MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID : this.uid
    if (this.name && !TextUtils.isEmpty(this.name)) {
      user.name = this.name
    }
    user.source = this.source ?? FriendSource.BOSS

    return user
  }
}