import { FriendType, MessageEntity, TemplateId } from '../../../db/entity/MessageEntity'
import { MessageExtendField } from './MessageExtendField'
import { MessageLocalField } from './MessageLocalField'
import Protocol from '../../../pb/chatprotocol'
import { Kernel } from '../../../../Kernel'
import { FriendSource } from '../../../db/entity/ContactBaseInfo'
import { TextUtils } from '@ohos/utils/Index'
import { MessageMediaType, MessageStatus, MessageType } from '../Constants'
import { MqttConfig, ReceivedMessageStatus } from '../../../MqttConfig'
import { ProtobufMessage } from './ProtobufMessage'


export declare type ChatMessageBodyType = Object|null

export abstract class ChatMessage<T extends ChatMessageBodyType> extends ProtobufMessage<Protocol.ITechwolfMessage> {
  messageBody?: T
  soundUri?: (string|null)
  isOffline?: (boolean|null)
  pushText?: (string|null)
  friendName?: (string|null)

  /**
   * 本地拓展字段
   */
  localField?: MessageLocalField

  /**
   * 消息 基础 其他字段
   */
  extendField?: MessageExtendField

  private message: MessageEntity

  constructor() {
    super()
    this.message = new MessageEntity()
    this.message.mediaType = this.getMediaType()
  }

  getMessageType(): number {
    return MqttConfig.CHAT_TYPE_MESSAGE
  }

  abstract getMediaType(): number
  abstract parseProtobufMessageBody(messageBody: Protocol.ITechwolfMessageBody)
  abstract getProtobufMessageBody(): Protocol.ITechwolfMessageBody

  /**
   * 是否需要更新联系人最后一条消息
   */
  abstract isNeedUpdateContactLastMessage(): boolean

  /**
   * 是否支持双聊
   */
  abstract isTwoWayChatSupported(): boolean

  /**
   * 是否可以显示新招呼
   * @returns
   */
  abstract showNewGreet(): boolean

  /**
   * 是否有发送状态
   */
  abstract hasSendStatus(): boolean

  abstract getSummary(): string|null|undefined

  private getCardLastChatText(name: string): string {
    let account = Kernel.getInstance().getAccount()
    return !account ? '' : "您正在与" + (account.isBoss() ? "牛人" : "Boss") + name + "沟通"
  }

  /**
   * 获取消息描述
   */
  public getDescription(contactLastChatText?: string |null): string |null |undefined {
    // 加密消息提示
    if (this.isEncrypted()) {
      return '您收到了来自牛人的消息'
    }

    // 撤回消息在f2展示的文案
    if (this.status == MessageStatus.Withdrawn) {
      // TODO
    }

    // 如果是系统消息，不在f2消息列表显示
    if (this.msgType == MessageType.SystemNotShownInF2) {
      return TextUtils.isEmpty(contactLastChatText) ? this.getCardLastChatText(this.getFriendName() ?? '') : contactLastChatText
    }

    let desc = this.getSummary() ?? ''

    // 使用title
    if (TextUtils.isEmpty(desc) && !TextUtils.isEmpty(this.headTitle)) {
      desc = this.headTitle ?? ''
    }

    // 群聊 & 别人发的消息 显示发送者名字
    // TODO: 群聊
    if (!TextUtils.isEmpty(desc) && this.isSentToMe() && this.isGroup()) {
      // 羚羊计划 最后消息如果是信息卡片, 则展示默认文案, 不需要添加对方名字
      if (this.getMediaType() == MessageMediaType.Ante) {
        // TODO
      } else {
        let headerHint = this.getFriendName()
        if (!TextUtils.isEmpty(headerHint)) {
          headerHint = headerHint + ": ";
        }
        desc = headerHint + desc;
      }
    }

    // 兜底处理
    if (TextUtils.isEmpty(desc)) {
      desc = this.getCardLastChatText(this.getFriendName() ?? '')
    }

    return desc;
  }

  private getExtendField() {
    if (!this.extendField && this.message.extMsg && TextUtils.isNotEmpty(this.message.extMsg)) {
      this.extendField = JSON.parse(this.message.extMsg)
    }
  }

  public getStyle(): number {
    this.getExtendField();
    if (this.extendField) {
      return this.extendField.style
    }
    return 0;
  }

  public getFriendName(): string|undefined|null {
    return this.friendName
  }

  public getQuoteId(): number|null {
    this.getExtendField()
    if (this.extendField) {
      return this.extendField.quoteId
    }
    return null
  }

  public fromProtobuf(pbMessage: Protocol.ITechwolfMessage) {
    this.message.mid = pbMessage.mid
    this.message.smid = pbMessage.mid
    this.message.status = ChatMessage.fromProtobufStatus(pbMessage.status)
    this.message.badged = pbMessage.uncount ?? 0
    this.message.time = pbMessage.time
    this.message.cmid = pbMessage.cmid
    this.message.msgType = pbMessage.type
    this.message.securityId = pbMessage.securityId ?? null
    this.message.bizId = pbMessage.bizId ?? ''
    this.message.bizType = pbMessage.bizType ?? 0
    this.message.taskId = pbMessage.taskId
    this.message.flag = pbMessage.flag ?? -1

    // 局部变量不需要保存DB 临时使用
    this.isOffline = pbMessage.offline
    this.pushText = pbMessage.pushText
    this.soundUri = pbMessage.pushSound == 2 ? "android.resource://com.hpbr.bosszhipin/raw/gold_sound" : null


    let from: Protocol.ITechwolfUser = pbMessage.from
    let to: Protocol.ITechwolfUser = pbMessage.to
    this.message.sender = from.uid

    let account = Kernel.getInstance().getAccount()
    if (account && from.uid == account.uid || this.message.friendType == FriendType.Group) { // 我发出的
      this.message.friendId = to.uid;
      this.message.friendSource = to.source ?? FriendSource.BOSS
    } else { // 对方发出的
      this.message.friendId = from.uid;
      this.message.friendSource = from.source ?? FriendSource.BOSS
    }

    // 获取好友名字
    if (account) {
      if (to.uid == account.uid) { // 我是接收方
        this.friendName = pbMessage.from.name
      } else if (from.uid == account.uid) { // 我是发送方
        this.friendName = to.name
      }
    }

    let extendField: MessageExtendField = new MessageExtendField()
    let messageBody = pbMessage.body
    if (messageBody) {
      this.message.mediaType = messageBody.type
      this.message.content = messageBody.text
      this.message.templateId = messageBody.templateId
      this.message.headTitle = messageBody.headTitle
      this.parseProtobufMessageBody(messageBody);

      extendField.style = messageBody.style ?? 0
      extendField.quoteId = pbMessage.quoteId
    }
    this.extendField = extendField
    this.message.data = pbMessage.encryptedBody

    this.setExtFields()
  }

  public toProtobuf(): Protocol.ITechwolfMessage {
    let bean = this.message
    let message = Protocol.TechwolfMessage.create()
    message.mid = bean.cmid
    message.status = bean.status
    message.uncount = bean.badged
    message.type = bean.msgType
    message.time = bean.time
    message.cmid = bean.cmid

    let fromUser: Protocol.TechwolfUser = Protocol.TechwolfUser.create()
    fromUser.uid = this.message.sender
    fromUser.source = FriendSource.BOSS
    message.from = fromUser

    let toUser: Protocol.TechwolfUser = Protocol.TechwolfUser.create()
    toUser.uid = this.message.friendId
    toUser.source = this.message.friendSource
    message.to = toUser

    if (bean.bizId) {
      message.bizId = bean.bizId
    }
    if (bean.bizType) {
      message.bizType = bean.bizType
    }
    message.quoteId = this.getQuoteId()
    message.taskId = 0

    let body: Protocol.ITechwolfMessageBody|null = this.getProtobufMessageBody()
    if (body != null) {
      message.body = body
    }
    if (this.pushText != null) {
      message.pushText = this.pushText
    }

    if (this.isOffline != undefined && this.isOffline != null) {
      message.offline = this.isOffline
    }

    return message
  }

  private setExtFields(): MessageEntity {
    if (this.messageBody) {
      this.message.extStr = JSON.stringify(this.messageBody)
    }
    if (this.extendField) {
      this.message.extMsg = JSON.stringify(this.extendField)
    }
    if (this.localField) {
      this.message.extMapField = JSON.stringify(this.localField)
    }
    return this.message
  }

  private getExtFields() {
    if (TextUtils.isNotEmpty(this.message.extStr)) {
      this.messageBody = JSON.parse(this.message.extStr ?? '');
    }
    if (TextUtils.isNotEmpty(this.message.extMsg)) {
      this.extendField = JSON.parse(this.message.extMsg ?? '');
    }

    if (TextUtils.isNotEmpty(this.message.extMapField)) {
      this.localField = JSON.parse(this.message.extMapField ?? '');
    }
  }

  public fromDataBase(dbMessage: MessageEntity) {
    this.message = dbMessage
    this.getExtFields()
  }

  public toDataBase(): MessageEntity {
    this.setExtFields()
    return this.message
  }

  private static fromProtobufStatus(status?: (number |null)): number {
    switch (status) {
      case ReceivedMessageStatus.Unread:
      case ReceivedMessageStatus.Received:
        return MessageStatus.Success
      default:
        return MessageStatus.Read
    }
  }

  public isEncrypted(): boolean {
    return this.message.data != null && this.message.data != undefined && this.message.data.length > 0;
  }

  public isSentToMe(): boolean {
    return this.message.sender == this.message.friendId
  }

  public isSentFromMe(): boolean {
    return this.message.sender != this.message.friendId
  }

  public isGroup(): boolean {
    return this.message.friendType == FriendType.Group
  }

  set mid(mid: number) {
    this.message.mid = mid
  }

  get mid(): number {
    return this.message.mid
  }

  set status(status: number) {
    this.message.status = status
  }

  get status(): number {
    return this.message.status
  }

  set msgType(msgType: number) {
    this.message.msgType = msgType
  }

  get msgType() {
    return this.message.msgType
  }

  set friendType(value: number) {
    this.message.friendType = value
  }

  get friendType(): number {
    return this.message.friendType
  }

  set time(value: number) {
    this.message.time = value
  }

  get time(): number {
    return this.message.time
  }

  set cmid(value: number) {
    this.message.cmid = value
  }

  get cmid(): number {
    return this.message.cmid
  }

  set smid(value: number) {
    this.message.smid = value
  }

  get smid(): number {
    return this.message.smid
  }

  set sender(value: number) {
    this.message.sender = value
  }

  get sender(): number {
    return this.message.sender
  }

  set friendId(value: number) {
    this.message.friendId = value
  }

  get friendId() {
    return this.message.friendId
  }

  set friendSource(value: number) {
    this.message.friendSource = value
  }

  get friendSource(): number {
    return this.message.friendSource
  }

  set bizType(value: number) {
    this.message.bizType = value
  }

  get bizType(): number {
    return this.message.bizType
  }

  set templateId(value: number) {
    this.message.templateId = value
  }

  get templateId() {
    return this.message.templateId
  }

  set headTitle(value: string|null|undefined) {
    this.message.headTitle = value
  }

  get headTitle(): string|null|undefined {
    return this.message.headTitle
  }

  set content(value: string|null|undefined) {
    this.message.content = value
  }

  get content(): string|null|undefined {
    return this.message.content
  }

  set extend(value: string|null|undefined) {
    this.message.extend = value
  }

  get extend(): string|null|undefined {
    return this.message.extend
  }

  set id(value: number|undefined) {
    this.message.id = value
  }

  get id(): number|undefined {
    return this.message.id
  }

  set securityId(value: string|null|undefined) {
    this.message.securityId = value
  }

  get securityId(): string|null|undefined {
    return this.message.securityId
  }

  isBadged(): boolean {
    return this.message.isBadged()
  }

  isWithdrawn(): boolean {
    return this.message.isWithdrawn()
  }

  hasBeenRead(): boolean {
    return this.message.hasBeenRead()
  }

  isFromBoss() {
    return this.friendSource == FriendSource.BOSS
  }

  isBossPresetAnswerTemplate() {
    return this.templateId == TemplateId.BossPresetAnswer
  }

  protected getValueFromExtend(key: string): Object|null {
    if (this.extend) {
      let json: Record<string, Object> = JSON.parse(this.extend)
      return json[key]
    }
    return null
  }
}
