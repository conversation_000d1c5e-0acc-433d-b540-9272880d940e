import { ChatMessage } from './ChatMessage';
import Protocol from '../../../pb/chatprotocol'
import { CheckUtils, StringUtil, TextUtils } from '@ohos/utils/Index';
import { Long } from '@ohos/protobufjs';
import { IProtobufConverter } from '../IProtobufConverter';
import { TemplateId } from '../../../db/entity/MessageEntity';
import { MessageMediaType } from '../Constants';


export class TextMessage extends ChatMessage<ChatAtBean |null> {
  getMediaType(): number {
    return MessageMediaType.Text
  }

  showNewGreet(): boolean {
    return this.isSentToMe() && this.templateId == TemplateId.Normal
  }

  parseProtobufMessageBody(messageBody: Protocol.ITechwolfMessageBody) {
    if (messageBody.atInfo && messageBody.atInfo.flag > 0) {
      let chatAtBean = new ChatAtBean();
      chatAtBean.fromProtobuf(messageBody.atInfo)
      this.messageBody = chatAtBean
    }
  }

  getProtobufMessageBody(): Protocol.ITechwolfMessageBody {
    let body: Protocol.TechwolfMessageBody = Protocol.TechwolfMessageBody.create();
    body.type = this.getMediaType()
    body.templateId = this.templateId
    if (!TextUtils.isEmpty(this.headTitle)) {
      body.headTitle = this.headTitle ?? ''
    }
    if (!TextUtils.isEmpty(this.content)) {
      body.text = this.content ?? ''
    }
    if (!TextUtils.isEmpty(this.extend)) {
      body.extend = this.extend ?? ''
    }

    body.articles = []

    if (this.messageBody) {
      body.atInfo = this.messageBody.toProtobuf()
    }
    return body
  }

  isNeedToSave(): boolean {
    return true
  }

  isTwoWayChatSupported(): boolean {
    return this.templateId == TemplateId.Normal
  }

  public isNeedUpdateContactLastMessage() {
    return true
  }

  public hasSendStatus(): boolean {
    return true
  }

  public getSummary(): string|null|undefined {
    let desc = this.content
    if (this.templateId == TemplateId.ExchangePhoneOrWechat) {
      let combineRegionCodeText = this.createCombineRegionCodeText(false)
      if (!TextUtils.isEmpty(combineRegionCodeText)) {
        desc = combineRegionCodeText;
      }

      if (desc && !TextUtils.isEmpty(desc) && desc.includes("<copy>") && desc.includes("</copy>")) {
        desc = desc.replace("<copy>", "").replace("</copy>", "")
      }
    }

    return desc
  }

  private createCombineRegionCodeText(nextLine: boolean): string|null {
    let reginCode = this.getPhoneRegionCode()
    let isAdd86Number = reginCode === "+86"

    if (!this.content) {
      return null
    }

    let splitText = "：";

    // 如果是境外号码 需要手动补充境外区域
    if (!TextUtils.isEmpty(reginCode) && !isAdd86Number) {
      let split: string[] = this.content.split(splitText);
      if (split.length == 2) {
        this.content = split[0] + splitText + reginCode + " " + split[1];
        if (nextLine) {
          this.content = this.content.replace(splitText, "：\r\n");
        }
      }
    }

    return this.content.replace("<phone>", "").replace("</phone>", "");
  }

  public getPhone(): string|null {
    return this.content ? StringUtil.parserTagContent(this.content, "<phone>", "</phone>") : null
  }

  public getPhoneRegionCode(): string|null {
    let value = this.getValueFromExtend("regionCode")
    return value ? value as string : null
  }

  public getWechatNumber() {
    return this.content ? StringUtil.parserTagContent(this.content, '<copy>', '</copy>') : null
  }

  getBossAiChatMsgMark(): string |null {
    if (this.isFromBoss()) {
      let mark = this.getValueFromExtend("aiChatMsgMark")
      if (mark) {
        return mark as string
      }
    }

    return null
  }
}

export class ChatAtBean implements IProtobufConverter<Protocol.IAtInfo>{
  flag: number = 0 // AT标记: 0.无 1.AT消息接收者(下行)
  uids: Array<number|Long>|null = null // AT用户列表

  fromProtobuf(atInfo: Protocol.IAtInfo) {
    this.flag = atInfo.flag
    this.uids = atInfo.uids ?? null
  }

  toProtobuf(): Protocol.IAtInfo|null {
    if (this.uids && CheckUtils.isNotEmptyArr(this.uids)) {
      let atInfo = Protocol.AtInfo.create()
      atInfo.flag = this.flag
      atInfo.uids = this.uids
      return atInfo
    }

    return null
  }
}