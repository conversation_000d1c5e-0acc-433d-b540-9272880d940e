import { ProtobufMessage } from './ProtobufMessage';
import { MqttConfig } from '../../../MqttConfig';
import Protocol from '../../../pb/chatprotocol'
import { <PERSON><PERSON> } from '../../../../Kernel';
import { MessageFactory } from '../../../MessageFactory';


export class PresenceMessage extends ProtobufMessage<Protocol.TechwolfPresence> {
  type: number
  lastMid: number

  constructor(type: number, lastMid: number) {
    super()
    this.type = type
    this.lastMid = lastMid
  }

  getMessageType(): number {
    return MqttConfig.CHAT_TYPE_PRESENCE
  }

  isNeedToSave(): boolean {
    return false
  }

  fromProtobuf(pbMessage: Protocol.TechwolfPresence): void {
    throw new Error('Method not implemented.');
  }

  toProtobuf(): Protocol.TechwolfPresence|null {
    let clientTime = Protocol.TechwolfClientTime.create({
      startTime: Date.now(),
      resumeTime: Date.now()
    });

    let account = Kernel.getInstance().getAccount()
    if (!account) {
      return null
    }
    let uid = account.uid
    let presence = Protocol.TechwolfPresence.create({
      type: this.type,
      userId: uid,
      uid: uid,
      clientInfo: MessageFactory.createClientInfo(),
      lastMessageId: this.lastMid,
      lastGroupMessageId: 0,
      clientTime: clientTime
    })
    return presence
  }
}