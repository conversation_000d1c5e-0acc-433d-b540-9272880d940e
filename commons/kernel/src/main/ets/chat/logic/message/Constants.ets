/**
 * 消息的状态
 */
export enum MessageStatus {
  None = -1,  // 默认状态
  Sending,    // 发送中
  Success,    // 送达
  Failure,    // 失败
  Read,       // 已读
  Withdrawn   // 撤回
}

/**
 * 消息类型
 */
export enum MessageType {
  None,                       // 默认值
  Single,                     // 单聊
  Group,                      // 群聊
  System,                     // 系统消息
  SystemNotShownInF2,         // 系统消息（不计入F2联系人显示
  SystemGroupChat,            // 群聊系统消息
  SystemGroupChatNotShownInF2 // 群聊系统消息（F2不计数）
}

/**
 * 消息的媒体类型
 */
export enum MessageMediaType {
  None,
  Text,           // 文本
  Sound,          // 语音
  Image,          // 图片
  Action,         // 动作
  Article,        // 图文
  Notification,   // 通知
  Dialog,         // 对话框
  Job,            // 职位
  Resume,         // 简历
  RedEnvelope,    // 红包
  Order,          // 订单
  HyperLink,      // 超链接
  Video,          // 视频消息
  Interview,      // 面试状态消息
  ArticleList,    // 多图文消息
  ArticleList2,   // 提示图文消息
  TextWarning,    // 聊天窗口的警告信息
  JobShare,       // 职位分享
  ResumeShare,    // 简历分享
  Sticker,        // 动态表情
  Undefined,      // 没有该类型
  ChatShare,      // 聊天分享
  InterviewShare, // 面试分享
  ListCard,       // 通用列表卡片
  StartRate,      // 星级评分卡片
  Frame,          // 异步加载消息
  MultiImage,     // 多图片 美食照片
  Company,        // 公司
  Ante,           // 羚羊计划用户卡片

  Iq = 50,        // IQ
  JobList,        // 群聊待发布职位列表本地卡片

  Time = 1000,    // 本地时间
}