import { FriendType } from '../../../db/entity/MessageEntity';
import { MqttConfig } from '../../../MqttConfig';
import { ITechwolfMessageSync } from '../../../pb/chatprotocol';
import { ProtobufMessage } from './ProtobufMessage';


export class MidSyncMessage extends ProtobufMessage<ITechwolfMessageSync> {
  friendType: number = -1
  clientMid: number = -1
  serverId: number = -1

  getMessageType(): number {
    return MqttConfig.CHAT_TYPE_SEND_MESSAGE_ID_RESPONSE
  }

  isNeedToSave(): boolean {
    return false
  }

  fromProtobuf(pbMessage: ITechwolfMessageSync): void {
    this.clientMid = pbMessage.clientMid
    this.serverId = pbMessage.serverMid
  }

  toProtobuf(): ITechwolfMessageSync|null {
    return null
  }

  isGroup() {
    return this.friendType == FriendType.Group
  }
}