import { HashMap } from '@kit.ArkTS';
import Protocol from 'ets/chat/pb/chatprotocol';
import { MqttConfig } from '../../../MqttConfig';
import { ProtobufMessage } from './ProtobufMessage';


export class IqResponseMessage extends ProtobufMessage<Protocol.TechwolfIqResponse> {
  public query: string|null = null // 请求路径

  public params: HashMap<String, String> = new HashMap() // 返回结果

  getMessageType(): number {
    return MqttConfig.CHAT_TYPE_IQ_RESPONSE
  }

  isNeedToSave(): boolean {
    return false
  }

  fromProtobuf(pbMessage: Protocol.TechwolfIqResponse): void {
  }

  toProtobuf(): Protocol.TechwolfIqResponse|null {
    throw new Error('Method not implemented.');
  }
}