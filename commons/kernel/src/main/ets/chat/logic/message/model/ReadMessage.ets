import { FriendSource } from '../../../db/entity/ContactBaseInfo';
import { FriendType } from '../../../db/entity/MessageEntity';
import { MqttConfig } from '../../../MqttConfig';
import Protocol from '../../../pb/chatprotocol'
import { ProtobufMessage } from './ProtobufMessage';


export class ReadMessage extends ProtobufMessage<Protocol.ITechwolfMessageRead> {
  friendId: number = -1     // 联系人的用户ID
  friendSource: number = -1 // 联系人的来源
  messageId: number = -1    // 消息ID
  readTime: number = -1     // 读取时间
  sync: boolean = false     // 是否是同步已读消息：其他端（如PC）同时登录
  friendType: number = -1

  getMessageType(): number {
    return MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ
  }

  isNeedToSave(): boolean {
    return false
  }

  fromProtobuf(pbMessage: Protocol.ITechwolfMessageRead): void {
    this.friendId = pbMessage.userId
    this.messageId = pbMessage.messageId
    this.readTime = pbMessage.readTime
    this.sync = pbMessage.sync ?? false
    this.friendSource = pbMessage.userSource ?? FriendSource.BOSS
  }

  toProtobuf(): Protocol.ITechwolfMessageRead {
    let pbMessage = Protocol.TechwolfMessageRead.create()
    pbMessage.userId = this.friendId
    pbMessage.messageId = this.messageId
    pbMessage.readTime = this.readTime
    pbMessage.sync = this.sync
    pbMessage.userSource = this.friendSource
    return pbMessage
  }

  isGroup() {
    return this.friendType == FriendType.Group
  }
}