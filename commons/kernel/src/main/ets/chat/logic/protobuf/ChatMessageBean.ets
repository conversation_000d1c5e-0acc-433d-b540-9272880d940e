/**
 * Created by monch on 15/4/13.
 */
import BaseEntity from '../../../base/BaseEntity'
import { ChatMessageBodyBean } from './ChatMessageBodyBean'
import ChatUserBean from './ChatUserBean'

export class ChatMessageBean extends BaseEntity {
  /**
   * 7.06的扩展字段
   */
  public bizId?: (string|null) = ''

  public bizType?: (number|null) = -1

  public quoteId: number = 0
     
  //用户布局卡片显示,不需要入库
  // public transient quoteChatBean: ChatBean;
     
  /**
   * 来源用户信息
   */
  public fromUser: ChatUserBean|null = null

  /**
   * 接收用户信息
   */
  public toUser: ChatUserBean|null = null
  
  /**
   * 消息类型，1为单聊，2为群聊，3为系统消息，4为系统消息（不计入F2联系人显示）5为群聊系统消息 6位群聊系统消息（F2不计数）
   */
  public type: number = 0
  
  /**
   * 6.17消息撤回本地记录的撤回文案,服务区不需要此字段
   */
  // public transient revocationText: string;

  /**
   * 消息内容主体
   */
  public messageBody: ChatMessageBodyBean|null = null

  /**
   * 消息加密主体 与 {@link #messageBody}互斥
   */
  public encryptedBody?: (Uint8Array|null) = undefined

  /**
   * 消息通知内容
   */
  public pushText: string = ''

  /**
   * 消息声音文件，不需要入库
   */
  public soundUri: string|null = null // TODO: transient

  /**
   * 消息任务id
   */
  public taskId: number = 0

  /**
   *
   */
  public securityId?: (string|null)

  /**
   * 消息时间
   */
  public time: number = 0

  /**
   * 是否为离线消息
   */
  public isOffline: boolean|null = null

  /**
   * 是否接收过，4.1开始放弃使用
   */
  public isReceived: boolean = false

  /**
   * 消息状态，0为未读消息，1为已接收，2为已读
   * 【对方给我发送的消息】
   */
  public status: number = -1

  /**
   * 客户端生成的临时变量，
   * 在发送中无任何意义，
   * 只用于接收消息时，
   * 如果有此值，
   * 需要查询本地的消息id是否与此值有相等，
   * 如果有，则修改为正式服务器id
   */
  public clientTempMessageId: number = 0

  /**
   * 0为默认,1为不计消息数
   */
  public unCount: number = 0
     
  /***
   * 特殊标记位，用低2位表示客户端用户的身份，0.牛人 1.BOSS 2.预留 3.预留
   */
  public flag?: (number|null)= -1

}