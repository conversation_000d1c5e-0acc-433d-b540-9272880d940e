/**
 * Created by monch on 15/4/13.
 */
import BaseEntity from '../../../base/BaseEntity';
import { ChatActionBean } from './ChatActionBean';

export class ChatMessageBodyBean extends BaseEntity {
   public static readonly BODY_TYPE_JD: number = 8;
   public static readonly BODY_TYPE_RESUME: number = 9;
     
  /**
   * 消息类型：1.文本 2.语音 3.图片 4.动作 5.图文 6.通知 7.对话框 8.职位 9.简历 10.红包 11.订单 12.超链接
   * 13.视频消息  14.面试状态消息  15.多图文消息  16.提示图文消息 17.聊天窗口的警告信息 18.职位分享 19.简历分享 20.动态表情 22.聊天分享 23.面试分享  24//通用列表卡片 25 //V7.06 客服 星级评分卡片 27//多图片 美食照片
   * <p>
   * 备注：类型'21'被陈良柱跳过了
   */
  public type: number = -1

  /**
   * 消息内容模板ID，1.正常 2.居中 3.居中灰色 4.直聘技巧样式 5.交换电话或微信样式 6.红包灰色居中 7.破冰语 8.提醒设置9.牛人约面试 10不入库的文本消息 (和1唯一区别就是不入库)
   */
  public templateId: number = -1

  /**
   * 标题
   */
  public title?: (string|null)

  /**
   * 文本消息内容，类型为1时必须存在
   */
  public text?: (string|null)
     
  /**
   * 8.21版本链接卡片需要
   */
  public extend: string = ''

  /**
   * 声音消息内容，类型为2时必须存在
   */
  // public sound: ChatSoundBean

  /**
   * 图片消息内容，类型为3时必须存在
   */
  // public image: ChatImageBean
     
  /**
   * 810版本多图片&上传美食作品
   */
  // public multiplyImage: List<ChatImageInfoBean>
  
  /**
   * gif类型
   */
  // public gifImageBean: ChatGifImageBean
  
  /**
   * 动作消息内容，类型为4时必须存在
   */
  public action?: ChatActionBean
  
  /**
   * 图文消息内容，类型为5时必须存在
   */
  // public article: ChatArticleBean
     
  /**
   * 多图文消息内容，类型为15时必须存在
   */
  // public articleList: List<ChatArticleBean>
  
  /**
   * 通知消息内容，类型为6时必须存在
   */
  // public notify: ChatNotifyBean
  
  /**
   * 对话框消息内容，类型为7时必须存在
   */
  // public dialog: ChatDialogBean
  
  /**
   * 职位消息内容，类型为8时必须存在
   */
  // public job: ChatJobBean
     
  /**
   * 简历消息内容，类型为9时必须存在
   */
  // public resume: ChatResumeBean
  
  /**
   * 红包消息
   */
  // public redEnvelope: ChatRedEnvelopeBean
  
  /**
   * 订单消息
   */
  // public orderBean: ChatOrderBean
  
  /**
   * 超链接
   */
  // public hyperLinkBean: ChatHyperLinkBean
  
  /**
   * 视频通话
   */
  // public videoBean: ChatVideoBean
  
  /**
   * 面试状态消息
   */
  // public interviewBean: ChatInterviewBean
     
  /**
   * 群聊分享职位
   */
  // public jobShareBean: ChatJobShareBean
  
  /**
   * 群聊分享简历
   */
  // public resumeShareBean: ChatResumeShareBean
     
  /**
   * 群聊@
   */
  // public atBean: ChatAtBean
     
  /**
   * 聊天历史分享
   */
  // public chatShareBean: ChatShareBean
     
  /**
   * 面试分享
   */
  // public interviewShare: InterviewShare
     
  /**
   * 客服 猜你想问列表
   */
  // public flingList: ChatCustomerFlingList
     
  /**
   * 评分卡片
   */
  // public gradeCardBean: ChatGradeCardBean
     
  /**
   * 异步消息
   */
  // public frameBean: ChatFrameBean
     
  /**
   * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=*********
   */
  // public companyDescBean: ChatCompanyDescBean
     
  // public antelopeInfoBean: ChatGroupAntelopeInfoCardBean
     
  /**
   * 颜色风格
   */
  public style: number = -1
     
  /**
   * 是否是第一条消息消息,用是否是卡片消息判断
   * * @return
   */
  public  isFirstMessage(): boolean {
    return this.type == ChatMessageBodyBean.BODY_TYPE_JD || this.type == ChatMessageBodyBean.BODY_TYPE_RESUME;
  }
}