import BaseEntityAuto from '../../../base/BaseEntityAuto'

/**
 * Created by monch on 15/4/13.
 */
export class MessageBean extends BaseEntityAuto {
  /**
   * 聊天消息的真实ID
   */
  public mid: number
  
  /**
   * 用户排序
   */
  public smid: number

  /**
   * 客户端生成的临时变量，
   * 在发送中无任何意义，
   * 只用于接收消息时，
   * 如果有此值，
   * 需要查询本地的消息id是否与此值有相等，
   * 如果有，则修改为正式服务器id
   */
  public cmid: number

  public fromUid: number

  public fromName: string

  /**
   * 蓝白融合来源
   * {@link com.hpbr.bosszhipin.data.db.entry.ContactBean#FROM_BOSS}
   * {@link com.hpbr.bosszhipin.data.db.entry.ContactBean#FROM_DIAN_ZHANG}
   */
  public friendSource: number = 0

  public toUid: number

  public toName: string

  /**
   * 消息任务id
   */
  public taskId: number

  /**
   *
   */
  public securityId: string

  /**
   * 消息时间
   */
  public time: number

  /**
   * 消息状态，0为未读消息，1为已接收，2为已读
   */
  public status: number

  /**
   * 消息类型：1.文本 2.语音 3.图片 4.动作 5.图文 6.通知 7.对话框 8.职位 9.简历 10.红包 11.订单 12.超链接
   * 13.视频消息  14.面试状态消息  15.多图文消息  16.提示图文消息 17.聊天窗口的警告信息 18.职位分享 19.简历分享 20.动态表情
   */
  public type: number

  /**
   * 0为默认,1为不计消息数
   */
  public unCount: number

  /***
   * 特殊标记位，用低2位表示客户端用户的身份，0.牛人 1.BOSS 2.预留 3.预留
   */
  public flag: number  = -1

  /**
   * 消息类型，1为单聊，2为群聊
   */
  public sessionType: number

  /**
   * 消息类型，1为单聊，2为群聊，3为系统消息，4为系统消息（不计入F2联系人显示）5为群聊系统消息 6位群聊系统消息（F2不计数）
   */
  public sessionMessageType: number

  /****************************begin 附加信息********************************   */
  /**
   * 协议版本号
   */
  public version: string

  /**
   * 消息发送状态，0为发送中，1为发送成功，2为发送失败，3为已读 4撤回消息
   */
  public sendStatus: number

  /**
   * 我的身份
   */
  public myRole: number

  /**
   * 我的用户ID
   */
  public myUserId: number

  /**
   * 聊天发送时间
   */
  public messageSendTime: number

  /****************************end 附加信息********************************   */


  /****************************begin 文本********************************   */
  /**
   * 消息内容模板ID，1.正常 2.居中 3.居中灰色 4.直聘技巧样式 5.交换电话或微信样式 6.红包灰色居中 7.破冰语 8.提醒设置 9.Boss同意接受简历,达成双聊
   */
  public templateId: number

  /**
   * 标题
   */
  public title: string

  /**
   * 文本消息内容，类型为1时必须存在
   */
  public text: string

  public extend: string

  /**
   * 撤回消息文案
   */
  public revocationText: string

  /****************************end 文本********************************   */


  /****************************begin 语音********************************   */
  /**
   * 声音id
   */
  public soundSid: number
  /**
   * 声音路径
   */
  public soundUrl: string
  /**
   * 声音时长
   */
  public soundDuration: number
  /**
   * 声音文件的本地路径
   */
  public soundLocalUrl: string

  /****************************end 语音********************************   */


  /****************************begin 图片********************************   */
  /**
   * 缩略图路径
   */
  public tinyUrl: string
  /**
   * 缩略图的宽
   */
  public tinyWidth: number
  /**
   * 缩略图的高
   */
  public tinyHeight: number

  /**
   * 原图路径
   */
  public originUrl: string
  
  /**
   * 原图的宽
   */
  public originWidth: number
  
  /**
   * 原图的高
   */
  public originHeight: number

  /****************************end 图片********************************   */


  /**
   * 本地 新职位/新简历 是否已经读取 【0未读】【1已读取】
   */
  public newPositionResumeStatus: number

  /**
   * 对方给我发送的消息是否已读
   */
  public hasRead: boolean

  //简历是否模糊
  public isResumeBlurred: boolean

  /**
   * 动作消息内容，类型为4时必须存在
   */
  // public action: ChatActionBean

  /**
   * 图文消息内容，类型为5时必须存在
   */
  // public article: ChatArticleBean

  /**
   * 多图文消息内容，类型为15时必须存在
   */
  // public articleList: List<ChatArticleBean>

  /**
   * 810版本美食上传
   */
  // public multiplyImage: List<ChatImageInfoBean>

  /**
   * 通知消息内容，类型为6时必须存在
   */
  // public notify: ChatNotifyBean

  /**
   * 对话框消息内容，类型为7时必须存在
   */
  // public dialog: ChatDialogBean

  /**
   * 职位消息内容，类型为8时必须存在
   */
  // public job: ChatJobBean

  /**
   * 简历消息内容，类型为9时必须存在
   */
  // public resume: ChatResumeBean

  /**
   * 红包消息
   */
  // public redEnvelope: ChatRedEnvelopeBean

  /**
   * 订单消息
   */
  // public orderBean: ChatOrderBean

  /**
   * 超链接
   */
  // public hyperLinkBean: ChatHyperLinkBean

  /**
   * 视频通话
   */
  // public videoBean: ChatVideoBean

  /**
   * 面试状态消息
   */
  // public interviewBean: ChatInterviewBean

  // public jobShareBean: ChatJobShareBean

  // public resumeShareBean: ChatResumeShareBean

  // public atBean: ChatAtBean

  // public gifImageBean: ChatGifImageBean

  // public chatShareBean: ChatShareBean

  // public interviewShare: InterviewShare

  // public flingList: ChatCustomerFlingList

  // public gradeCardBean: ChatGradeCardBean

  // public frameBean: ChatFrameBean

  // public companyDescBean: ChatCompanyDescBean

  // public antelopeInfoBean: ChatGroupAntelopeInfoCardBean

  public entryMessage: ArrayBuffer

  public bizid: string

  public bizType: number

  public quoteId: number

  public style: number
}
