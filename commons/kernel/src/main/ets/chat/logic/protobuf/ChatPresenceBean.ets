/**
 * Created by monch on 15/4/13.
 */
export default class ChatPresenceBean {
  /**
   * 出席类型，1.上线, 2.隐身（上行）3.下线 4. 后台（Android） 5. 恢复前台（Android）
   */
  public type?: number

  /**
   * 设置信息
   */
  // TODO
  // public clientInfo: ChatClientInfoBean;

  /**
   * 设备启动时间
   */
  public startTimer?: number;

  /**
   * 设备唤醒时间
   */
  public resumeTimer?: number

  /**
   * 本地最大的消息ID，除时间戳之外
   */
  public lastMessageId?: number

  /**
   * 本地最大的群聊消息ID，除时间戳之外
   */
  public lastGroupMessageId?: number

  /**
   * 定位成功时间
   */
  public locateTime?: number
}