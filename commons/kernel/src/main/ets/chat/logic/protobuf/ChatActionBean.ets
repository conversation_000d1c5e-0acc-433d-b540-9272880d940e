export class ChatActionBean {
  /**
   * 此类的动作type较多，
   * 10单点登录被踢，
   * 20对话框操作action（{"msg_id":1234, "action":1} action为操作的第几个按钮），
   * 27交换联系方式，
   * 28接受交换联系方式，
   * 29拒绝交换联系方式，
   * 30有人看过你（{"count":10}）
   * 31有人对你感兴趣（{"count":10}）
   * 32请求交换微信
   * 33同意交换微信
   * 34拒绝交换微信
   * 35有新人或新职位（{"count":10}）
   * 37 BOSS 请求获取简历
   * 38 牛人 同意获取简历
   * 39 牛人 拒绝获取简历
   * 40 牛人 请求发送简历
   * 41 BOSS 同意发送简历
   * 42 BOSS 拒绝发送简历
   * 43 有同事推荐牛人（{"newCount":"10","totalCount":"15"}）
   * 44 用户身份被冻结
   * 45 新用户注册首页弹窗
   * 46 应用评价弹窗
   * 47 同意约面试
   * 48 拒绝约面试
   * 49 面试提示泡泡
   * 50 待面试数量
   * 51 更新好友期望ID
   * 52 冻结/解冻联系人
   * 53 下发视频聊天的token  废弃
   * 54 联系人来源
   * 55 执行直聘协议
   * 56 有简历直通车消息
   * 57 APP确认Web登录
   * 58 关注公司列表中新职位数量
   * 59 同意牛炸开聊
   * 60 拒绝牛炸开聊
   * 61 Boss对牛人发起面试邀请
   * 62 Boss收回面试邀请
   * 63 Boss的面试邀请超时
   * 64 牛人/Boss取消面试
   * 65 即将面试
   * 66 面试时间到
   * 67 面试达成
   * 68 牛人/Boss/双方失约
   * 69 面试邀请的房卡
   * 70 安全框架冻结
   * 71 消息解密及密钥（解锁商业阻断） {"secretKey":"base64EncodedKey"}
   * 75 消息撤回
   * 77 Boss批量沟通牛人
   * 79 清空F3气泡消息
   * 84 视频只有Boss可以发送,牛人接收
   * 85 语音面试
   * 86 视频未接听,发送方主动挂断,通知接收方挂断视频
   * 87 语音未接听,发送方主动挂断,通知接收方挂断语音
   * 89 ACCOUNT_WRITE_OFF_ACCEPTED(89)，注销通过
   * 96 删除消息 样例：{aid = 96, extend=“{\”mid\":***********} “}
   * 97 更新消息 样例：{aid = 97, extend=“{\”mid\":***********} “}
   * 111 Boss发送地理位置到牛人
   * 997 删除联系人
   * 998 指定删除message
   * 9999 日志记录开关 ({"userId":用户ID,"switchType":0关闭 1打开 2上传,"date":"需要上传日志的日期 yyyy-MM-dd"})
   */
  type?: number

  /**
   * 扩展数据
   */
  extend?: string
}