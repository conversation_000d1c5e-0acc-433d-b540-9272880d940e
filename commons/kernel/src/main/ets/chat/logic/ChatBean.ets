import BaseEntityAuto from '../../base/BaseEntityAuto'
import { MqttConfig } from '../MqttConfig'
import { MessageMediaType } from './message/Constants'
import ChatIQResponseBean from './protobuf/ChatIQResponseBean'
import { ChatMessageBean } from './protobuf/ChatMessageBean'
import ChatMessageSyncBean from './protobuf/ChatMessageSyncBean'
import ChatPresenceBean from './protobuf/ChatPresenceBean'
import { MessageExtendField, MessageUser } from './message/model/MessageExtendField'
import { Logger } from '@ohos/utils/Index'
import { Kernel } from '../../Kernel'
import { ChatDataBase } from '../db/ChatDataBase'


export class ChatBean extends BaseEntityAuto {
  /**
   * 聊天消息的真实ID
   */
  public msgId: number = -1

  /**
   * 本地临时消息变量
   */
  public clientTempMessageId: number = -1

  /**
   * 我的用户ID
   */
  public myUserId: number = -1

  /**
   * 我的身份
   */
  public myRole: number = -1

  /**
   * 发送消息源的用户ID
   */
  public fromUserId: number = -1

  /**
   * 接收消息源的用户ID
   */
  public toUserId: number = -1

  /**
   * 协议类型，1:message 2: presence 3: iq 4: iqResponse 5:syncAll 6:reader 7:数据同步
   */
  public msgType: number = -1

  /**
   * 协议版本号
   */
  public version?: string|null = null

  /**
   * 聊天消息，类型为1时，必须存在
   */
  public message: ChatMessageBean|null = null

  /**
   * 当前状态消息：上线下线使用，类型为2时，必须存在
   */
  public presence?: ChatPresenceBean

  /**
   * IQ消息：同步需要的消息数据时使用，类型为3时，必须存在
   */
  // TODO
  // public iq: ChatIQBean

  /**
   * IQ消息返回结果：同步消息数据返回的结果，类型为4时，必须存在
   */
  public iqResponse: ChatIQResponseBean|null = null

  /**
   * 同步消息内容：返回服务器的消息ID，类型为5时，必须存在
   */
  public messageSync: ChatMessageSyncBean|null = null

  /**
   * 消息已读内容
   */
  // TODO
  // public messageRead: Array<ChatMessageReadBean>

  /**
   * 数据同步消息
   */
  // TODO
  // public messageDataSync: ChatDataSync

  //消息域 1:单聊 2:群聊
  public domain: number = -1

  /**
   * 发送是否成功，默认为false
   */
  public sendSuccess: boolean = false

  /**
   * 聊天发送时间
   */
  public messageSendTime: number = -1

  /**
   * 消息发送状态，0为发送中，1为发送成功，2为发送失败，3为已读 4 表示撤回
   * <p>
   * 【我给对方发送的消息】
   */
  public status: number = -1

  /**
   * 对方给我发送的消息是否已读
   */
  public hasRead: boolean = false

  public time: number = -1

  public isContactNeedRefresh: boolean = false

  public sortMsgId: number = -1

  public isGroup(): boolean {
    return this.domain == 2;
  }

  /**
   * 检查是否为存储消息
   *
   *  TODO: 合入MessageUtils.isMessageNeedSave逻辑
   */
  public isNeedToSave(): boolean {
    if (this.msgType == MqttConfig.CHAT_TYPE_MESSAGE) {
      if (this.isActionMessage()) {
        let action = this.message?.messageBody?.action
        if (action && action.type != 27
          && action.type != 32
          && action.type != 37
          && action.type != 40) {
          return false;
        }
      }
      return true;
    }
    return false;
  }

  public isActionMessage(): boolean {
    return this.message != null
      && this.message.messageBody != null
      && this.message.messageBody.type == 4;
  }

  public toExtString(): string|null {
    switch (this.message?.messageBody?.type) {
      case MessageMediaType.Text:
        return null
      default:
        return null
    }
  }

  public toFieldMap(): string|null {
    if (!this.hasRead) {
      let record: Record<string, Object> = {
        'clickRead': true
      };
      return JSON.stringify(record)
    }
    return null;
  }

  public toExtendField(): string|null {
    if (this.message && this.message.fromUser && this.message.toUser && this.message.messageBody) {
      let from = new MessageUser()
      from.avatar = this.message.fromUser.avatar
      from.name = this.message.fromUser.name
      from.uid = this.message.fromUser.id
      from.source = this.message.fromUser.friendSource

      let to = new MessageUser()
      to.avatar = this.message.toUser.avatar
      to.name = this.message.toUser.name
      to.uid = this.message.toUser.id
      to.source = this.message.toUser.friendSource
      let extendField = new MessageExtendField(this.message.messageBody.style, this.message.quoteId)
      return JSON.stringify(extendField)
    }
    return null
  }

  public isEncrypted(): boolean {
    return this.message != null
      && this.message.encryptedBody != null
      && this.message.encryptedBody != undefined
      && this.message.encryptedBody.length > 0;
  }

  getFriendName(): string {
    let account = Kernel.getInstance().getAccount()
    if (account) {
      try {
        if (this.toUserId == account.uid) { // 我是接收方
          return this.message?.fromUser?.name ?? ''
        } else if (this.fromUserId == account.uid) { // 我是发送方
          return this.message?.toUser?.name ?? ''
        }
      } catch (e) {
        Logger.error('getFriendName failed: ' + JSON.stringify(e))
      }
    }
    return ''
  }

  async getContactBean() {
    if (!this.message?.toUser) {
      return null
    }

    let account = Kernel.getInstance().getAccount()
    if (!account) {
      return null
    }

    let contactDao = await ChatDataBase.getInstance().getContactDao()
    if (!contactDao) {
      return null
    }

    let friendId: number = this.toUserId == account.uid ? this.fromUserId : this.toUserId
    return await contactDao.getByFriendIdAndSource(friendId, this.message.toUser.friendSource);
  }
}
