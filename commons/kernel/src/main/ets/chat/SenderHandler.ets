import { Logger } from '@ohos/utils';
import { ISendCallback } from '../mqtt/MqttConfig';
import MqttConnection from '../mqtt/MqttConnection';
import { ContactEntity } from './db/entity/ContactEntity';
import { MessageFactory } from './MessageFactory';
import { MessageSendCallback } from './MessageSendCallback';
import { ChatMessage, ChatMessageBodyType } from './logic/message/model/ChatMessage';
import { ReadMessage } from '../../../../Index';
import { PresenceMessage } from './logic/message/model/PresenceMessage';
import MessageRepository from './MessageRepositoty';
import { ProtobufMessage } from './logic/message/model/ProtobufMessage';


class DefaultCallback implements ISendCallback {
  private successCallback?: (mid: number) => void;
  private failureCallback?: () => void;

  setSuccessCallback(callback: (mid: number) => void): void {
    this.successCallback = callback;
  }

  setFailureCallback(callback: () => void): void {
    this.failureCallback = callback;
  }

  onSuccess(mid: number): void {
    if (this.successCallback) {
      this.successCallback(mid);
    }
  }

  onFailure(): void {
    if (this.failureCallback) {
      this.failureCallback();
    }
  }
}

export class SenderHandler {
  /**
   * 出席消息发送是否成功
   * 使用此变量是为了解决连接完成，
   * 但是出席消息一直未发送成功，
   * 这中间接到的新消息称为中间消息，
   * 此部分消息收到之后，
   * 出席消息如果发送成功，
   * 因为本地最大消息ID已经改变，
   * 所以中间可能会出现丢消息的状态
   */
  public static mPresenceMessageSendSuccess = false;
  private static messageSendCallback: MessageSendCallback = new MessageSendCallback()

  private static async sendMessageInternal(to: ContactEntity|null, message: ProtobufMessage<Object>, callback?: ISendCallback) {
    let wrapperCallback = new DefaultCallback()
    wrapperCallback.setSuccessCallback(async (mid) => {
      await SenderHandler.messageSendCallback.onSuccess(to, message, mid)
      callback?.onSuccess(mid)
    })
    wrapperCallback.setFailureCallback(async () => {
      await SenderHandler.messageSendCallback.onFailure(to, message)
      callback?.onFailure()
    })

    await SenderHandler.messageSendCallback.onStart(to, message)
    let msgArrayBuffer = MessageFactory.protobufMessageArrayBuffer(message)
    if (msgArrayBuffer) {
      MqttConnection.publish(msgArrayBuffer, wrapperCallback);
    }
  }

  static async sendPresenceMessage(type: number) {
    let callBack = new DefaultCallback();
    callBack.setSuccessCallback((mid) => {
      Logger.info("Presence Success");
      SenderHandler.mPresenceMessageSendSuccess = true;
    });
    callBack.setFailureCallback(async () => {
      Logger.error("Presence failed: isConnected = " + (await MqttConnection.isConnected()));
    })

    let maxMid = await MessageRepository.getInstance().getMaxMessageId()
    await SenderHandler.sendMessageInternal(null, new PresenceMessage(type, maxMid), callBack)
  }

  static async sendMessage(to: ContactEntity, message: ChatMessage<ChatMessageBodyType>, callback?: ISendCallback) {
    await SenderHandler.sendMessageInternal(to, message, callback)
  }

  static async sendReadMessage(message: ReadMessage, callback?: ISendCallback) {
    await SenderHandler.sendMessageInternal(null, message, callback)
  }
}