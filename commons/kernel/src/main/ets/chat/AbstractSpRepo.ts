import dataPreferences from '@ohos.data.preferences';
import common from '@ohos.app.ability.common';

export default abstract class AbstractRepo {
  protected sharedPreferences: dataPreferences.Preferences
  private context: common.Context

  abstract getName(): string

  constructor(context: common.Context) {
    this.context = context
  }

  protected async getSharedPreferences() {
    if (!this.sharedPreferences) {
      this.sharedPreferences = await dataPreferences.getPreferences(this.context, this.getName())
    }
  }

  protected async putValue(key: string, val: dataPreferences.ValueType) {
    if (!this.sharedPreferences) {
      await this.getSharedPreferences();
    }

    // 如果存的值是undefined或null，取的时候会抛异常
    if (val) {
      await this.sharedPreferences.put(key, val)
    }
  }

  protected async getValue<T extends dataPreferences.ValueType>(key: string, defaultValue: T): Promise<T> {
    if (!this.sharedPreferences) {
      await this.getSharedPreferences();
    }
    return await this.sharedPreferences.get(key, defaultValue) as T
  }
}