import { Logger } from '@ohos/utils/Index';
import { ChatDataBase } from '../chat/db/ChatDataBase';
import { ContactDao } from './db/dao/ContactDao';
import { MessageDao } from './db/dao/MessageDao';
import { ContactEntity } from './db/entity/ContactEntity';
import { ContactUtils } from './ContactUtils';
import { MqttConfig } from './MqttConfig';
import { ChatMessage, ChatMessageBodyType } from './logic/message/model/ChatMessage';
import { MessageMediaType, MessageStatus } from './logic/message/Constants';
import { ProtobufMessage } from './logic/message/model/ProtobufMessage';
import { ReadMessage } from '../../../../Index';
import MessageRepository from './MessageRepositoty';


/**
 * 处理发出的消息
 */
export class MessageSendCallback {
  private messageDao: MessageDao|null = null
  private contactDao: ContactDao|null = null

  private async init() {
    if (!this.messageDao) {
      this.messageDao = await ChatDataBase.getInstance().getMessageDao()
    }
    if (!this.contactDao) {
      this.contactDao = await ChatDataBase.getInstance().getContactDao()
    }
  }

  /**
   * 信息发送前
   */
  async onStart(to: ContactEntity|null, message: ProtobufMessage<Object>) {
    await this.init()

    if (!message.isNeedToSave()) {
      return
    }

    switch (message.getMessageType()) {
      case MqttConfig.CHAT_TYPE_MESSAGE:
        if (to) {
          await this.handleMessageSave(to, message as ChatMessage<ChatMessageBodyType>)
        }
        break
    }
  }

  async onSuccess(to: ContactEntity|null, message: ProtobufMessage<Object>, mid: number = 0) {
    await this.init()
    await this.onComplete(to, message, true, mid)
  }

  async onFailure(to: ContactEntity|null, message: ProtobufMessage<Object>) {
    await this.onComplete(to, message, false)
  }

  /**
   * 信息发送：处理结果
   **/
  private async onComplete(to: ContactEntity|null, message: ProtobufMessage<Object>, success: boolean, mid?: number) {
    switch (message.getMessageType()) {
      case MqttConfig.CHAT_TYPE_MESSAGE:
        if (to) {
          await this.handleMessageResult(to, message as ChatMessage<ChatMessageBodyType>, success, mid)
        }
        break
      case MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ:
        await this.handleReadMessageResult(message as ReadMessage, success)
        break
    }
  }

  private async handleMessageSave(to: ContactEntity, message: ChatMessage<ChatMessageBodyType>) {
    message.toDataBase()

    // 1、保存消息
    await this.saveMessage(message);

    // 2、更新联系人
    await this.updateContact(to, message);
  }


  /**
   * 信息发后：结果
   *
   * mid
   *  成功：>= 0（灰度时>0）
   *  失败：无
   **/
  private async handleMessageResult(to: ContactEntity, message: ChatMessage<ChatMessageBodyType>, success: boolean, mid: number = -1) {
    if (message.getMediaType() == MessageMediaType.Action) {
      // TODO
    } else {
      // 更新联系人最后一条消息状态
      await this.contactDao?.updateLastMessageState(to.friendId, to.friendSource, success ? MessageStatus.Success : MessageStatus.Failure)
    }

    // 更新mid
    if (mid > 0) {
      await this.messageDao?.updateMidByCmid(mid, message.cmid)
      await MessageRepository.getInstance().saveMaxMessageId(false, mid)
    }

    // 更新状态
    await this.messageDao?.updateStatusByCmid(success ? MessageStatus.Success : MessageStatus.Failure, message.cmid)
  }

  private async saveMessage(bean: ChatMessage<ChatMessageBodyType>) {
    if (this.messageDao) {
      bean.time = Date.now();
      if (!bean.id) { // 新消息
        bean.id = await this.messageDao.save(bean.toDataBase());
        if (bean.id > 0) {
          Logger.debug("保存数据成功：" + bean.id);
        } else {
          Logger.error("保存数据失败：" + bean);
        }
      } else { // 旧消息
        await this.messageDao.updateStatusAndTimeById(bean.status, bean.time, bean.id)
      }
    }
  }

  private async updateContact(to: ContactEntity, message: ChatMessage<ChatMessageBodyType>) {
    if (!this.contactDao) {
      return
    }

    let lastChatStatus: number
    let lastChatClientMessageId: number
    if (message.getMediaType() == MessageMediaType.Action) {
      // 事件消息不处理发送状态
      lastChatStatus = MessageStatus.None
      lastChatClientMessageId = -1;
    } else {
      // 将普通消息处理为发送中状态
      lastChatStatus = MessageStatus.Sending
      lastChatClientMessageId = message.cmid
    }

    to.localInfo.messageField.summary = ContactUtils.getSingleChatLastText(to, message, false)
    to.localInfo.messageField.msgState = lastChatStatus
    to.localInfo.messageField.cmid = lastChatClientMessageId
    let lastChatTime = Date.now()
    to.datetime = lastChatTime
    to.localInfo.messageField.chatTime = lastChatTime
    to.localInfo.setHaveSentToFriend()

    // 保存 & 通知联系人刷新
    await this.contactDao.upsert(to)
  }

  private async handleReadMessageResult(message: ReadMessage, success: boolean) {
    if (success && this.messageDao && this.contactDao) {
      // 该好友发送成功的所有消息置为已读
      await this.messageDao.setAllMessageSentToMeRead(message.friendId, message.friendSource, message.messageId)

      // 该联系人的未读消息数清零 & 如果是最后一条消息则置为已读
      await this.contactDao.setAllMessagesRead(message.friendId, message.friendSource, message.messageId)
    }
  }
}