import * as $protobuf from '@ohos/protobufjs';
import Long = require("long");
/** Properties of a TechwolfUser. */
export interface ITechwolfUser {

    /** TechwolfUser uid */
    uid: (number|Long);

    /** TechwolfUser name */
    name?: (string|null);

    /** TechwolfUser avatar */
    avatar?: (string|null);

    /** TechwolfUser company */
    company?: (string|null);

    /** TechwolfUser headImg */
    headImg?: (number|null);

    /** TechwolfUser certification */
    certification?: (number|null);

    /** TechwolfUser source */
    source?: (number|null);
}

/** Represents a TechwolfUser. */
export class TechwolfUser implements ITechwolfUser {

    /**
     * Constructs a new TechwolfUser.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfUser);

    /** TechwolfUser uid. */
    public uid: (number|Long);

    /** TechwolfUser name. */
    public name: string;

    /** TechwolfUser avatar. */
    public avatar: string;

    /** TechwolfUser company. */
    public company: string;

    /** TechwolfUser headImg. */
    public headImg: number;

    /** TechwolfUser certification. */
    public certification: number;

    /** TechwolfUser source. */
    public source: number;

    /**
     * Creates a new TechwolfUser instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfUser instance
     */
    public static create(properties?: ITechwolfUser): TechwolfUser;

    /**
     * Encodes the specified TechwolfUser message. Does not implicitly {@link TechwolfUser.verify|verify} messages.
     * @param message TechwolfUser message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfUser, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfUser message, length delimited. Does not implicitly {@link TechwolfUser.verify|verify} messages.
     * @param message TechwolfUser message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfUser, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfUser message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfUser;

    /**
     * Decodes a TechwolfUser message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfUser;

    /**
     * Verifies a TechwolfUser message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfUser message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfUser
     */
    public static fromObject(object: { [k: string]: any }): TechwolfUser;

    /**
     * Creates a plain object from a TechwolfUser message. Also converts values to other types if specified.
     * @param message TechwolfUser
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfUser, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfUser to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfUser
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfSound. */
export interface ITechwolfSound {

    /** TechwolfSound sid */
    sid?: (number|Long|null);

    /** TechwolfSound url */
    url?: (string|null);

    /** TechwolfSound duration */
    duration?: (number|null);

    /** TechwolfSound templateId */
    templateId?: (number|null);
}

/** Represents a TechwolfSound. */
export class TechwolfSound implements ITechwolfSound {

    /**
     * Constructs a new TechwolfSound.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfSound);

    /** TechwolfSound sid. */
    public sid: (number|Long);

    /** TechwolfSound url. */
    public url: string;

    /** TechwolfSound duration. */
    public duration: number;

    /** TechwolfSound templateId. */
    public templateId: number;

    /**
     * Creates a new TechwolfSound instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfSound instance
     */
    public static create(properties?: ITechwolfSound): TechwolfSound;

    /**
     * Encodes the specified TechwolfSound message. Does not implicitly {@link TechwolfSound.verify|verify} messages.
     * @param message TechwolfSound message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfSound, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfSound message, length delimited. Does not implicitly {@link TechwolfSound.verify|verify} messages.
     * @param message TechwolfSound message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfSound, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfSound message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfSound
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfSound;

    /**
     * Decodes a TechwolfSound message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfSound
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfSound;

    /**
     * Verifies a TechwolfSound message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfSound message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfSound
     */
    public static fromObject(object: { [k: string]: any }): TechwolfSound;

    /**
     * Creates a plain object from a TechwolfSound message. Also converts values to other types if specified.
     * @param message TechwolfSound
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfSound, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfSound to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfSound
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfVideo. */
export interface ITechwolfVideo {

    /** TechwolfVideo type */
    type: number;

    /** TechwolfVideo status */
    status: number;

    /** TechwolfVideo duration */
    duration?: (number|null);

    /** TechwolfVideo text */
    text?: (string|null);
}

/** Represents a TechwolfVideo. */
export class TechwolfVideo implements ITechwolfVideo {

    /**
     * Constructs a new TechwolfVideo.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfVideo);

    /** TechwolfVideo type. */
    public type: number;

    /** TechwolfVideo status. */
    public status: number;

    /** TechwolfVideo duration. */
    public duration: number;

    /** TechwolfVideo text. */
    public text: string;

    /**
     * Creates a new TechwolfVideo instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfVideo instance
     */
    public static create(properties?: ITechwolfVideo): TechwolfVideo;

    /**
     * Encodes the specified TechwolfVideo message. Does not implicitly {@link TechwolfVideo.verify|verify} messages.
     * @param message TechwolfVideo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfVideo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfVideo message, length delimited. Does not implicitly {@link TechwolfVideo.verify|verify} messages.
     * @param message TechwolfVideo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfVideo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfVideo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfVideo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfVideo;

    /**
     * Decodes a TechwolfVideo message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfVideo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfVideo;

    /**
     * Verifies a TechwolfVideo message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfVideo message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfVideo
     */
    public static fromObject(object: { [k: string]: any }): TechwolfVideo;

    /**
     * Creates a plain object from a TechwolfVideo message. Also converts values to other types if specified.
     * @param message TechwolfVideo
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfVideo, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfVideo to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfVideo
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfInterview. */
export interface ITechwolfInterview {

    /** TechwolfInterview condition */
    condition: number;

    /** TechwolfInterview text */
    text: string;

    /** TechwolfInterview url */
    url?: (string|null);

    /** TechwolfInterview extend */
    extend?: (string|null);
}

/** Represents a TechwolfInterview. */
export class TechwolfInterview implements ITechwolfInterview {

    /**
     * Constructs a new TechwolfInterview.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfInterview);

    /** TechwolfInterview condition. */
    public condition: number;

    /** TechwolfInterview text. */
    public text: string;

    /** TechwolfInterview url. */
    public url: string;

    /** TechwolfInterview extend. */
    public extend: string;

    /**
     * Creates a new TechwolfInterview instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfInterview instance
     */
    public static create(properties?: ITechwolfInterview): TechwolfInterview;

    /**
     * Encodes the specified TechwolfInterview message. Does not implicitly {@link TechwolfInterview.verify|verify} messages.
     * @param message TechwolfInterview message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfInterview, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfInterview message, length delimited. Does not implicitly {@link TechwolfInterview.verify|verify} messages.
     * @param message TechwolfInterview message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfInterview, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfInterview message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfInterview
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfInterview;

    /**
     * Decodes a TechwolfInterview message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfInterview
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfInterview;

    /**
     * Verifies a TechwolfInterview message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfInterview message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfInterview
     */
    public static fromObject(object: { [k: string]: any }): TechwolfInterview;

    /**
     * Creates a plain object from a TechwolfInterview message. Also converts values to other types if specified.
     * @param message TechwolfInterview
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfInterview, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfInterview to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfInterview
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfImageInfo. */
export interface ITechwolfImageInfo {

    /** TechwolfImageInfo url */
    url: string;

    /** TechwolfImageInfo width */
    width: number;

    /** TechwolfImageInfo height */
    height: number;
}

/** Represents a TechwolfImageInfo. */
export class TechwolfImageInfo implements ITechwolfImageInfo {

    /**
     * Constructs a new TechwolfImageInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfImageInfo);

    /** TechwolfImageInfo url. */
    public url: string;

    /** TechwolfImageInfo width. */
    public width: number;

    /** TechwolfImageInfo height. */
    public height: number;

    /**
     * Creates a new TechwolfImageInfo instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfImageInfo instance
     */
    public static create(properties?: ITechwolfImageInfo): TechwolfImageInfo;

    /**
     * Encodes the specified TechwolfImageInfo message. Does not implicitly {@link TechwolfImageInfo.verify|verify} messages.
     * @param message TechwolfImageInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfImageInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfImageInfo message, length delimited. Does not implicitly {@link TechwolfImageInfo.verify|verify} messages.
     * @param message TechwolfImageInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfImageInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfImageInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfImageInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfImageInfo;

    /**
     * Decodes a TechwolfImageInfo message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfImageInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfImageInfo;

    /**
     * Verifies a TechwolfImageInfo message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfImageInfo message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfImageInfo
     */
    public static fromObject(object: { [k: string]: any }): TechwolfImageInfo;

    /**
     * Creates a plain object from a TechwolfImageInfo message. Also converts values to other types if specified.
     * @param message TechwolfImageInfo
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfImageInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfImageInfo to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfImageInfo
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfImage. */
export interface ITechwolfImage {

    /** TechwolfImage iid */
    iid?: (number|Long|null);

    /** TechwolfImage tinyImage */
    tinyImage?: (ITechwolfImageInfo|null);

    /** TechwolfImage originImage */
    originImage?: (ITechwolfImageInfo|null);
}

/** Represents a TechwolfImage. */
export class TechwolfImage implements ITechwolfImage {

    /**
     * Constructs a new TechwolfImage.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfImage);

    /** TechwolfImage iid. */
    public iid: (number|Long);

    /** TechwolfImage tinyImage. */
    public tinyImage?: (ITechwolfImageInfo|null);

    /** TechwolfImage originImage. */
    public originImage?: (ITechwolfImageInfo|null);

    /**
     * Creates a new TechwolfImage instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfImage instance
     */
    public static create(properties?: ITechwolfImage): TechwolfImage;

    /**
     * Encodes the specified TechwolfImage message. Does not implicitly {@link TechwolfImage.verify|verify} messages.
     * @param message TechwolfImage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfImage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfImage message, length delimited. Does not implicitly {@link TechwolfImage.verify|verify} messages.
     * @param message TechwolfImage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfImage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfImage message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfImage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfImage;

    /**
     * Decodes a TechwolfImage message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfImage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfImage;

    /**
     * Verifies a TechwolfImage message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfImage message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfImage
     */
    public static fromObject(object: { [k: string]: any }): TechwolfImage;

    /**
     * Creates a plain object from a TechwolfImage message. Also converts values to other types if specified.
     * @param message TechwolfImage
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfImage, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfImage to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfImage
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfAction. */
export interface ITechwolfAction {

    /** TechwolfAction aid */
    aid: number;

    /** TechwolfAction extend */
    extend?: (string|null);
}

/** Represents a TechwolfAction. */
export class TechwolfAction implements ITechwolfAction {

    /**
     * Constructs a new TechwolfAction.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfAction);

    /** TechwolfAction aid. */
    public aid: number;

    /** TechwolfAction extend. */
    public extend: string;

    /**
     * Creates a new TechwolfAction instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfAction instance
     */
    public static create(properties?: ITechwolfAction): TechwolfAction;

    /**
     * Encodes the specified TechwolfAction message. Does not implicitly {@link TechwolfAction.verify|verify} messages.
     * @param message TechwolfAction message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfAction, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfAction message, length delimited. Does not implicitly {@link TechwolfAction.verify|verify} messages.
     * @param message TechwolfAction message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfAction, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfAction message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfAction
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfAction;

    /**
     * Decodes a TechwolfAction message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfAction
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfAction;

    /**
     * Verifies a TechwolfAction message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfAction message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfAction
     */
    public static fromObject(object: { [k: string]: any }): TechwolfAction;

    /**
     * Creates a plain object from a TechwolfAction message. Also converts values to other types if specified.
     * @param message TechwolfAction
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfAction, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfAction to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfAction
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfArticle. */
export interface ITechwolfArticle {

    /** TechwolfArticle title */
    title: string;

    /** TechwolfArticle description */
    description: string;

    /** TechwolfArticle picUrl */
    picUrl: string;

    /** TechwolfArticle url */
    url: string;

    /** TechwolfArticle templateId */
    templateId?: (number|null);

    /** TechwolfArticle bottomText */
    bottomText?: (string|null);

    /** TechwolfArticle timeout */
    timeout?: (number|Long|null);

    /** TechwolfArticle statisticParameters */
    statisticParameters?: (string|null);

    /** TechwolfArticle highlightParts */
    highlightParts?: (ITechwolfSlice[]|null);

    /** TechwolfArticle dimParts */
    dimParts?: (ITechwolfSlice[]|null);

    /** TechwolfArticle subTitle */
    subTitle?: (string|null);

    /** TechwolfArticle extend */
    extend?: (string|null);
}

/** Represents a TechwolfArticle. */
export class TechwolfArticle implements ITechwolfArticle {

    /**
     * Constructs a new TechwolfArticle.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfArticle);

    /** TechwolfArticle title. */
    public title: string;

    /** TechwolfArticle description. */
    public description: string;

    /** TechwolfArticle picUrl. */
    public picUrl: string;

    /** TechwolfArticle url. */
    public url: string;

    /** TechwolfArticle templateId. */
    public templateId: number;

    /** TechwolfArticle bottomText. */
    public bottomText: string;

    /** TechwolfArticle timeout. */
    public timeout: (number|Long);

    /** TechwolfArticle statisticParameters. */
    public statisticParameters: string;

    /** TechwolfArticle highlightParts. */
    public highlightParts: ITechwolfSlice[];

    /** TechwolfArticle dimParts. */
    public dimParts: ITechwolfSlice[];

    /** TechwolfArticle subTitle. */
    public subTitle: string;

    /** TechwolfArticle extend. */
    public extend: string;

    /**
     * Creates a new TechwolfArticle instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfArticle instance
     */
    public static create(properties?: ITechwolfArticle): TechwolfArticle;

    /**
     * Encodes the specified TechwolfArticle message. Does not implicitly {@link TechwolfArticle.verify|verify} messages.
     * @param message TechwolfArticle message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfArticle, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfArticle message, length delimited. Does not implicitly {@link TechwolfArticle.verify|verify} messages.
     * @param message TechwolfArticle message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfArticle, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfArticle message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfArticle
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfArticle;

    /**
     * Decodes a TechwolfArticle message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfArticle
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfArticle;

    /**
     * Verifies a TechwolfArticle message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfArticle message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfArticle
     */
    public static fromObject(object: { [k: string]: any }): TechwolfArticle;

    /**
     * Creates a plain object from a TechwolfArticle message. Also converts values to other types if specified.
     * @param message TechwolfArticle
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfArticle, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfArticle to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfArticle
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfNotify. */
export interface ITechwolfNotify {

    /** TechwolfNotify text */
    text: string;

    /** TechwolfNotify url */
    url?: (string|null);

    /** TechwolfNotify title */
    title?: (string|null);
}

/** Represents a TechwolfNotify. */
export class TechwolfNotify implements ITechwolfNotify {

    /**
     * Constructs a new TechwolfNotify.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfNotify);

    /** TechwolfNotify text. */
    public text: string;

    /** TechwolfNotify url. */
    public url: string;

    /** TechwolfNotify title. */
    public title: string;

    /**
     * Creates a new TechwolfNotify instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfNotify instance
     */
    public static create(properties?: ITechwolfNotify): TechwolfNotify;

    /**
     * Encodes the specified TechwolfNotify message. Does not implicitly {@link TechwolfNotify.verify|verify} messages.
     * @param message TechwolfNotify message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfNotify, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfNotify message, length delimited. Does not implicitly {@link TechwolfNotify.verify|verify} messages.
     * @param message TechwolfNotify message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfNotify, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfNotify message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfNotify
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfNotify;

    /**
     * Decodes a TechwolfNotify message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfNotify
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfNotify;

    /**
     * Verifies a TechwolfNotify message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfNotify message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfNotify
     */
    public static fromObject(object: { [k: string]: any }): TechwolfNotify;

    /**
     * Creates a plain object from a TechwolfNotify message. Also converts values to other types if specified.
     * @param message TechwolfNotify
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfNotify, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfNotify to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfNotify
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfButton. */
export interface ITechwolfButton {

    /** TechwolfButton text */
    text: string;

    /** TechwolfButton url */
    url?: (string|null);

    /** TechwolfButton templateId */
    templateId?: (number|null);
}

/** Represents a TechwolfButton. */
export class TechwolfButton implements ITechwolfButton {

    /**
     * Constructs a new TechwolfButton.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfButton);

    /** TechwolfButton text. */
    public text: string;

    /** TechwolfButton url. */
    public url: string;

    /** TechwolfButton templateId. */
    public templateId: number;

    /**
     * Creates a new TechwolfButton instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfButton instance
     */
    public static create(properties?: ITechwolfButton): TechwolfButton;

    /**
     * Encodes the specified TechwolfButton message. Does not implicitly {@link TechwolfButton.verify|verify} messages.
     * @param message TechwolfButton message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfButton, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfButton message, length delimited. Does not implicitly {@link TechwolfButton.verify|verify} messages.
     * @param message TechwolfButton message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfButton, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfButton message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfButton
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfButton;

    /**
     * Decodes a TechwolfButton message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfButton
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfButton;

    /**
     * Verifies a TechwolfButton message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfButton message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfButton
     */
    public static fromObject(object: { [k: string]: any }): TechwolfButton;

    /**
     * Creates a plain object from a TechwolfButton message. Also converts values to other types if specified.
     * @param message TechwolfButton
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfButton, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfButton to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfButton
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfDialog. */
export interface ITechwolfDialog {

    /** TechwolfDialog text */
    text: string;

    /** TechwolfDialog buttons */
    buttons?: (ITechwolfButton[]|null);

    /** TechwolfDialog operated */
    operated: boolean;

    /** TechwolfDialog clickMore */
    clickMore?: (boolean|null);

    /** TechwolfDialog type */
    type?: (number|null);

    /** TechwolfDialog backgroundUrl */
    backgroundUrl?: (string|null);

    /** TechwolfDialog timeout */
    timeout?: (number|Long|null);

    /** TechwolfDialog statisticParameters */
    statisticParameters?: (string|null);

    /** TechwolfDialog title */
    title?: (string|null);

    /** TechwolfDialog url */
    url?: (string|null);

    /** TechwolfDialog selectedIndex */
    selectedIndex?: (number|null);

    /** TechwolfDialog extend */
    extend?: (string|null);

    /** TechwolfDialog content */
    content?: (string|null);
}

/** Represents a TechwolfDialog. */
export class TechwolfDialog implements ITechwolfDialog {

    /**
     * Constructs a new TechwolfDialog.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfDialog);

    /** TechwolfDialog text. */
    public text: string;

    /** TechwolfDialog buttons. */
    public buttons: ITechwolfButton[];

    /** TechwolfDialog operated. */
    public operated: boolean;

    /** TechwolfDialog clickMore. */
    public clickMore: boolean;

    /** TechwolfDialog type. */
    public type: number;

    /** TechwolfDialog backgroundUrl. */
    public backgroundUrl: string;

    /** TechwolfDialog timeout. */
    public timeout: (number|Long);

    /** TechwolfDialog statisticParameters. */
    public statisticParameters: string;

    /** TechwolfDialog title. */
    public title: string;

    /** TechwolfDialog url. */
    public url: string;

    /** TechwolfDialog selectedIndex. */
    public selectedIndex: number;

    /** TechwolfDialog extend. */
    public extend: string;

    /** TechwolfDialog content. */
    public content: string;

    /**
     * Creates a new TechwolfDialog instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfDialog instance
     */
    public static create(properties?: ITechwolfDialog): TechwolfDialog;

    /**
     * Encodes the specified TechwolfDialog message. Does not implicitly {@link TechwolfDialog.verify|verify} messages.
     * @param message TechwolfDialog message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfDialog, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfDialog message, length delimited. Does not implicitly {@link TechwolfDialog.verify|verify} messages.
     * @param message TechwolfDialog message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfDialog, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfDialog message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfDialog
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfDialog;

    /**
     * Decodes a TechwolfDialog message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfDialog
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfDialog;

    /**
     * Verifies a TechwolfDialog message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfDialog message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfDialog
     */
    public static fromObject(object: { [k: string]: any }): TechwolfDialog;

    /**
     * Creates a plain object from a TechwolfDialog message. Also converts values to other types if specified.
     * @param message TechwolfDialog
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfDialog, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfDialog to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfDialog
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfJobDesc. */
export interface ITechwolfJobDesc {

    /** TechwolfJobDesc title */
    title: string;

    /** TechwolfJobDesc company */
    company: string;

    /** TechwolfJobDesc salary */
    salary: string;

    /** TechwolfJobDesc url */
    url: string;

    /** TechwolfJobDesc jobId */
    jobId: (number|Long);

    /** TechwolfJobDesc positionCategory */
    positionCategory?: (string|null);

    /** TechwolfJobDesc experience */
    experience?: (string|null);

    /** TechwolfJobDesc education */
    education?: (string|null);

    /** TechwolfJobDesc city */
    city?: (string|null);

    /** TechwolfJobDesc bossTitle */
    bossTitle?: (string|null);

    /** TechwolfJobDesc boss */
    boss?: (ITechwolfUser|null);

    /** TechwolfJobDesc lid */
    lid?: (string|null);

    /** TechwolfJobDesc stage */
    stage?: (string|null);

    /** TechwolfJobDesc bottomText */
    bottomText?: (string|null);

    /** TechwolfJobDesc jobLabel */
    jobLabel?: (string|null);

    /** TechwolfJobDesc iconFlag */
    iconFlag?: (number|null);

    /** TechwolfJobDesc content */
    content?: (string|null);

    /** TechwolfJobDesc labels */
    labels?: (string[]|null);

    /** TechwolfJobDesc expectId */
    expectId?: (number|Long|null);

    /** TechwolfJobDesc expectPosition */
    expectPosition?: (string|null);

    /** TechwolfJobDesc expectSalary */
    expectSalary?: (string|null);

    /** TechwolfJobDesc partTimeDesc */
    partTimeDesc?: (string|null);

    /** TechwolfJobDesc geek */
    geek?: (ITechwolfUser|null);

    /** TechwolfJobDesc latlon */
    latlon?: (string|null);

    /** TechwolfJobDesc distance */
    distance?: (string|null);

    /** TechwolfJobDesc extend */
    extend?: (string|null);
}

/** Represents a TechwolfJobDesc. */
export class TechwolfJobDesc implements ITechwolfJobDesc {

    /**
     * Constructs a new TechwolfJobDesc.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfJobDesc);

    /** TechwolfJobDesc title. */
    public title: string;

    /** TechwolfJobDesc company. */
    public company: string;

    /** TechwolfJobDesc salary. */
    public salary: string;

    /** TechwolfJobDesc url. */
    public url: string;

    /** TechwolfJobDesc jobId. */
    public jobId: (number|Long);

    /** TechwolfJobDesc positionCategory. */
    public positionCategory: string;

    /** TechwolfJobDesc experience. */
    public experience: string;

    /** TechwolfJobDesc education. */
    public education: string;

    /** TechwolfJobDesc city. */
    public city: string;

    /** TechwolfJobDesc bossTitle. */
    public bossTitle: string;

    /** TechwolfJobDesc boss. */
    public boss?: (ITechwolfUser|null);

    /** TechwolfJobDesc lid. */
    public lid: string;

    /** TechwolfJobDesc stage. */
    public stage: string;

    /** TechwolfJobDesc bottomText. */
    public bottomText: string;

    /** TechwolfJobDesc jobLabel. */
    public jobLabel: string;

    /** TechwolfJobDesc iconFlag. */
    public iconFlag: number;

    /** TechwolfJobDesc content. */
    public content: string;

    /** TechwolfJobDesc labels. */
    public labels: string[];

    /** TechwolfJobDesc expectId. */
    public expectId: (number|Long);

    /** TechwolfJobDesc expectPosition. */
    public expectPosition: string;

    /** TechwolfJobDesc expectSalary. */
    public expectSalary: string;

    /** TechwolfJobDesc partTimeDesc. */
    public partTimeDesc: string;

    /** TechwolfJobDesc geek. */
    public geek?: (ITechwolfUser|null);

    /** TechwolfJobDesc latlon. */
    public latlon: string;

    /** TechwolfJobDesc distance. */
    public distance: string;

    /** TechwolfJobDesc extend. */
    public extend: string;

    /**
     * Creates a new TechwolfJobDesc instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfJobDesc instance
     */
    public static create(properties?: ITechwolfJobDesc): TechwolfJobDesc;

    /**
     * Encodes the specified TechwolfJobDesc message. Does not implicitly {@link TechwolfJobDesc.verify|verify} messages.
     * @param message TechwolfJobDesc message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfJobDesc, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfJobDesc message, length delimited. Does not implicitly {@link TechwolfJobDesc.verify|verify} messages.
     * @param message TechwolfJobDesc message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfJobDesc, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfJobDesc message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfJobDesc
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfJobDesc;

    /**
     * Decodes a TechwolfJobDesc message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfJobDesc
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfJobDesc;

    /**
     * Verifies a TechwolfJobDesc message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfJobDesc message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfJobDesc
     */
    public static fromObject(object: { [k: string]: any }): TechwolfJobDesc;

    /**
     * Creates a plain object from a TechwolfJobDesc message. Also converts values to other types if specified.
     * @param message TechwolfJobDesc
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfJobDesc, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfJobDesc to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfJobDesc
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfResume. */
export interface ITechwolfResume {

    /** TechwolfResume user */
    user: ITechwolfUser;

    /** TechwolfResume description */
    description?: (string|null);

    /** TechwolfResume city */
    city?: (string|null);

    /** TechwolfResume position */
    position?: (string|null);

    /** TechwolfResume keywords */
    keywords?: (string[]|null);

    /** TechwolfResume expectId */
    expectId?: (number|Long|null);

    /** TechwolfResume lid */
    lid?: (string|null);

    /** TechwolfResume gender */
    gender?: (number|null);

    /** TechwolfResume salary */
    salary?: (string|null);

    /** TechwolfResume workYear */
    workYear?: (string|null);

    /** TechwolfResume content1 */
    content1?: (string|null);

    /** TechwolfResume content2 */
    content2?: (string|null);

    /** TechwolfResume education */
    education?: (string|null);

    /** TechwolfResume age */
    age?: (string|null);

    /** TechwolfResume labels */
    labels?: (string[]|null);

    /** TechwolfResume experiences */
    experiences?: (IUserExperience[]|null);

    /** TechwolfResume positionCategory */
    positionCategory?: (string|null);

    /** TechwolfResume jobSalary */
    jobSalary?: (string|null);

    /** TechwolfResume bottomText */
    bottomText?: (string|null);

    /** TechwolfResume applyStatus */
    applyStatus?: (string|null);

    /** TechwolfResume jobId */
    jobId?: (number|Long|null);

    /** TechwolfResume content3 */
    content3?: (string|null);

    /** TechwolfResume securityId */
    securityId?: (string|null);

    /** TechwolfResume boss */
    boss?: (ITechwolfUser|null);

    /** TechwolfResume brandName */
    brandName?: (string|null);
}

/** Represents a TechwolfResume. */
export class TechwolfResume implements ITechwolfResume {

    /**
     * Constructs a new TechwolfResume.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfResume);

    /** TechwolfResume user. */
    public user: ITechwolfUser;

    /** TechwolfResume description. */
    public description: string;

    /** TechwolfResume city. */
    public city: string;

    /** TechwolfResume position. */
    public position: string;

    /** TechwolfResume keywords. */
    public keywords: string[];

    /** TechwolfResume expectId. */
    public expectId: (number|Long);

    /** TechwolfResume lid. */
    public lid: string;

    /** TechwolfResume gender. */
    public gender: number;

    /** TechwolfResume salary. */
    public salary: string;

    /** TechwolfResume workYear. */
    public workYear: string;

    /** TechwolfResume content1. */
    public content1: string;

    /** TechwolfResume content2. */
    public content2: string;

    /** TechwolfResume education. */
    public education: string;

    /** TechwolfResume age. */
    public age: string;

    /** TechwolfResume labels. */
    public labels: string[];

    /** TechwolfResume experiences. */
    public experiences: IUserExperience[];

    /** TechwolfResume positionCategory. */
    public positionCategory: string;

    /** TechwolfResume jobSalary. */
    public jobSalary: string;

    /** TechwolfResume bottomText. */
    public bottomText: string;

    /** TechwolfResume applyStatus. */
    public applyStatus: string;

    /** TechwolfResume jobId. */
    public jobId: (number|Long);

    /** TechwolfResume content3. */
    public content3: string;

    /** TechwolfResume securityId. */
    public securityId: string;

    /** TechwolfResume boss. */
    public boss?: (ITechwolfUser|null);

    /** TechwolfResume brandName. */
    public brandName: string;

    /**
     * Creates a new TechwolfResume instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfResume instance
     */
    public static create(properties?: ITechwolfResume): TechwolfResume;

    /**
     * Encodes the specified TechwolfResume message. Does not implicitly {@link TechwolfResume.verify|verify} messages.
     * @param message TechwolfResume message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfResume, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfResume message, length delimited. Does not implicitly {@link TechwolfResume.verify|verify} messages.
     * @param message TechwolfResume message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfResume, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfResume message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfResume
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfResume;

    /**
     * Decodes a TechwolfResume message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfResume
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfResume;

    /**
     * Verifies a TechwolfResume message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfResume message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfResume
     */
    public static fromObject(object: { [k: string]: any }): TechwolfResume;

    /**
     * Creates a plain object from a TechwolfResume message. Also converts values to other types if specified.
     * @param message TechwolfResume
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfResume, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfResume to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfResume
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfHyperLink. */
export interface ITechwolfHyperLink {

    /** TechwolfHyperLink text */
    text: string;

    /** TechwolfHyperLink url */
    url: string;

    /** TechwolfHyperLink hyperLinkType */
    hyperLinkType: number;

    /** TechwolfHyperLink extraJson */
    extraJson?: (string|null);
}

/** Represents a TechwolfHyperLink. */
export class TechwolfHyperLink implements ITechwolfHyperLink {

    /**
     * Constructs a new TechwolfHyperLink.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfHyperLink);

    /** TechwolfHyperLink text. */
    public text: string;

    /** TechwolfHyperLink url. */
    public url: string;

    /** TechwolfHyperLink hyperLinkType. */
    public hyperLinkType: number;

    /** TechwolfHyperLink extraJson. */
    public extraJson: string;

    /**
     * Creates a new TechwolfHyperLink instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfHyperLink instance
     */
    public static create(properties?: ITechwolfHyperLink): TechwolfHyperLink;

    /**
     * Encodes the specified TechwolfHyperLink message. Does not implicitly {@link TechwolfHyperLink.verify|verify} messages.
     * @param message TechwolfHyperLink message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfHyperLink, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfHyperLink message, length delimited. Does not implicitly {@link TechwolfHyperLink.verify|verify} messages.
     * @param message TechwolfHyperLink message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfHyperLink, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfHyperLink message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfHyperLink
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfHyperLink;

    /**
     * Decodes a TechwolfHyperLink message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfHyperLink
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfHyperLink;

    /**
     * Verifies a TechwolfHyperLink message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfHyperLink message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfHyperLink
     */
    public static fromObject(object: { [k: string]: any }): TechwolfHyperLink;

    /**
     * Creates a plain object from a TechwolfHyperLink message. Also converts values to other types if specified.
     * @param message TechwolfHyperLink
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfHyperLink, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfHyperLink to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfHyperLink
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfMessageBody. */
export interface ITechwolfMessageBody {

    /** TechwolfMessageBody type */
    type: number;

    /** TechwolfMessageBody templateId */
    templateId: number;

    /** TechwolfMessageBody headTitle */
    headTitle?: (string|null);

    /** TechwolfMessageBody text */
    text?: (string|null);

    /** TechwolfMessageBody sound */
    sound?: (ITechwolfSound|null);

    /** TechwolfMessageBody image */
    image?: (ITechwolfImage|null);

    /** TechwolfMessageBody action */
    action?: (ITechwolfAction|null);

    /** TechwolfMessageBody articles */
    articles?: (ITechwolfArticle[]|null);

    /** TechwolfMessageBody notify */
    notify?: (ITechwolfNotify|null);

    /** TechwolfMessageBody dialog */
    dialog?: (ITechwolfDialog|null);

    /** TechwolfMessageBody jobDesc */
    jobDesc?: (ITechwolfJobDesc|null);

    /** TechwolfMessageBody resume */
    resume?: (ITechwolfResume|null);

    /** TechwolfMessageBody redEnvelope */
    redEnvelope?: (ITechwolfRedEnvelope|null);

    /** TechwolfMessageBody orderDetail */
    orderDetail?: (ITechwolfOrderDetail|null);

    /** TechwolfMessageBody hyperLink */
    hyperLink?: (ITechwolfHyperLink|null);

    /** TechwolfMessageBody video */
    video?: (ITechwolfVideo|null);

    /** TechwolfMessageBody interview */
    interview?: (ITechwolfInterview|null);

    /** TechwolfMessageBody jobShare */
    jobShare?: (ITechwolfJobShare|null);

    /** TechwolfMessageBody resumeShare */
    resumeShare?: (ITechwolfResumeShare|null);

    /** TechwolfMessageBody atInfo */
    atInfo?: (IAtInfo|null);

    /** TechwolfMessageBody sticker */
    sticker?: (ITechwolfSticker|null);

    /** TechwolfMessageBody chatShare */
    chatShare?: (ITechwolfChatShare|null);

    /** TechwolfMessageBody interviewShare */
    interviewShare?: (ITechwolfInterviewShare|null);

    /** TechwolfMessageBody listCard */
    listCard?: (ITechwolfListCard|null);

    /** TechwolfMessageBody starRate */
    starRate?: (ITechwolfStarRate|null);

    /** TechwolfMessageBody frame */
    frame?: (ITechwolfFrame|null);

    /** TechwolfMessageBody multiImage */
    multiImage?: (ITechwolfMultiImage|null);

    /** TechwolfMessageBody extend */
    extend?: (string|null);

    /** TechwolfMessageBody style */
    style?: (number|null);

    /** TechwolfMessageBody comDesc */
    comDesc?: (ITechwolfComDesc|null);

    /** TechwolfMessageBody userCard */
    userCard?: (ITechwolfUserCard|null);
}

/** Represents a TechwolfMessageBody. */
export class TechwolfMessageBody implements ITechwolfMessageBody {

    /**
     * Constructs a new TechwolfMessageBody.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfMessageBody);

    /** TechwolfMessageBody type. */
    public type: number;

    /** TechwolfMessageBody templateId. */
    public templateId: number;

    /** TechwolfMessageBody headTitle. */
    public headTitle: string;

    /** TechwolfMessageBody text. */
    public text: string;

    /** TechwolfMessageBody sound. */
    public sound?: (ITechwolfSound|null);

    /** TechwolfMessageBody image. */
    public image?: (ITechwolfImage|null);

    /** TechwolfMessageBody action. */
    public action?: (ITechwolfAction|null);

    /** TechwolfMessageBody articles. */
    public articles: ITechwolfArticle[];

    /** TechwolfMessageBody notify. */
    public notify?: (ITechwolfNotify|null);

    /** TechwolfMessageBody dialog. */
    public dialog?: (ITechwolfDialog|null);

    /** TechwolfMessageBody jobDesc. */
    public jobDesc?: (ITechwolfJobDesc|null);

    /** TechwolfMessageBody resume. */
    public resume?: (ITechwolfResume|null);

    /** TechwolfMessageBody redEnvelope. */
    public redEnvelope?: (ITechwolfRedEnvelope|null);

    /** TechwolfMessageBody orderDetail. */
    public orderDetail?: (ITechwolfOrderDetail|null);

    /** TechwolfMessageBody hyperLink. */
    public hyperLink?: (ITechwolfHyperLink|null);

    /** TechwolfMessageBody video. */
    public video?: (ITechwolfVideo|null);

    /** TechwolfMessageBody interview. */
    public interview?: (ITechwolfInterview|null);

    /** TechwolfMessageBody jobShare. */
    public jobShare?: (ITechwolfJobShare|null);

    /** TechwolfMessageBody resumeShare. */
    public resumeShare?: (ITechwolfResumeShare|null);

    /** TechwolfMessageBody atInfo. */
    public atInfo?: (IAtInfo|null);

    /** TechwolfMessageBody sticker. */
    public sticker?: (ITechwolfSticker|null);

    /** TechwolfMessageBody chatShare. */
    public chatShare?: (ITechwolfChatShare|null);

    /** TechwolfMessageBody interviewShare. */
    public interviewShare?: (ITechwolfInterviewShare|null);

    /** TechwolfMessageBody listCard. */
    public listCard?: (ITechwolfListCard|null);

    /** TechwolfMessageBody starRate. */
    public starRate?: (ITechwolfStarRate|null);

    /** TechwolfMessageBody frame. */
    public frame?: (ITechwolfFrame|null);

    /** TechwolfMessageBody multiImage. */
    public multiImage?: (ITechwolfMultiImage|null);

    /** TechwolfMessageBody extend. */
    public extend: string;

    /** TechwolfMessageBody style. */
    public style: number;

    /** TechwolfMessageBody comDesc. */
    public comDesc?: (ITechwolfComDesc|null);

    /** TechwolfMessageBody userCard. */
    public userCard?: (ITechwolfUserCard|null);

    /**
     * Creates a new TechwolfMessageBody instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfMessageBody instance
     */
    public static create(properties?: ITechwolfMessageBody): TechwolfMessageBody;

    /**
     * Encodes the specified TechwolfMessageBody message. Does not implicitly {@link TechwolfMessageBody.verify|verify} messages.
     * @param message TechwolfMessageBody message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfMessageBody, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfMessageBody message, length delimited. Does not implicitly {@link TechwolfMessageBody.verify|verify} messages.
     * @param message TechwolfMessageBody message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfMessageBody, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfMessageBody message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfMessageBody
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfMessageBody;

    /**
     * Decodes a TechwolfMessageBody message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfMessageBody
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfMessageBody;

    /**
     * Verifies a TechwolfMessageBody message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfMessageBody message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfMessageBody
     */
    public static fromObject(object: { [k: string]: any }): TechwolfMessageBody;

    /**
     * Creates a plain object from a TechwolfMessageBody message. Also converts values to other types if specified.
     * @param message TechwolfMessageBody
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfMessageBody, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfMessageBody to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfMessageBody
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfMessage. */
export interface ITechwolfMessage {

    /** TechwolfMessage from */
    from: ITechwolfUser;

    /** TechwolfMessage to */
    to: ITechwolfUser;

    /** TechwolfMessage type */
    type: number;

    /** TechwolfMessage mid */
    mid?: (number|Long|null);

    /** TechwolfMessage time */
    time?: (number|Long|null);

    /** TechwolfMessage body */
    body: ITechwolfMessageBody;

    /** TechwolfMessage offline */
    offline?: (boolean|null);

    /** TechwolfMessage received */
    received?: (boolean|null);

    /** TechwolfMessage pushText */
    pushText?: (string|null);

    /** TechwolfMessage taskId */
    taskId?: (number|Long|null);

    /** TechwolfMessage cmid */
    cmid?: (number|Long|null);

    /** TechwolfMessage status */
    status?: (number|null);

    /** TechwolfMessage uncount */
    uncount?: (number|null);

    /** TechwolfMessage pushSound */
    pushSound?: (number|null);

    /** TechwolfMessage flag */
    flag?: (number|null);

    /** TechwolfMessage encryptedBody */
    encryptedBody?: (Uint8Array|null);

    /** TechwolfMessage bizId */
    bizId?: (string|null);

    /** TechwolfMessage bizType */
    bizType?: (number|null);

    /** TechwolfMessage securityId */
    securityId?: (string|null);

    /** TechwolfMessage quoteId */
    quoteId?: (number|Long|null);
}

/** Represents a TechwolfMessage. */
export class TechwolfMessage implements ITechwolfMessage {

    /**
     * Constructs a new TechwolfMessage.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfMessage);

    /** TechwolfMessage from. */
    public from: ITechwolfUser;

    /** TechwolfMessage to. */
    public to: ITechwolfUser;

    /** TechwolfMessage type. */
    public type: number;

    /** TechwolfMessage mid. */
    public mid: (number|Long);

    /** TechwolfMessage time. */
    public time: (number|Long);

    /** TechwolfMessage body. */
    public body: ITechwolfMessageBody;

    /** TechwolfMessage offline. */
    public offline: boolean;

    /** TechwolfMessage received. */
    public received: boolean;

    /** TechwolfMessage pushText. */
    public pushText: string;

    /** TechwolfMessage taskId. */
    public taskId: (number|Long);

    /** TechwolfMessage cmid. */
    public cmid: (number|Long);

    /** TechwolfMessage status. */
    public status: number;

    /** TechwolfMessage uncount. */
    public uncount: number;

    /** TechwolfMessage pushSound. */
    public pushSound: number;

    /** TechwolfMessage flag. */
    public flag: number;

    /** TechwolfMessage encryptedBody. */
    public encryptedBody: Uint8Array;

    /** TechwolfMessage bizId. */
    public bizId: string;

    /** TechwolfMessage bizType. */
    public bizType: number;

    /** TechwolfMessage securityId. */
    public securityId: string;

    /** TechwolfMessage quoteId. */
    public quoteId: (number|Long);

    /**
     * Creates a new TechwolfMessage instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfMessage instance
     */
    public static create(properties?: ITechwolfMessage): TechwolfMessage;

    /**
     * Encodes the specified TechwolfMessage message. Does not implicitly {@link TechwolfMessage.verify|verify} messages.
     * @param message TechwolfMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfMessage message, length delimited. Does not implicitly {@link TechwolfMessage.verify|verify} messages.
     * @param message TechwolfMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfMessage message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfMessage;

    /**
     * Decodes a TechwolfMessage message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfMessage;

    /**
     * Verifies a TechwolfMessage message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfMessage message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfMessage
     */
    public static fromObject(object: { [k: string]: any }): TechwolfMessage;

    /**
     * Creates a plain object from a TechwolfMessage message. Also converts values to other types if specified.
     * @param message TechwolfMessage
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfMessage to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfMessage
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfClientInfo. */
export interface ITechwolfClientInfo {

    /** TechwolfClientInfo version */
    version?: (string|null);

    /** TechwolfClientInfo system */
    system?: (string|null);

    /** TechwolfClientInfo systemVersion */
    systemVersion?: (string|null);

    /** TechwolfClientInfo model */
    model?: (string|null);

    /** TechwolfClientInfo uniqid */
    uniqid?: (string|null);

    /** TechwolfClientInfo network */
    network?: (string|null);

    /** TechwolfClientInfo appid */
    appid?: (number|null);

    /** TechwolfClientInfo platform */
    platform?: (string|null);

    /** TechwolfClientInfo channel */
    channel?: (string|null);

    /** TechwolfClientInfo ssid */
    ssid?: (string|null);

    /** TechwolfClientInfo bssid */
    bssid?: (string|null);

    /** TechwolfClientInfo longitude */
    longitude?: (number|null);

    /** TechwolfClientInfo latitude */
    latitude?: (number|null);
}

/** Represents a TechwolfClientInfo. */
export class TechwolfClientInfo implements ITechwolfClientInfo {

    /**
     * Constructs a new TechwolfClientInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfClientInfo);

    /** TechwolfClientInfo version. */
    public version: string;

    /** TechwolfClientInfo system. */
    public system: string;

    /** TechwolfClientInfo systemVersion. */
    public systemVersion: string;

    /** TechwolfClientInfo model. */
    public model: string;

    /** TechwolfClientInfo uniqid. */
    public uniqid: string;

    /** TechwolfClientInfo network. */
    public network: string;

    /** TechwolfClientInfo appid. */
    public appid: number;

    /** TechwolfClientInfo platform. */
    public platform: string;

    /** TechwolfClientInfo channel. */
    public channel: string;

    /** TechwolfClientInfo ssid. */
    public ssid: string;

    /** TechwolfClientInfo bssid. */
    public bssid: string;

    /** TechwolfClientInfo longitude. */
    public longitude: number;

    /** TechwolfClientInfo latitude. */
    public latitude: number;

    /**
     * Creates a new TechwolfClientInfo instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfClientInfo instance
     */
    public static create(properties?: ITechwolfClientInfo): TechwolfClientInfo;

    /**
     * Encodes the specified TechwolfClientInfo message. Does not implicitly {@link TechwolfClientInfo.verify|verify} messages.
     * @param message TechwolfClientInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfClientInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfClientInfo message, length delimited. Does not implicitly {@link TechwolfClientInfo.verify|verify} messages.
     * @param message TechwolfClientInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfClientInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfClientInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfClientInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfClientInfo;

    /**
     * Decodes a TechwolfClientInfo message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfClientInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfClientInfo;

    /**
     * Verifies a TechwolfClientInfo message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfClientInfo message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfClientInfo
     */
    public static fromObject(object: { [k: string]: any }): TechwolfClientInfo;

    /**
     * Creates a plain object from a TechwolfClientInfo message. Also converts values to other types if specified.
     * @param message TechwolfClientInfo
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfClientInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfClientInfo to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfClientInfo
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfClientTime. */
export interface ITechwolfClientTime {

    /** TechwolfClientTime startTime */
    startTime?: (number|Long|null);

    /** TechwolfClientTime resumeTime */
    resumeTime?: (number|Long|null);

    /** TechwolfClientTime locateTime */
    locateTime?: (number|Long|null);
}

/** Represents a TechwolfClientTime. */
export class TechwolfClientTime implements ITechwolfClientTime {

    /**
     * Constructs a new TechwolfClientTime.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfClientTime);

    /** TechwolfClientTime startTime. */
    public startTime: (number|Long);

    /** TechwolfClientTime resumeTime. */
    public resumeTime: (number|Long);

    /** TechwolfClientTime locateTime. */
    public locateTime: (number|Long);

    /**
     * Creates a new TechwolfClientTime instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfClientTime instance
     */
    public static create(properties?: ITechwolfClientTime): TechwolfClientTime;

    /**
     * Encodes the specified TechwolfClientTime message. Does not implicitly {@link TechwolfClientTime.verify|verify} messages.
     * @param message TechwolfClientTime message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfClientTime, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfClientTime message, length delimited. Does not implicitly {@link TechwolfClientTime.verify|verify} messages.
     * @param message TechwolfClientTime message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfClientTime, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfClientTime message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfClientTime
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfClientTime;

    /**
     * Decodes a TechwolfClientTime message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfClientTime
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfClientTime;

    /**
     * Verifies a TechwolfClientTime message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfClientTime message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfClientTime
     */
    public static fromObject(object: { [k: string]: any }): TechwolfClientTime;

    /**
     * Creates a plain object from a TechwolfClientTime message. Also converts values to other types if specified.
     * @param message TechwolfClientTime
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfClientTime, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfClientTime to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfClientTime
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfPresence. */
export interface ITechwolfPresence {

    /** TechwolfPresence type */
    type: number;

    /** TechwolfPresence uid */
    uid: number;

    /** TechwolfPresence clientInfo */
    clientInfo?: (ITechwolfClientInfo|null);

    /** TechwolfPresence clientTime */
    clientTime?: (ITechwolfClientTime|null);

    /** TechwolfPresence lastMessageId */
    lastMessageId?: (number|Long|null);

    /** TechwolfPresence lastGroupMessageId */
    lastGroupMessageId?: (number|Long|null);

    /** TechwolfPresence userId */
    userId?: (number|Long|null);
}

/** Represents a TechwolfPresence. */
export class TechwolfPresence implements ITechwolfPresence {

    /**
     * Constructs a new TechwolfPresence.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfPresence);

    /** TechwolfPresence type. */
    public type: number;

    /** TechwolfPresence uid. */
    public uid: number;

    /** TechwolfPresence clientInfo. */
    public clientInfo?: (ITechwolfClientInfo|null);

    /** TechwolfPresence clientTime. */
    public clientTime?: (ITechwolfClientTime|null);

    /** TechwolfPresence lastMessageId. */
    public lastMessageId: (number|Long);

    /** TechwolfPresence lastGroupMessageId. */
    public lastGroupMessageId: (number|Long);

    /** TechwolfPresence userId. */
    public userId: (number|Long);

    /**
     * Creates a new TechwolfPresence instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfPresence instance
     */
    public static create(properties?: ITechwolfPresence): TechwolfPresence;

    /**
     * Encodes the specified TechwolfPresence message. Does not implicitly {@link TechwolfPresence.verify|verify} messages.
     * @param message TechwolfPresence message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfPresence, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfPresence message, length delimited. Does not implicitly {@link TechwolfPresence.verify|verify} messages.
     * @param message TechwolfPresence message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfPresence, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfPresence message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfPresence
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfPresence;

    /**
     * Decodes a TechwolfPresence message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfPresence
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfPresence;

    /**
     * Verifies a TechwolfPresence message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfPresence message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfPresence
     */
    public static fromObject(object: { [k: string]: any }): TechwolfPresence;

    /**
     * Creates a plain object from a TechwolfPresence message. Also converts values to other types if specified.
     * @param message TechwolfPresence
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfPresence, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfPresence to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfPresence
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfKVEntry. */
export interface ITechwolfKVEntry {

    /** TechwolfKVEntry key */
    key: string;

    /** TechwolfKVEntry value */
    value: string;
}

/** Represents a TechwolfKVEntry. */
export class TechwolfKVEntry implements ITechwolfKVEntry {

    /**
     * Constructs a new TechwolfKVEntry.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfKVEntry);

    /** TechwolfKVEntry key. */
    public key: string;

    /** TechwolfKVEntry value. */
    public value: string;

    /**
     * Creates a new TechwolfKVEntry instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfKVEntry instance
     */
    public static create(properties?: ITechwolfKVEntry): TechwolfKVEntry;

    /**
     * Encodes the specified TechwolfKVEntry message. Does not implicitly {@link TechwolfKVEntry.verify|verify} messages.
     * @param message TechwolfKVEntry message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfKVEntry, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfKVEntry message, length delimited. Does not implicitly {@link TechwolfKVEntry.verify|verify} messages.
     * @param message TechwolfKVEntry message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfKVEntry, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfKVEntry message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfKVEntry
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfKVEntry;

    /**
     * Decodes a TechwolfKVEntry message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfKVEntry
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfKVEntry;

    /**
     * Verifies a TechwolfKVEntry message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfKVEntry message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfKVEntry
     */
    public static fromObject(object: { [k: string]: any }): TechwolfKVEntry;

    /**
     * Creates a plain object from a TechwolfKVEntry message. Also converts values to other types if specified.
     * @param message TechwolfKVEntry
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfKVEntry, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfKVEntry to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfKVEntry
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfIq. */
export interface ITechwolfIq {

    /** TechwolfIq qid */
    qid: (number|Long);

    /** TechwolfIq query */
    query: string;

    /** TechwolfIq params */
    params?: (ITechwolfKVEntry[]|null);
}

/** Represents a TechwolfIq. */
export class TechwolfIq implements ITechwolfIq {

    /**
     * Constructs a new TechwolfIq.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfIq);

    /** TechwolfIq qid. */
    public qid: (number|Long);

    /** TechwolfIq query. */
    public query: string;

    /** TechwolfIq params. */
    public params: ITechwolfKVEntry[];

    /**
     * Creates a new TechwolfIq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfIq instance
     */
    public static create(properties?: ITechwolfIq): TechwolfIq;

    /**
     * Encodes the specified TechwolfIq message. Does not implicitly {@link TechwolfIq.verify|verify} messages.
     * @param message TechwolfIq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfIq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfIq message, length delimited. Does not implicitly {@link TechwolfIq.verify|verify} messages.
     * @param message TechwolfIq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfIq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfIq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfIq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfIq;

    /**
     * Decodes a TechwolfIq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfIq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfIq;

    /**
     * Verifies a TechwolfIq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfIq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfIq
     */
    public static fromObject(object: { [k: string]: any }): TechwolfIq;

    /**
     * Creates a plain object from a TechwolfIq message. Also converts values to other types if specified.
     * @param message TechwolfIq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfIq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfIq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfIq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfIqResponse. */
export interface ITechwolfIqResponse {

    /** TechwolfIqResponse qid */
    qid: (number|Long);

    /** TechwolfIqResponse query */
    query: string;

    /** TechwolfIqResponse results */
    results?: (ITechwolfKVEntry[]|null);
}

/** Represents a TechwolfIqResponse. */
export class TechwolfIqResponse implements ITechwolfIqResponse {

    /**
     * Constructs a new TechwolfIqResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfIqResponse);

    /** TechwolfIqResponse qid. */
    public qid: (number|Long);

    /** TechwolfIqResponse query. */
    public query: string;

    /** TechwolfIqResponse results. */
    public results: ITechwolfKVEntry[];

    /**
     * Creates a new TechwolfIqResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfIqResponse instance
     */
    public static create(properties?: ITechwolfIqResponse): TechwolfIqResponse;

    /**
     * Encodes the specified TechwolfIqResponse message. Does not implicitly {@link TechwolfIqResponse.verify|verify} messages.
     * @param message TechwolfIqResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfIqResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfIqResponse message, length delimited. Does not implicitly {@link TechwolfIqResponse.verify|verify} messages.
     * @param message TechwolfIqResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfIqResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfIqResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfIqResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfIqResponse;

    /**
     * Decodes a TechwolfIqResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfIqResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfIqResponse;

    /**
     * Verifies a TechwolfIqResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfIqResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfIqResponse
     */
    public static fromObject(object: { [k: string]: any }): TechwolfIqResponse;

    /**
     * Creates a plain object from a TechwolfIqResponse message. Also converts values to other types if specified.
     * @param message TechwolfIqResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfIqResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfIqResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfIqResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfMessageSync. */
export interface ITechwolfMessageSync {

    /** TechwolfMessageSync clientMid */
    clientMid: (number|Long);

    /** TechwolfMessageSync serverMid */
    serverMid: (number|Long);
}

/** Represents a TechwolfMessageSync. */
export class TechwolfMessageSync implements ITechwolfMessageSync {

    /**
     * Constructs a new TechwolfMessageSync.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfMessageSync);

    /** TechwolfMessageSync clientMid. */
    public clientMid: (number|Long);

    /** TechwolfMessageSync serverMid. */
    public serverMid: (number|Long);

    /**
     * Creates a new TechwolfMessageSync instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfMessageSync instance
     */
    public static create(properties?: ITechwolfMessageSync): TechwolfMessageSync;

    /**
     * Encodes the specified TechwolfMessageSync message. Does not implicitly {@link TechwolfMessageSync.verify|verify} messages.
     * @param message TechwolfMessageSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfMessageSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfMessageSync message, length delimited. Does not implicitly {@link TechwolfMessageSync.verify|verify} messages.
     * @param message TechwolfMessageSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfMessageSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfMessageSync message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfMessageSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfMessageSync;

    /**
     * Decodes a TechwolfMessageSync message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfMessageSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfMessageSync;

    /**
     * Verifies a TechwolfMessageSync message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfMessageSync message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfMessageSync
     */
    public static fromObject(object: { [k: string]: any }): TechwolfMessageSync;

    /**
     * Creates a plain object from a TechwolfMessageSync message. Also converts values to other types if specified.
     * @param message TechwolfMessageSync
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfMessageSync, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfMessageSync to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfMessageSync
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfMessageRead. */
export interface ITechwolfMessageRead {

    /** TechwolfMessageRead userId */
    userId: (number|Long);

    /** TechwolfMessageRead messageId */
    messageId: (number|Long);

    /** TechwolfMessageRead readTime */
    readTime: (number|Long);

    /** TechwolfMessageRead sync */
    sync?: (boolean|null);

    /** TechwolfMessageRead userSource */
    userSource?: (number|null);
}

/** Represents a TechwolfMessageRead. */
export class TechwolfMessageRead implements ITechwolfMessageRead {

    /**
     * Constructs a new TechwolfMessageRead.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfMessageRead);

    /** TechwolfMessageRead userId. */
    public userId: (number|Long);

    /** TechwolfMessageRead messageId. */
    public messageId: (number|Long);

    /** TechwolfMessageRead readTime. */
    public readTime: (number|Long);

    /** TechwolfMessageRead sync. */
    public sync: boolean;

    /** TechwolfMessageRead userSource. */
    public userSource: number;

    /**
     * Creates a new TechwolfMessageRead instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfMessageRead instance
     */
    public static create(properties?: ITechwolfMessageRead): TechwolfMessageRead;

    /**
     * Encodes the specified TechwolfMessageRead message. Does not implicitly {@link TechwolfMessageRead.verify|verify} messages.
     * @param message TechwolfMessageRead message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfMessageRead, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfMessageRead message, length delimited. Does not implicitly {@link TechwolfMessageRead.verify|verify} messages.
     * @param message TechwolfMessageRead message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfMessageRead, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfMessageRead message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfMessageRead
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfMessageRead;

    /**
     * Decodes a TechwolfMessageRead message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfMessageRead
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfMessageRead;

    /**
     * Verifies a TechwolfMessageRead message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfMessageRead message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfMessageRead
     */
    public static fromObject(object: { [k: string]: any }): TechwolfMessageRead;

    /**
     * Creates a plain object from a TechwolfMessageRead message. Also converts values to other types if specified.
     * @param message TechwolfMessageRead
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfMessageRead, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfMessageRead to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfMessageRead
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfChatProtocol. */
export interface ITechwolfChatProtocol {

    /** TechwolfChatProtocol type */
    type: number;

    /** TechwolfChatProtocol version */
    version?: (string|null);

    /** TechwolfChatProtocol messages */
    messages?: (ITechwolfMessage[]|null);

    /** TechwolfChatProtocol presence */
    presence?: (ITechwolfPresence|null);

    /** TechwolfChatProtocol iq */
    iq?: (ITechwolfIq|null);

    /** TechwolfChatProtocol iqResponse */
    iqResponse?: (ITechwolfIqResponse|null);

    /** TechwolfChatProtocol messageSync */
    messageSync?: (ITechwolfMessageSync[]|null);

    /** TechwolfChatProtocol messageRead */
    messageRead?: (ITechwolfMessageRead[]|null);

    /** TechwolfChatProtocol dataSync */
    dataSync?: (ITechwolfDataSync|null);

    /** TechwolfChatProtocol domain */
    domain?: (number|null);
}

/** Represents a TechwolfChatProtocol. */
export class TechwolfChatProtocol implements ITechwolfChatProtocol {

    /**
     * Constructs a new TechwolfChatProtocol.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfChatProtocol);

    /** TechwolfChatProtocol type. */
    public type: number;

    /** TechwolfChatProtocol version. */
    public version: string;

    /** TechwolfChatProtocol messages. */
    public messages: ITechwolfMessage[];

    /** TechwolfChatProtocol presence. */
    public presence?: (ITechwolfPresence|null);

    /** TechwolfChatProtocol iq. */
    public iq?: (ITechwolfIq|null);

    /** TechwolfChatProtocol iqResponse. */
    public iqResponse?: (ITechwolfIqResponse|null);

    /** TechwolfChatProtocol messageSync. */
    public messageSync: ITechwolfMessageSync[];

    /** TechwolfChatProtocol messageRead. */
    public messageRead: ITechwolfMessageRead[];

    /** TechwolfChatProtocol dataSync. */
    public dataSync?: (ITechwolfDataSync|null);

    /** TechwolfChatProtocol domain. */
    public domain: number;

    /**
     * Creates a new TechwolfChatProtocol instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfChatProtocol instance
     */
    public static create(properties?: ITechwolfChatProtocol): TechwolfChatProtocol;

    /**
     * Encodes the specified TechwolfChatProtocol message. Does not implicitly {@link TechwolfChatProtocol.verify|verify} messages.
     * @param message TechwolfChatProtocol message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfChatProtocol, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfChatProtocol message, length delimited. Does not implicitly {@link TechwolfChatProtocol.verify|verify} messages.
     * @param message TechwolfChatProtocol message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfChatProtocol, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfChatProtocol message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfChatProtocol
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfChatProtocol;

    /**
     * Decodes a TechwolfChatProtocol message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfChatProtocol
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfChatProtocol;

    /**
     * Verifies a TechwolfChatProtocol message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfChatProtocol message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfChatProtocol
     */
    public static fromObject(object: { [k: string]: any }): TechwolfChatProtocol;

    /**
     * Creates a plain object from a TechwolfChatProtocol message. Also converts values to other types if specified.
     * @param message TechwolfChatProtocol
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfChatProtocol, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfChatProtocol to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfChatProtocol
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfRedEnvelope. */
export interface ITechwolfRedEnvelope {

    /** TechwolfRedEnvelope redId */
    redId: (number|Long);

    /** TechwolfRedEnvelope redText */
    redText: string;

    /** TechwolfRedEnvelope redTitle */
    redTitle: string;

    /** TechwolfRedEnvelope clickUrl */
    clickUrl: string;
}

/** Represents a TechwolfRedEnvelope. */
export class TechwolfRedEnvelope implements ITechwolfRedEnvelope {

    /**
     * Constructs a new TechwolfRedEnvelope.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfRedEnvelope);

    /** TechwolfRedEnvelope redId. */
    public redId: (number|Long);

    /** TechwolfRedEnvelope redText. */
    public redText: string;

    /** TechwolfRedEnvelope redTitle. */
    public redTitle: string;

    /** TechwolfRedEnvelope clickUrl. */
    public clickUrl: string;

    /**
     * Creates a new TechwolfRedEnvelope instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfRedEnvelope instance
     */
    public static create(properties?: ITechwolfRedEnvelope): TechwolfRedEnvelope;

    /**
     * Encodes the specified TechwolfRedEnvelope message. Does not implicitly {@link TechwolfRedEnvelope.verify|verify} messages.
     * @param message TechwolfRedEnvelope message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfRedEnvelope, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfRedEnvelope message, length delimited. Does not implicitly {@link TechwolfRedEnvelope.verify|verify} messages.
     * @param message TechwolfRedEnvelope message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfRedEnvelope, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfRedEnvelope message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfRedEnvelope
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfRedEnvelope;

    /**
     * Decodes a TechwolfRedEnvelope message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfRedEnvelope
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfRedEnvelope;

    /**
     * Verifies a TechwolfRedEnvelope message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfRedEnvelope message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfRedEnvelope
     */
    public static fromObject(object: { [k: string]: any }): TechwolfRedEnvelope;

    /**
     * Creates a plain object from a TechwolfRedEnvelope message. Also converts values to other types if specified.
     * @param message TechwolfRedEnvelope
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfRedEnvelope, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfRedEnvelope to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfRedEnvelope
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfOrderDetail. */
export interface ITechwolfOrderDetail {

    /** TechwolfOrderDetail title */
    title: string;

    /** TechwolfOrderDetail subTitle */
    subTitle: string;

    /** TechwolfOrderDetail url */
    url?: (string|null);

    /** TechwolfOrderDetail orderDetailEntryList */
    orderDetailEntryList?: (ITechwolfOrderDetailEntry[]|null);
}

/** Represents a TechwolfOrderDetail. */
export class TechwolfOrderDetail implements ITechwolfOrderDetail {

    /**
     * Constructs a new TechwolfOrderDetail.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfOrderDetail);

    /** TechwolfOrderDetail title. */
    public title: string;

    /** TechwolfOrderDetail subTitle. */
    public subTitle: string;

    /** TechwolfOrderDetail url. */
    public url: string;

    /** TechwolfOrderDetail orderDetailEntryList. */
    public orderDetailEntryList: ITechwolfOrderDetailEntry[];

    /**
     * Creates a new TechwolfOrderDetail instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfOrderDetail instance
     */
    public static create(properties?: ITechwolfOrderDetail): TechwolfOrderDetail;

    /**
     * Encodes the specified TechwolfOrderDetail message. Does not implicitly {@link TechwolfOrderDetail.verify|verify} messages.
     * @param message TechwolfOrderDetail message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfOrderDetail, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfOrderDetail message, length delimited. Does not implicitly {@link TechwolfOrderDetail.verify|verify} messages.
     * @param message TechwolfOrderDetail message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfOrderDetail, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfOrderDetail message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfOrderDetail
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfOrderDetail;

    /**
     * Decodes a TechwolfOrderDetail message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfOrderDetail
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfOrderDetail;

    /**
     * Verifies a TechwolfOrderDetail message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfOrderDetail message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfOrderDetail
     */
    public static fromObject(object: { [k: string]: any }): TechwolfOrderDetail;

    /**
     * Creates a plain object from a TechwolfOrderDetail message. Also converts values to other types if specified.
     * @param message TechwolfOrderDetail
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfOrderDetail, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfOrderDetail to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfOrderDetail
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfOrderDetailItem. */
export interface ITechwolfOrderDetailItem {

    /** TechwolfOrderDetailItem name */
    name: string;

    /** TechwolfOrderDetailItem templateId */
    templateId: number;
}

/** Represents a TechwolfOrderDetailItem. */
export class TechwolfOrderDetailItem implements ITechwolfOrderDetailItem {

    /**
     * Constructs a new TechwolfOrderDetailItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfOrderDetailItem);

    /** TechwolfOrderDetailItem name. */
    public name: string;

    /** TechwolfOrderDetailItem templateId. */
    public templateId: number;

    /**
     * Creates a new TechwolfOrderDetailItem instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfOrderDetailItem instance
     */
    public static create(properties?: ITechwolfOrderDetailItem): TechwolfOrderDetailItem;

    /**
     * Encodes the specified TechwolfOrderDetailItem message. Does not implicitly {@link TechwolfOrderDetailItem.verify|verify} messages.
     * @param message TechwolfOrderDetailItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfOrderDetailItem, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfOrderDetailItem message, length delimited. Does not implicitly {@link TechwolfOrderDetailItem.verify|verify} messages.
     * @param message TechwolfOrderDetailItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfOrderDetailItem, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfOrderDetailItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfOrderDetailItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfOrderDetailItem;

    /**
     * Decodes a TechwolfOrderDetailItem message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfOrderDetailItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfOrderDetailItem;

    /**
     * Verifies a TechwolfOrderDetailItem message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfOrderDetailItem message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfOrderDetailItem
     */
    public static fromObject(object: { [k: string]: any }): TechwolfOrderDetailItem;

    /**
     * Creates a plain object from a TechwolfOrderDetailItem message. Also converts values to other types if specified.
     * @param message TechwolfOrderDetailItem
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfOrderDetailItem, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfOrderDetailItem to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfOrderDetailItem
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfOrderDetailEntry. */
export interface ITechwolfOrderDetailEntry {

    /** TechwolfOrderDetailEntry key */
    key: ITechwolfOrderDetailItem;

    /** TechwolfOrderDetailEntry value */
    value: ITechwolfOrderDetailItem;
}

/** Represents a TechwolfOrderDetailEntry. */
export class TechwolfOrderDetailEntry implements ITechwolfOrderDetailEntry {

    /**
     * Constructs a new TechwolfOrderDetailEntry.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfOrderDetailEntry);

    /** TechwolfOrderDetailEntry key. */
    public key: ITechwolfOrderDetailItem;

    /** TechwolfOrderDetailEntry value. */
    public value: ITechwolfOrderDetailItem;

    /**
     * Creates a new TechwolfOrderDetailEntry instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfOrderDetailEntry instance
     */
    public static create(properties?: ITechwolfOrderDetailEntry): TechwolfOrderDetailEntry;

    /**
     * Encodes the specified TechwolfOrderDetailEntry message. Does not implicitly {@link TechwolfOrderDetailEntry.verify|verify} messages.
     * @param message TechwolfOrderDetailEntry message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfOrderDetailEntry, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfOrderDetailEntry message, length delimited. Does not implicitly {@link TechwolfOrderDetailEntry.verify|verify} messages.
     * @param message TechwolfOrderDetailEntry message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfOrderDetailEntry, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfOrderDetailEntry message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfOrderDetailEntry
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfOrderDetailEntry;

    /**
     * Decodes a TechwolfOrderDetailEntry message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfOrderDetailEntry
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfOrderDetailEntry;

    /**
     * Verifies a TechwolfOrderDetailEntry message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfOrderDetailEntry message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfOrderDetailEntry
     */
    public static fromObject(object: { [k: string]: any }): TechwolfOrderDetailEntry;

    /**
     * Creates a plain object from a TechwolfOrderDetailEntry message. Also converts values to other types if specified.
     * @param message TechwolfOrderDetailEntry
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfOrderDetailEntry, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfOrderDetailEntry to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfOrderDetailEntry
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfUserSync. */
export interface ITechwolfUserSync {

    /** TechwolfUserSync uid */
    uid: (number|Long);

    /** TechwolfUserSync identity */
    identity: number;

    /** TechwolfUserSync extraJson */
    extraJson?: (string|null);

    /** TechwolfUserSync userSource */
    userSource?: (number|null);
}

/** Represents a TechwolfUserSync. */
export class TechwolfUserSync implements ITechwolfUserSync {

    /**
     * Constructs a new TechwolfUserSync.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfUserSync);

    /** TechwolfUserSync uid. */
    public uid: (number|Long);

    /** TechwolfUserSync identity. */
    public identity: number;

    /** TechwolfUserSync extraJson. */
    public extraJson: string;

    /** TechwolfUserSync userSource. */
    public userSource: number;

    /**
     * Creates a new TechwolfUserSync instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfUserSync instance
     */
    public static create(properties?: ITechwolfUserSync): TechwolfUserSync;

    /**
     * Encodes the specified TechwolfUserSync message. Does not implicitly {@link TechwolfUserSync.verify|verify} messages.
     * @param message TechwolfUserSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfUserSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfUserSync message, length delimited. Does not implicitly {@link TechwolfUserSync.verify|verify} messages.
     * @param message TechwolfUserSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfUserSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfUserSync message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfUserSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfUserSync;

    /**
     * Decodes a TechwolfUserSync message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfUserSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfUserSync;

    /**
     * Verifies a TechwolfUserSync message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfUserSync message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfUserSync
     */
    public static fromObject(object: { [k: string]: any }): TechwolfUserSync;

    /**
     * Creates a plain object from a TechwolfUserSync message. Also converts values to other types if specified.
     * @param message TechwolfUserSync
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfUserSync, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfUserSync to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfUserSync
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfDataSync. */
export interface ITechwolfDataSync {

    /** TechwolfDataSync type */
    type: number;

    /** TechwolfDataSync userSync */
    userSync?: (ITechwolfUserSync|null);

    /** TechwolfDataSync groupSync */
    groupSync?: (ITechwolfGroupSync|null);
}

/** Represents a TechwolfDataSync. */
export class TechwolfDataSync implements ITechwolfDataSync {

    /**
     * Constructs a new TechwolfDataSync.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfDataSync);

    /** TechwolfDataSync type. */
    public type: number;

    /** TechwolfDataSync userSync. */
    public userSync?: (ITechwolfUserSync|null);

    /** TechwolfDataSync groupSync. */
    public groupSync?: (ITechwolfGroupSync|null);

    /**
     * Creates a new TechwolfDataSync instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfDataSync instance
     */
    public static create(properties?: ITechwolfDataSync): TechwolfDataSync;

    /**
     * Encodes the specified TechwolfDataSync message. Does not implicitly {@link TechwolfDataSync.verify|verify} messages.
     * @param message TechwolfDataSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfDataSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfDataSync message, length delimited. Does not implicitly {@link TechwolfDataSync.verify|verify} messages.
     * @param message TechwolfDataSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfDataSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfDataSync message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfDataSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfDataSync;

    /**
     * Decodes a TechwolfDataSync message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfDataSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfDataSync;

    /**
     * Verifies a TechwolfDataSync message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfDataSync message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfDataSync
     */
    public static fromObject(object: { [k: string]: any }): TechwolfDataSync;

    /**
     * Creates a plain object from a TechwolfDataSync message. Also converts values to other types if specified.
     * @param message TechwolfDataSync
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfDataSync, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfDataSync to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfDataSync
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfSlice. */
export interface ITechwolfSlice {

    /** TechwolfSlice startIndex */
    startIndex: number;

    /** TechwolfSlice endIndex */
    endIndex: number;
}

/** Represents a TechwolfSlice. */
export class TechwolfSlice implements ITechwolfSlice {

    /**
     * Constructs a new TechwolfSlice.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfSlice);

    /** TechwolfSlice startIndex. */
    public startIndex: number;

    /** TechwolfSlice endIndex. */
    public endIndex: number;

    /**
     * Creates a new TechwolfSlice instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfSlice instance
     */
    public static create(properties?: ITechwolfSlice): TechwolfSlice;

    /**
     * Encodes the specified TechwolfSlice message. Does not implicitly {@link TechwolfSlice.verify|verify} messages.
     * @param message TechwolfSlice message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfSlice, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfSlice message, length delimited. Does not implicitly {@link TechwolfSlice.verify|verify} messages.
     * @param message TechwolfSlice message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfSlice, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfSlice message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfSlice
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfSlice;

    /**
     * Decodes a TechwolfSlice message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfSlice
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfSlice;

    /**
     * Verifies a TechwolfSlice message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfSlice message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfSlice
     */
    public static fromObject(object: { [k: string]: any }): TechwolfSlice;

    /**
     * Creates a plain object from a TechwolfSlice message. Also converts values to other types if specified.
     * @param message TechwolfSlice
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfSlice, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfSlice to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfSlice
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a UserExperience. */
export interface IUserExperience {

    /** UserExperience organization */
    organization: string;

    /** UserExperience occupation */
    occupation: string;

    /** UserExperience startDate */
    startDate?: (string|null);

    /** UserExperience endDate */
    endDate?: (string|null);

    /** UserExperience type */
    type: number;
}

/** Represents a UserExperience. */
export class UserExperience implements IUserExperience {

    /**
     * Constructs a new UserExperience.
     * @param [properties] Properties to set
     */
    constructor(properties?: IUserExperience);

    /** UserExperience organization. */
    public organization: string;

    /** UserExperience occupation. */
    public occupation: string;

    /** UserExperience startDate. */
    public startDate: string;

    /** UserExperience endDate. */
    public endDate: string;

    /** UserExperience type. */
    public type: number;

    /**
     * Creates a new UserExperience instance using the specified properties.
     * @param [properties] Properties to set
     * @returns UserExperience instance
     */
    public static create(properties?: IUserExperience): UserExperience;

    /**
     * Encodes the specified UserExperience message. Does not implicitly {@link UserExperience.verify|verify} messages.
     * @param message UserExperience message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IUserExperience, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified UserExperience message, length delimited. Does not implicitly {@link UserExperience.verify|verify} messages.
     * @param message UserExperience message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IUserExperience, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a UserExperience message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns UserExperience
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): UserExperience;

    /**
     * Decodes a UserExperience message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns UserExperience
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): UserExperience;

    /**
     * Verifies a UserExperience message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a UserExperience message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns UserExperience
     */
    public static fromObject(object: { [k: string]: any }): UserExperience;

    /**
     * Creates a plain object from a UserExperience message. Also converts values to other types if specified.
     * @param message UserExperience
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: UserExperience, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this UserExperience to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for UserExperience
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfJobShare. */
export interface ITechwolfJobShare {

    /** TechwolfJobShare user */
    user: ITechwolfUser;

    /** TechwolfJobShare jobId */
    jobId: (number|Long);

    /** TechwolfJobShare position */
    position: string;

    /** TechwolfJobShare salary */
    salary: string;

    /** TechwolfJobShare location */
    location?: (string|null);

    /** TechwolfJobShare company */
    company: string;

    /** TechwolfJobShare stage */
    stage?: (string|null);

    /** TechwolfJobShare experience */
    experience?: (string|null);

    /** TechwolfJobShare education */
    education?: (string|null);

    /** TechwolfJobShare url */
    url?: (string|null);

    /** TechwolfJobShare lid */
    lid?: (string|null);

    /** TechwolfJobShare price */
    price?: (string|null);

    /** TechwolfJobShare description */
    description?: (string|null);

    /** TechwolfJobShare bossId */
    bossId?: (number|Long|null);

    /** TechwolfJobShare extend */
    extend?: (string|null);
}

/** Represents a TechwolfJobShare. */
export class TechwolfJobShare implements ITechwolfJobShare {

    /**
     * Constructs a new TechwolfJobShare.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfJobShare);

    /** TechwolfJobShare user. */
    public user: ITechwolfUser;

    /** TechwolfJobShare jobId. */
    public jobId: (number|Long);

    /** TechwolfJobShare position. */
    public position: string;

    /** TechwolfJobShare salary. */
    public salary: string;

    /** TechwolfJobShare location. */
    public location: string;

    /** TechwolfJobShare company. */
    public company: string;

    /** TechwolfJobShare stage. */
    public stage: string;

    /** TechwolfJobShare experience. */
    public experience: string;

    /** TechwolfJobShare education. */
    public education: string;

    /** TechwolfJobShare url. */
    public url: string;

    /** TechwolfJobShare lid. */
    public lid: string;

    /** TechwolfJobShare price. */
    public price: string;

    /** TechwolfJobShare description. */
    public description: string;

    /** TechwolfJobShare bossId. */
    public bossId: (number|Long);

    /** TechwolfJobShare extend. */
    public extend: string;

    /**
     * Creates a new TechwolfJobShare instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfJobShare instance
     */
    public static create(properties?: ITechwolfJobShare): TechwolfJobShare;

    /**
     * Encodes the specified TechwolfJobShare message. Does not implicitly {@link TechwolfJobShare.verify|verify} messages.
     * @param message TechwolfJobShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfJobShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfJobShare message, length delimited. Does not implicitly {@link TechwolfJobShare.verify|verify} messages.
     * @param message TechwolfJobShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfJobShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfJobShare message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfJobShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfJobShare;

    /**
     * Decodes a TechwolfJobShare message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfJobShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfJobShare;

    /**
     * Verifies a TechwolfJobShare message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfJobShare message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfJobShare
     */
    public static fromObject(object: { [k: string]: any }): TechwolfJobShare;

    /**
     * Creates a plain object from a TechwolfJobShare message. Also converts values to other types if specified.
     * @param message TechwolfJobShare
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfJobShare, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfJobShare to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfJobShare
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfResumeShare. */
export interface ITechwolfResumeShare {

    /** TechwolfResumeShare user */
    user: ITechwolfUser;

    /** TechwolfResumeShare expectId */
    expectId: (number|Long);

    /** TechwolfResumeShare position */
    position: string;

    /** TechwolfResumeShare salary */
    salary: string;

    /** TechwolfResumeShare location */
    location?: (string|null);

    /** TechwolfResumeShare applyStatus */
    applyStatus?: (string|null);

    /** TechwolfResumeShare age */
    age?: (string|null);

    /** TechwolfResumeShare experience */
    experience?: (string|null);

    /** TechwolfResumeShare education */
    education?: (string|null);

    /** TechwolfResumeShare url */
    url?: (string|null);

    /** TechwolfResumeShare lid */
    lid?: (string|null);

    /** TechwolfResumeShare gender */
    gender?: (number|null);

    /** TechwolfResumeShare blurred */
    blurred?: (boolean|null);

    /** TechwolfResumeShare source */
    source?: (number|null);
}

/** Represents a TechwolfResumeShare. */
export class TechwolfResumeShare implements ITechwolfResumeShare {

    /**
     * Constructs a new TechwolfResumeShare.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfResumeShare);

    /** TechwolfResumeShare user. */
    public user: ITechwolfUser;

    /** TechwolfResumeShare expectId. */
    public expectId: (number|Long);

    /** TechwolfResumeShare position. */
    public position: string;

    /** TechwolfResumeShare salary. */
    public salary: string;

    /** TechwolfResumeShare location. */
    public location: string;

    /** TechwolfResumeShare applyStatus. */
    public applyStatus: string;

    /** TechwolfResumeShare age. */
    public age: string;

    /** TechwolfResumeShare experience. */
    public experience: string;

    /** TechwolfResumeShare education. */
    public education: string;

    /** TechwolfResumeShare url. */
    public url: string;

    /** TechwolfResumeShare lid. */
    public lid: string;

    /** TechwolfResumeShare gender. */
    public gender: number;

    /** TechwolfResumeShare blurred. */
    public blurred: boolean;

    /** TechwolfResumeShare source. */
    public source: number;

    /**
     * Creates a new TechwolfResumeShare instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfResumeShare instance
     */
    public static create(properties?: ITechwolfResumeShare): TechwolfResumeShare;

    /**
     * Encodes the specified TechwolfResumeShare message. Does not implicitly {@link TechwolfResumeShare.verify|verify} messages.
     * @param message TechwolfResumeShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfResumeShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfResumeShare message, length delimited. Does not implicitly {@link TechwolfResumeShare.verify|verify} messages.
     * @param message TechwolfResumeShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfResumeShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfResumeShare message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfResumeShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfResumeShare;

    /**
     * Decodes a TechwolfResumeShare message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfResumeShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfResumeShare;

    /**
     * Verifies a TechwolfResumeShare message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfResumeShare message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfResumeShare
     */
    public static fromObject(object: { [k: string]: any }): TechwolfResumeShare;

    /**
     * Creates a plain object from a TechwolfResumeShare message. Also converts values to other types if specified.
     * @param message TechwolfResumeShare
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfResumeShare, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfResumeShare to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfResumeShare
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an AtInfo. */
export interface IAtInfo {

    /** AtInfo flag */
    flag: number;

    /** AtInfo uids */
    uids?: ((number|Long)[]|null);
}

/** Represents an AtInfo. */
export class AtInfo implements IAtInfo {

    /**
     * Constructs a new AtInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: IAtInfo);

    /** AtInfo flag. */
    public flag: number;

    /** AtInfo uids. */
    public uids: (number|Long)[];

    /**
     * Creates a new AtInfo instance using the specified properties.
     * @param [properties] Properties to set
     * @returns AtInfo instance
     */
    public static create(properties?: IAtInfo): AtInfo;

    /**
     * Encodes the specified AtInfo message. Does not implicitly {@link AtInfo.verify|verify} messages.
     * @param message AtInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IAtInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified AtInfo message, length delimited. Does not implicitly {@link AtInfo.verify|verify} messages.
     * @param message AtInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IAtInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an AtInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns AtInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): AtInfo;

    /**
     * Decodes an AtInfo message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns AtInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): AtInfo;

    /**
     * Verifies an AtInfo message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an AtInfo message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns AtInfo
     */
    public static fromObject(object: { [k: string]: any }): AtInfo;

    /**
     * Creates a plain object from an AtInfo message. Also converts values to other types if specified.
     * @param message AtInfo
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: AtInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this AtInfo to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for AtInfo
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfGroupSync. */
export interface ITechwolfGroupSync {

    /** TechwolfGroupSync gid */
    gid: (number|Long);

    /** TechwolfGroupSync version */
    version?: (number|null);

    /** TechwolfGroupSync encGid */
    encGid?: (string|null);

    /** TechwolfGroupSync extraJson */
    extraJson?: (string|null);
}

/** Represents a TechwolfGroupSync. */
export class TechwolfGroupSync implements ITechwolfGroupSync {

    /**
     * Constructs a new TechwolfGroupSync.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfGroupSync);

    /** TechwolfGroupSync gid. */
    public gid: (number|Long);

    /** TechwolfGroupSync version. */
    public version: number;

    /** TechwolfGroupSync encGid. */
    public encGid: string;

    /** TechwolfGroupSync extraJson. */
    public extraJson: string;

    /**
     * Creates a new TechwolfGroupSync instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfGroupSync instance
     */
    public static create(properties?: ITechwolfGroupSync): TechwolfGroupSync;

    /**
     * Encodes the specified TechwolfGroupSync message. Does not implicitly {@link TechwolfGroupSync.verify|verify} messages.
     * @param message TechwolfGroupSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfGroupSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfGroupSync message, length delimited. Does not implicitly {@link TechwolfGroupSync.verify|verify} messages.
     * @param message TechwolfGroupSync message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfGroupSync, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfGroupSync message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfGroupSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfGroupSync;

    /**
     * Decodes a TechwolfGroupSync message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfGroupSync
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfGroupSync;

    /**
     * Verifies a TechwolfGroupSync message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfGroupSync message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfGroupSync
     */
    public static fromObject(object: { [k: string]: any }): TechwolfGroupSync;

    /**
     * Creates a plain object from a TechwolfGroupSync message. Also converts values to other types if specified.
     * @param message TechwolfGroupSync
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfGroupSync, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfGroupSync to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfGroupSync
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfSticker. */
export interface ITechwolfSticker {

    /** TechwolfSticker sid */
    sid: (number|Long);

    /** TechwolfSticker packId */
    packId?: (number|Long|null);

    /** TechwolfSticker image */
    image?: (ITechwolfImage|null);

    /** TechwolfSticker format */
    format?: (string|null);

    /** TechwolfSticker name */
    name?: (string|null);
}

/** Represents a TechwolfSticker. */
export class TechwolfSticker implements ITechwolfSticker {

    /**
     * Constructs a new TechwolfSticker.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfSticker);

    /** TechwolfSticker sid. */
    public sid: (number|Long);

    /** TechwolfSticker packId. */
    public packId: (number|Long);

    /** TechwolfSticker image. */
    public image?: (ITechwolfImage|null);

    /** TechwolfSticker format. */
    public format: string;

    /** TechwolfSticker name. */
    public name: string;

    /**
     * Creates a new TechwolfSticker instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfSticker instance
     */
    public static create(properties?: ITechwolfSticker): TechwolfSticker;

    /**
     * Encodes the specified TechwolfSticker message. Does not implicitly {@link TechwolfSticker.verify|verify} messages.
     * @param message TechwolfSticker message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfSticker, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfSticker message, length delimited. Does not implicitly {@link TechwolfSticker.verify|verify} messages.
     * @param message TechwolfSticker message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfSticker, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfSticker message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfSticker
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfSticker;

    /**
     * Decodes a TechwolfSticker message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfSticker
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfSticker;

    /**
     * Verifies a TechwolfSticker message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfSticker message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfSticker
     */
    public static fromObject(object: { [k: string]: any }): TechwolfSticker;

    /**
     * Creates a plain object from a TechwolfSticker message. Also converts values to other types if specified.
     * @param message TechwolfSticker
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfSticker, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfSticker to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfSticker
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfChatShare. */
export interface ITechwolfChatShare {

    /** TechwolfChatShare shareId */
    shareId: (number|Long);

    /** TechwolfChatShare title */
    title: string;

    /** TechwolfChatShare records */
    records?: (string[]|null);

    /** TechwolfChatShare bottomText */
    bottomText?: (string|null);

    /** TechwolfChatShare url */
    url?: (string|null);

    /** TechwolfChatShare from */
    from: ITechwolfUser;

    /** TechwolfChatShare to */
    to: ITechwolfUser;

    /** TechwolfChatShare user */
    user: ITechwolfUser;
}

/** Represents a TechwolfChatShare. */
export class TechwolfChatShare implements ITechwolfChatShare {

    /**
     * Constructs a new TechwolfChatShare.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfChatShare);

    /** TechwolfChatShare shareId. */
    public shareId: (number|Long);

    /** TechwolfChatShare title. */
    public title: string;

    /** TechwolfChatShare records. */
    public records: string[];

    /** TechwolfChatShare bottomText. */
    public bottomText: string;

    /** TechwolfChatShare url. */
    public url: string;

    /** TechwolfChatShare from. */
    public from: ITechwolfUser;

    /** TechwolfChatShare to. */
    public to: ITechwolfUser;

    /** TechwolfChatShare user. */
    public user: ITechwolfUser;

    /**
     * Creates a new TechwolfChatShare instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfChatShare instance
     */
    public static create(properties?: ITechwolfChatShare): TechwolfChatShare;

    /**
     * Encodes the specified TechwolfChatShare message. Does not implicitly {@link TechwolfChatShare.verify|verify} messages.
     * @param message TechwolfChatShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfChatShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfChatShare message, length delimited. Does not implicitly {@link TechwolfChatShare.verify|verify} messages.
     * @param message TechwolfChatShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfChatShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfChatShare message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfChatShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfChatShare;

    /**
     * Decodes a TechwolfChatShare message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfChatShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfChatShare;

    /**
     * Verifies a TechwolfChatShare message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfChatShare message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfChatShare
     */
    public static fromObject(object: { [k: string]: any }): TechwolfChatShare;

    /**
     * Creates a plain object from a TechwolfChatShare message. Also converts values to other types if specified.
     * @param message TechwolfChatShare
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfChatShare, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfChatShare to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfChatShare
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfInterviewShare. */
export interface ITechwolfInterviewShare {

    /** TechwolfInterviewShare interviewId */
    interviewId: (number|Long);

    /** TechwolfInterviewShare user */
    user: ITechwolfUser;

    /** TechwolfInterviewShare title */
    title: string;

    /** TechwolfInterviewShare bottomText */
    bottomText: string;

    /** TechwolfInterviewShare url */
    url?: (string|null);

    /** TechwolfInterviewShare interviewTime */
    interviewTime?: (string|null);

    /** TechwolfInterviewShare interviewAddress */
    interviewAddress?: (string|null);

    /** TechwolfInterviewShare jobName */
    jobName?: (string|null);
}

/** Represents a TechwolfInterviewShare. */
export class TechwolfInterviewShare implements ITechwolfInterviewShare {

    /**
     * Constructs a new TechwolfInterviewShare.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfInterviewShare);

    /** TechwolfInterviewShare interviewId. */
    public interviewId: (number|Long);

    /** TechwolfInterviewShare user. */
    public user: ITechwolfUser;

    /** TechwolfInterviewShare title. */
    public title: string;

    /** TechwolfInterviewShare bottomText. */
    public bottomText: string;

    /** TechwolfInterviewShare url. */
    public url: string;

    /** TechwolfInterviewShare interviewTime. */
    public interviewTime: string;

    /** TechwolfInterviewShare interviewAddress. */
    public interviewAddress: string;

    /** TechwolfInterviewShare jobName. */
    public jobName: string;

    /**
     * Creates a new TechwolfInterviewShare instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfInterviewShare instance
     */
    public static create(properties?: ITechwolfInterviewShare): TechwolfInterviewShare;

    /**
     * Encodes the specified TechwolfInterviewShare message. Does not implicitly {@link TechwolfInterviewShare.verify|verify} messages.
     * @param message TechwolfInterviewShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfInterviewShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfInterviewShare message, length delimited. Does not implicitly {@link TechwolfInterviewShare.verify|verify} messages.
     * @param message TechwolfInterviewShare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfInterviewShare, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfInterviewShare message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfInterviewShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfInterviewShare;

    /**
     * Decodes a TechwolfInterviewShare message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfInterviewShare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfInterviewShare;

    /**
     * Verifies a TechwolfInterviewShare message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfInterviewShare message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfInterviewShare
     */
    public static fromObject(object: { [k: string]: any }): TechwolfInterviewShare;

    /**
     * Creates a plain object from a TechwolfInterviewShare message. Also converts values to other types if specified.
     * @param message TechwolfInterviewShare
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfInterviewShare, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfInterviewShare to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfInterviewShare
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfListItem. */
export interface ITechwolfListItem {

    /** TechwolfListItem title */
    title?: (string|null);

    /** TechwolfListItem icon */
    icon?: (number|null);

    /** TechwolfListItem url */
    url?: (string|null);

    /** TechwolfListItem text */
    text?: (string|null);
}

/** Represents a TechwolfListItem. */
export class TechwolfListItem implements ITechwolfListItem {

    /**
     * Constructs a new TechwolfListItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfListItem);

    /** TechwolfListItem title. */
    public title: string;

    /** TechwolfListItem icon. */
    public icon: number;

    /** TechwolfListItem url. */
    public url: string;

    /** TechwolfListItem text. */
    public text: string;

    /**
     * Creates a new TechwolfListItem instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfListItem instance
     */
    public static create(properties?: ITechwolfListItem): TechwolfListItem;

    /**
     * Encodes the specified TechwolfListItem message. Does not implicitly {@link TechwolfListItem.verify|verify} messages.
     * @param message TechwolfListItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfListItem, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfListItem message, length delimited. Does not implicitly {@link TechwolfListItem.verify|verify} messages.
     * @param message TechwolfListItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfListItem, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfListItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfListItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfListItem;

    /**
     * Decodes a TechwolfListItem message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfListItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfListItem;

    /**
     * Verifies a TechwolfListItem message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfListItem message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfListItem
     */
    public static fromObject(object: { [k: string]: any }): TechwolfListItem;

    /**
     * Creates a plain object from a TechwolfListItem message. Also converts values to other types if specified.
     * @param message TechwolfListItem
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfListItem, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfListItem to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfListItem
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfListCard. */
export interface ITechwolfListCard {

    /** TechwolfListCard title */
    title?: (string|null);

    /** TechwolfListCard items */
    items?: (ITechwolfListItem[]|null);

    /** TechwolfListCard pageSize */
    pageSize?: (number|null);

    /** TechwolfListCard extend */
    extend?: (string|null);
}

/** Represents a TechwolfListCard. */
export class TechwolfListCard implements ITechwolfListCard {

    /**
     * Constructs a new TechwolfListCard.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfListCard);

    /** TechwolfListCard title. */
    public title: string;

    /** TechwolfListCard items. */
    public items: ITechwolfListItem[];

    /** TechwolfListCard pageSize. */
    public pageSize: number;

    /** TechwolfListCard extend. */
    public extend: string;

    /**
     * Creates a new TechwolfListCard instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfListCard instance
     */
    public static create(properties?: ITechwolfListCard): TechwolfListCard;

    /**
     * Encodes the specified TechwolfListCard message. Does not implicitly {@link TechwolfListCard.verify|verify} messages.
     * @param message TechwolfListCard message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfListCard, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfListCard message, length delimited. Does not implicitly {@link TechwolfListCard.verify|verify} messages.
     * @param message TechwolfListCard message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfListCard, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfListCard message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfListCard
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfListCard;

    /**
     * Decodes a TechwolfListCard message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfListCard
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfListCard;

    /**
     * Verifies a TechwolfListCard message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfListCard message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfListCard
     */
    public static fromObject(object: { [k: string]: any }): TechwolfListCard;

    /**
     * Creates a plain object from a TechwolfListCard message. Also converts values to other types if specified.
     * @param message TechwolfListCard
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfListCard, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfListCard to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfListCard
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfStar. */
export interface ITechwolfStar {

    /** TechwolfStar starId */
    starId: (number|Long);

    /** TechwolfStar starDesc */
    starDesc?: (string|null);

    /** TechwolfStar options */
    options?: (ITechwolfListItem[]|null);
}

/** Represents a TechwolfStar. */
export class TechwolfStar implements ITechwolfStar {

    /**
     * Constructs a new TechwolfStar.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfStar);

    /** TechwolfStar starId. */
    public starId: (number|Long);

    /** TechwolfStar starDesc. */
    public starDesc: string;

    /** TechwolfStar options. */
    public options: ITechwolfListItem[];

    /**
     * Creates a new TechwolfStar instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfStar instance
     */
    public static create(properties?: ITechwolfStar): TechwolfStar;

    /**
     * Encodes the specified TechwolfStar message. Does not implicitly {@link TechwolfStar.verify|verify} messages.
     * @param message TechwolfStar message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfStar, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfStar message, length delimited. Does not implicitly {@link TechwolfStar.verify|verify} messages.
     * @param message TechwolfStar message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfStar, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfStar message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfStar
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfStar;

    /**
     * Decodes a TechwolfStar message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfStar
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfStar;

    /**
     * Verifies a TechwolfStar message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfStar message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfStar
     */
    public static fromObject(object: { [k: string]: any }): TechwolfStar;

    /**
     * Creates a plain object from a TechwolfStar message. Also converts values to other types if specified.
     * @param message TechwolfStar
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfStar, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfStar to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfStar
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfStarRate. */
export interface ITechwolfStarRate {

    /** TechwolfStarRate title */
    title?: (string|null);

    /** TechwolfStarRate stars */
    stars?: (ITechwolfStar[]|null);

    /** TechwolfStarRate rateStatus */
    rateStatus: number;

    /** TechwolfStarRate rateStar */
    rateStar?: (ITechwolfStar|null);

    /** TechwolfStarRate submitButton */
    submitButton?: (ITechwolfButton|null);
}

/** Represents a TechwolfStarRate. */
export class TechwolfStarRate implements ITechwolfStarRate {

    /**
     * Constructs a new TechwolfStarRate.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfStarRate);

    /** TechwolfStarRate title. */
    public title: string;

    /** TechwolfStarRate stars. */
    public stars: ITechwolfStar[];

    /** TechwolfStarRate rateStatus. */
    public rateStatus: number;

    /** TechwolfStarRate rateStar. */
    public rateStar?: (ITechwolfStar|null);

    /** TechwolfStarRate submitButton. */
    public submitButton?: (ITechwolfButton|null);

    /**
     * Creates a new TechwolfStarRate instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfStarRate instance
     */
    public static create(properties?: ITechwolfStarRate): TechwolfStarRate;

    /**
     * Encodes the specified TechwolfStarRate message. Does not implicitly {@link TechwolfStarRate.verify|verify} messages.
     * @param message TechwolfStarRate message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfStarRate, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfStarRate message, length delimited. Does not implicitly {@link TechwolfStarRate.verify|verify} messages.
     * @param message TechwolfStarRate message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfStarRate, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfStarRate message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfStarRate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfStarRate;

    /**
     * Decodes a TechwolfStarRate message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfStarRate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfStarRate;

    /**
     * Verifies a TechwolfStarRate message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfStarRate message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfStarRate
     */
    public static fromObject(object: { [k: string]: any }): TechwolfStarRate;

    /**
     * Creates a plain object from a TechwolfStarRate message. Also converts values to other types if specified.
     * @param message TechwolfStarRate
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfStarRate, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfStarRate to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfStarRate
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfFrame. */
export interface ITechwolfFrame {

    /** TechwolfFrame href */
    href: string;
}

/** Represents a TechwolfFrame. */
export class TechwolfFrame implements ITechwolfFrame {

    /**
     * Constructs a new TechwolfFrame.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfFrame);

    /** TechwolfFrame href. */
    public href: string;

    /**
     * Creates a new TechwolfFrame instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfFrame instance
     */
    public static create(properties?: ITechwolfFrame): TechwolfFrame;

    /**
     * Encodes the specified TechwolfFrame message. Does not implicitly {@link TechwolfFrame.verify|verify} messages.
     * @param message TechwolfFrame message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfFrame, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfFrame message, length delimited. Does not implicitly {@link TechwolfFrame.verify|verify} messages.
     * @param message TechwolfFrame message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfFrame, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfFrame message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfFrame
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfFrame;

    /**
     * Decodes a TechwolfFrame message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfFrame
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfFrame;

    /**
     * Verifies a TechwolfFrame message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfFrame message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfFrame
     */
    public static fromObject(object: { [k: string]: any }): TechwolfFrame;

    /**
     * Creates a plain object from a TechwolfFrame message. Also converts values to other types if specified.
     * @param message TechwolfFrame
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfFrame, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfFrame to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfFrame
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfMultiImage. */
export interface ITechwolfMultiImage {

    /** TechwolfMultiImage images */
    images?: (ITechwolfImageInfo[]|null);
}

/** Represents a TechwolfMultiImage. */
export class TechwolfMultiImage implements ITechwolfMultiImage {

    /**
     * Constructs a new TechwolfMultiImage.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfMultiImage);

    /** TechwolfMultiImage images. */
    public images: ITechwolfImageInfo[];

    /**
     * Creates a new TechwolfMultiImage instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfMultiImage instance
     */
    public static create(properties?: ITechwolfMultiImage): TechwolfMultiImage;

    /**
     * Encodes the specified TechwolfMultiImage message. Does not implicitly {@link TechwolfMultiImage.verify|verify} messages.
     * @param message TechwolfMultiImage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfMultiImage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfMultiImage message, length delimited. Does not implicitly {@link TechwolfMultiImage.verify|verify} messages.
     * @param message TechwolfMultiImage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfMultiImage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfMultiImage message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfMultiImage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfMultiImage;

    /**
     * Decodes a TechwolfMultiImage message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfMultiImage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfMultiImage;

    /**
     * Verifies a TechwolfMultiImage message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfMultiImage message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfMultiImage
     */
    public static fromObject(object: { [k: string]: any }): TechwolfMultiImage;

    /**
     * Creates a plain object from a TechwolfMultiImage message. Also converts values to other types if specified.
     * @param message TechwolfMultiImage
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfMultiImage, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfMultiImage to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfMultiImage
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfComDesc. */
export interface ITechwolfComDesc {

    /** TechwolfComDesc picUrl */
    picUrl: string;

    /** TechwolfComDesc name */
    name: string;

    /** TechwolfComDesc stage */
    stage?: (string|null);

    /** TechwolfComDesc scale */
    scale?: (string|null);

    /** TechwolfComDesc industry */
    industry?: (string|null);

    /** TechwolfComDesc welfareLabels */
    welfareLabels?: (string[]|null);

    /** TechwolfComDesc introduce */
    introduce?: (string|null);

    /** TechwolfComDesc url */
    url?: (string|null);

    /** TechwolfComDesc extend */
    extend?: (string|null);
}

/** Represents a TechwolfComDesc. */
export class TechwolfComDesc implements ITechwolfComDesc {

    /**
     * Constructs a new TechwolfComDesc.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfComDesc);

    /** TechwolfComDesc picUrl. */
    public picUrl: string;

    /** TechwolfComDesc name. */
    public name: string;

    /** TechwolfComDesc stage. */
    public stage: string;

    /** TechwolfComDesc scale. */
    public scale: string;

    /** TechwolfComDesc industry. */
    public industry: string;

    /** TechwolfComDesc welfareLabels. */
    public welfareLabels: string[];

    /** TechwolfComDesc introduce. */
    public introduce: string;

    /** TechwolfComDesc url. */
    public url: string;

    /** TechwolfComDesc extend. */
    public extend: string;

    /**
     * Creates a new TechwolfComDesc instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfComDesc instance
     */
    public static create(properties?: ITechwolfComDesc): TechwolfComDesc;

    /**
     * Encodes the specified TechwolfComDesc message. Does not implicitly {@link TechwolfComDesc.verify|verify} messages.
     * @param message TechwolfComDesc message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfComDesc, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfComDesc message, length delimited. Does not implicitly {@link TechwolfComDesc.verify|verify} messages.
     * @param message TechwolfComDesc message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfComDesc, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfComDesc message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfComDesc
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfComDesc;

    /**
     * Decodes a TechwolfComDesc message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfComDesc
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfComDesc;

    /**
     * Verifies a TechwolfComDesc message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfComDesc message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfComDesc
     */
    public static fromObject(object: { [k: string]: any }): TechwolfComDesc;

    /**
     * Creates a plain object from a TechwolfComDesc message. Also converts values to other types if specified.
     * @param message TechwolfComDesc
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfComDesc, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfComDesc to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfComDesc
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a TechwolfUserCard. */
export interface ITechwolfUserCard {

    /** TechwolfUserCard name */
    name: string;

    /** TechwolfUserCard tiny */
    tiny: string;

    /** TechwolfUserCard gender */
    gender?: (number|null);

    /** TechwolfUserCard comment */
    comment?: (string|null);

    /** TechwolfUserCard description */
    description?: (string|null);

    /** TechwolfUserCard labels */
    labels?: (string[]|null);

    /** TechwolfUserCard url */
    url?: (string|null);

    /** TechwolfUserCard extend */
    extend?: (string|null);
}

/** Represents a TechwolfUserCard. */
export class TechwolfUserCard implements ITechwolfUserCard {

    /**
     * Constructs a new TechwolfUserCard.
     * @param [properties] Properties to set
     */
    constructor(properties?: ITechwolfUserCard);

    /** TechwolfUserCard name. */
    public name: string;

    /** TechwolfUserCard tiny. */
    public tiny: string;

    /** TechwolfUserCard gender. */
    public gender: number;

    /** TechwolfUserCard comment. */
    public comment: string;

    /** TechwolfUserCard description. */
    public description: string;

    /** TechwolfUserCard labels. */
    public labels: string[];

    /** TechwolfUserCard url. */
    public url: string;

    /** TechwolfUserCard extend. */
    public extend: string;

    /**
     * Creates a new TechwolfUserCard instance using the specified properties.
     * @param [properties] Properties to set
     * @returns TechwolfUserCard instance
     */
    public static create(properties?: ITechwolfUserCard): TechwolfUserCard;

    /**
     * Encodes the specified TechwolfUserCard message. Does not implicitly {@link TechwolfUserCard.verify|verify} messages.
     * @param message TechwolfUserCard message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ITechwolfUserCard, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified TechwolfUserCard message, length delimited. Does not implicitly {@link TechwolfUserCard.verify|verify} messages.
     * @param message TechwolfUserCard message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ITechwolfUserCard, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a TechwolfUserCard message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns TechwolfUserCard
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): TechwolfUserCard;

    /**
     * Decodes a TechwolfUserCard message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns TechwolfUserCard
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): TechwolfUserCard;

    /**
     * Verifies a TechwolfUserCard message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a TechwolfUserCard message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns TechwolfUserCard
     */
    public static fromObject(object: { [k: string]: any }): TechwolfUserCard;

    /**
     * Creates a plain object from a TechwolfUserCard message. Also converts values to other types if specified.
     * @param message TechwolfUserCard
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: TechwolfUserCard, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this TechwolfUserCard to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for TechwolfUserCard
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}
