import { Kern<PERSON> } from '../../Kernel';
import { ChatDataBase } from '../db/ChatDataBase';
import { MessageStatus } from '../logic/message/Constants';
import { ReadMessage } from '../logic/message/model/ReadMessage';
import { common } from '@kit.AbilityKit';
import { ZPEvents } from '@ohos/utils/Index';


export class ReadMessageHandler {
  private eventHub: common.EventHub = (getContext(this) as common.UIAbilityContext).eventHub

  async handle(readMsgs: Array<ReadMessage>) {
    let account = Kernel.getInstance().getAccount()
    let contactDao = await ChatDataBase.getInstance().getContactDao()
    let messageDao = await ChatDataBase.getInstance().getMessageDao()
    if (!account || !contactDao || !messageDao) {
      return
    }

    for (let readMsg of readMsgs) {
      if (readMsg.friendId <= 0 || readMsg.messageId <= 0) {
        continue
      }

      let contact = await contactDao.getByFriendIdAndSource(readMsg.friendId, readMsg.friendSource)
      if (!contact) {
        continue
      }

      if (readMsg.sync) { // 其他端已读了对方的消息：修改未读数 & 对方发来的消息置已读
        if (readMsg.isGroup()) {
          // TODO: 群聊消息
        } else {
          if (!contact || contact.getUnreadCount() <= 0) {
            continue
          }

          await messageDao.setAllMessageReadByFromAndTo(readMsg.friendId, readMsg.friendId, readMsg.friendSource, readMsg.messageId)

          let total = await messageDao.countEqStatusGeMid(MessageStatus.Success, readMsg.friendId, readMsg.friendId, readMsg.friendSource, readMsg.messageId)
          if (total < contact.getUnreadCount()) {
            contactDao.setUnreadCount(readMsg.friendId, readMsg.friendSource, total)
          }
        }
      } else { // 对方已读了我的消息：将我发出的所有 "<=该mid" & "送达的" 消息处理置为已读
        await messageDao.setAllMessageReadByFromAndTo(account.uid, readMsg.friendId, readMsg.friendSource, readMsg.messageId)
        this.eventHub.emit(ZPEvents.MessageSyncSuccessEvent.getName())

        if (readMsg.isGroup()) {
          // TODO: 群聊消息
        } else {

          if (contact.getLastMid() <= readMsg.messageId) {
            let contactLastMsg = await messageDao.getByCmid(contact.getLastCmid())
            if (contactLastMsg && contactLastMsg.canBeSent()) {
              await contactDao.updateLastMessageState(contact.friendId, contact.friendSource, MessageStatus.Read)
            }
          }
        }
      }
    }
  }
}