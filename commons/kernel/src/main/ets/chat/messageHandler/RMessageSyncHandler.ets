import {ChatDataBase} from '../db/ChatDataBase';
import {MessageDao} from '../db/dao/MessageDao';
import MessageRepository from '../MessageRepositoty';
import common from '@ohos.app.ability.common'
import { Logger, ZPEvents } from '@ohos/utils';
import { MessageStatus } from '../logic/message/Constants';
import { MidSyncMessage } from '../logic/message/model/MidSyncMessage';


export default class RMessageSyncHandler {
  private eventHub: common.EventHub = (getContext(this) as common.UIAbilityContext).eventHub

  public async handle(beanList: Array<MidSyncMessage>) {
    for (let bean of beanList) {
      await this.handleSyncMessage(bean)
    }

    // 发出同步消息事件，聊天模块拉取联系人 & 消息列表
    this.eventHub.emit(ZPEvents.MessageSyncSuccessEvent.getName())
  }

  private async handleSyncMessage(sync: MidSyncMessage) {
    try {
      if (sync.clientMid <= 0 || sync.serverId <= 0) {
        return
      }

      let messageDao: MessageDao|null = await ChatDataBase.getInstance().getMessageDao()
      if (!messageDao) {
        return
      }

      Logger.info(`sid: ${sync.serverId}, cid:${sync.clientMid}`);
      let message = await messageDao.getByCmid(sync.clientMid);
      if (!message) {
        return
      }

      let status = message.status
      if (status != MessageStatus.Read) {
        status = MessageStatus.Success
      }
      await messageDao.updateMidAndStatusByCmid(sync.clientMid, sync.serverId, status);

      if (!sync.isGroup()) {
        MessageRepository.getInstance().saveMaxMessageId(false, sync.serverId)
      }
    } catch (e) {
      Logger.info("error: %s", JSON.stringify(e));
    }
  }
}