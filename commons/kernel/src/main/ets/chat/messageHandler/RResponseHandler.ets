import { Logger, TextUtils } from '@ohos/utils';
import { ChatBean } from '../logic/ChatBean';
import { MqttConfig } from '../MqttConfig';
import RHistoryMessageHandler, { HistoryParam } from './RHistoryMessageHandler';

export default class RResponseHandler {
  private mHistoryMessageHandler: RHistoryMessageHandler

  constructor(historyMessageHandler: RHistoryMessageHandler) {
    this.mHistoryMessageHandler = historyMessageHandler
  }

  public handle(domain: number, responseBean: ChatBean, list: Array<ChatBean>|null, uid: number, role: number): void {
    if (responseBean.iqResponse == null || TextUtils.isEmpty(responseBean.iqResponse.query)) {
      return;
    }

    let result: Map<string, string> = responseBean.iqResponse.results;
    let query: string = responseBean.iqResponse.query;
    Logger.debug("query = %s  , result=%s", query, result);
    if (domain == 1 || domain == 0) {
      if (MqttConfig.CHAT_HISTORY_MESSAGE_PULL === query) {
        //之前发送出席， 如果没有消息， 客户端收不到任何指令， 11.23版本之后 没有离线消息，会收到该IQ消息，标注没有离线消息  1.3 实时消息  1.3_1_2 表示离线消息
        let hasMore: boolean = result.get("hasMore") == "true"
        let secretId: string = result.get("secretId") ?? ""
        let lastId: number = parseInt(result.get("lastId") ?? '0')
        if (hasMore) {
          this.mHistoryMessageHandler.handle(new HistoryParam(secretId, lastId, hasMore, uid, role));
        }
      }
    }
  }
}