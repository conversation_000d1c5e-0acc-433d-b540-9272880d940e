import { ChatDataBase } from '../db/ChatDataBase';
import { MessageDao } from '../db/dao/MessageDao';
import MessageRepository from '../MessageRepositoty';
import common from '@ohos.app.ability.common'
import { Logger, ZPEvents } from '@ohos/utils';
import { ChatMessage, ChatMessageBodyType } from '../logic/message/model/ChatMessage';
import { ContactDao } from '../db/dao/ContactDao';
import { ContactEntity } from '../db/entity/ContactEntity';
import { ContactUtils } from '../ContactUtils';


// TODO: isUpdateMid？
export class SingleChatMessageHandler {
  private eventHub: common.EventHub = (getContext(this) as common.UIAbilityContext).eventHub
  private messageDao: MessageDao|null = null
  private contactDao: ContactDao|null = null

  async handle(messageList: Array<ChatMessage<ChatMessageBodyType>>, isUpdateMid: boolean) {
    if (messageList.length == 0) {
      return;
    }

    if (!this.messageDao) {
      this.messageDao = await ChatDataBase.getInstance().getMessageDao()
    }

    if (!this.contactDao) {
      this.contactDao = await ChatDataBase.getInstance().getContactDao()
    }

    if (!this.messageDao || !this.contactDao) {
      return
    }

    let messageListToSave = await this.filterDuplicatedMessages(messageList); //过滤本地存在
    Logger.info(`SingleChatMessageHandler filterExistMessages: ${messageListToSave.length}/${messageList.length}`)
    if (messageListToSave.length == 0) {
      return;
    }

    // TODO: 开始记录同步历史消息
    // recordMsgUpdateHelper.startRecordSyncMsg();

    this.saveNewMessages(messageListToSave, isUpdateMid)

    // TODO：同步新联系人
  }

  /**
   * 过滤掉本地已存在的重复数据
   */
  private async filterDuplicatedMessages(list: Array<ChatMessage<ChatMessageBodyType>>): Promise<Array<ChatMessage<ChatMessageBodyType>>> {
    let result = new Array<ChatMessage<ChatMessageBodyType>>();
    if (!this.messageDao) {
      return list
    }

    if (list.length == 1) {
      let msg = list[0]
      let midMsgCount = await this.messageDao.countByMid(msg.mid)
      if (midMsgCount > 0) {
        let maxMsgId = await MessageRepository.getInstance().getMaxMessageId()
        Logger.info(`receive a duplicated message: ${msg.mid}, maxId: ${maxMsgId}`);
        // 兼容收到重复消息，未更新Mid
        if (maxMsgId < msg.mid) {
          MessageRepository.getInstance().saveMaxMessageId(false, msg.mid);
        }
        list.pop();
      }
      return list;
    } else {
      let localMaxMessageId = await this.messageDao.getSingleMaxMid();
      let repeatIds = new Set<number>();
      let maxId = 0;

      for (let msg of list) {
        if (maxId < msg.mid) {
          maxId = msg.mid;
        }
        if (msg.mid <= localMaxMessageId && await this.messageDao.countByMid(msg.mid) > 0) {
          repeatIds.add(msg.mid);
          continue;
        }
        result.push(msg);
      }

      if (repeatIds.size > 0) {
        let curMaxMid = await MessageRepository.getInstance().getMaxMessageId()
        Logger.info(`receive a duplicated message: ${JSON.stringify(repeatIds)} maxId: ${curMaxMid}`);
      }

      // 兼容收到重复消息，未更新Mid
      if (result.length == 0 && maxId > await MessageRepository.getInstance().getMaxMessageId()) {
        await MessageRepository.getInstance().saveMaxMessageId(false, maxId);
      }
      return result;
    }
  }

  private async saveNewMessages(list: Array<ChatMessage<ChatMessageBodyType>>, isUpdateMid: boolean) {
    if (!this.messageDao || !this.contactDao) {
      return false
    }

    let msgIds: number[] = []
    let needSaveList: Array<ChatMessage<ChatMessageBodyType>> = new Array();
    let maxId: number = 0;
    for (let bean of list) {
      if (bean.isNeedToSave()) {
        needSaveList.push(bean);
        msgIds.push(bean.mid)
        if (isUpdateMid && maxId < bean.mid) {
          maxId = bean.mid;
        }
      }
    }

    Logger.info("saved messages: " + msgIds);

    let count = 0
    for (let msg of needSaveList) {
      let rowId = await this.messageDao.save(msg.toDataBase())
      if (rowId > 0) {
        count++
      }

      // TODO: 处理各种消息

      // 更新联系人
      let contact = await this.contactDao.getByFriendIdAndSource(msg.friendId, msg.friendSource)
      if (!contact) {
        contact = new ContactEntity()
        contact.friendId = msg.friendId
        contact.friendSource = msg.friendSource
      }
      ContactUtils.updateContactByMessage(contact, msg)
      this.contactDao.upsert(contact)
    }

    if (isUpdateMid && count > 0) {
      MessageRepository.getInstance().saveMaxMessageId(false, maxId);

      // 发出同步消息事件，聊天模块拉取联系人 & 消息列表
      this.eventHub.emit(ZPEvents.MessageSyncSuccessEvent.getName())
    }

    return true;
  }
}