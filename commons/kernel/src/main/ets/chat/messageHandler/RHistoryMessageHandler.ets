import util from '@ohos.util';
import { GET, URLConfig } from '@ohos/http';
import { Logger } from '@ohos/utils';
import { HistoryPullResponse } from '@ohos/zp_api';
import { <PERSON>el } from '../../Kernel';
import { MessageFactory } from '../MessageFactory';
import { MqttConfig } from '../MqttConfig';
import ReceiverHandler from '../ReceiverHandler';

export default class RHistoryMessageHandler {
  private receiverHandler: ReceiverHandler

  constructor(receiverHandler: ReceiverHandler) {
    this.receiverHandler = receiverHandler
  }

  public handle(historyParam: HistoryParam) {
    Logger.info("historyParam = %s", historyParam);
    if (!historyParam.hasMore) {
      return;
    }

    this.syncHistoryMessages(historyParam)
  }

  private syncHistoryMessages(historyParam: HistoryParam) {
    Logger.info("pull message historyParam = %s retryCount = %d", historyParam);
    let account = Kernel.getInstance().getAccount()
    if (!account) {
      return
    }
    let uid = account.uid
    let role = account.identity
    if (role != historyParam.role || uid != historyParam.uid) {
      Logger.error("historyParam = uid = %d role = %d", uid, role);
      return;
    }

    GET<HistoryPullResponse>(
      URLConfig.URL_ZPMSG_HISTORY_PULL, {
      lastId: historyParam.lastId,
      secretId: historyParam.securityId
    }).then((resp) => {
      if (!resp.zpData) {
        return
      }

      if (resp.zpData.stringList) {
        let base64Helper = new util.Base64Helper()
        for (let message of resp.zpData.stringList) {
          // TODO: 避免身份切换导致的数据不一致
          let bytes: Uint8Array = base64Helper.decodeSync(message)
          let protocol = MessageFactory.toChatProtocol(bytes)
          if (protocol.type == MqttConfig.CHAT_TYPE_MESSAGE) {
            Logger.info("syncHistoryMessages msgCount = %d time = %d", protocol.messages.length);
            this.receiverHandler.handleMessage(protocol.messages, protocol.domain)
          } else {
            Logger.error("pull message not support type= %d", protocol.type)
          }
        }
      }

      historyParam.hasMore = resp.zpData.hasMore
      if (resp.zpData.hasMore) {
        historyParam.lastId = resp.zpData.lastId
        historyParam.securityId = resp.zpData.secretId
        this.syncHistoryMessages(historyParam)
      } else {
        Logger.info("stop pull message historyParam = %s", historyParam)
      }
    })
  }
}

export class HistoryParam {
  securityId: string
  hasMore: boolean
  uid: number
  role: number
  lastId: number

  constructor(securityId: string, lastId: number, hasMore: boolean, uid: number, role: number) {
    this.securityId = securityId;
    this.lastId = lastId;
    this.hasMore = hasMore;
    this.uid = uid;
    this.role = role;
  }
}