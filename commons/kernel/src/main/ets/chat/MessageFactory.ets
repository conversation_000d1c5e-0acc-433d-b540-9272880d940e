import chat from './pb/chatprotocol'
import { MqttConfig } from './MqttConfig';
import { Logger } from '@ohos/utils';
import { TextMessage } from './logic/message/model/TextMessage';
import { ChatMessage } from './logic/message/model/ChatMessage';
import { getDefHttpProvider } from '@ohos/http';
import deviceInfo from '@ohos.deviceInfo';
import { MessageMediaType } from './logic/message/Constants';
import { ProtobufMessage } from './logic/message/model/ProtobufMessage';


export class MessageFactory {
  static createClientInfo(): chat.TechwolfClientInfo {
    const defHttpProvider = getDefHttpProvider()

    let clientInfo = chat.TechwolfClientInfo.create({
      version: "11.23",//defHttpProvider.getVersion(), 11.24开始使用http拉取了
      system: "Harmony",
      systemVersion: deviceInfo.sdkApiVersion + "",
      model: defHttpProvider.getDeviceType(),
      uniqid: defHttpProvider.getUniqId(),
      network:"WIFI",
      appid: Number.parseInt(defHttpProvider.getAppId()),
      platform:"Harmony",
      channel:"0",
      ssid: "BZL-INC",
      bssid:"1c:28:af:5c:41:f2",
      longitude: 116.44594,
      latitude: 39.970242
    });

    return clientInfo;
  }

  private static pbToArrayBuffer(chatProtocol: chat.ITechwolfChatProtocol) {
    let chatBuffer: Uint8Array = chat.TechwolfChatProtocol.encode(chatProtocol).finish();
    let chatBuffer2:Uint8Array = new Uint8Array(chatBuffer.length)
    chatBuffer.forEach((value: number, index: number, array: Uint8Array) => {
      chatBuffer2[index] = value
    })
    // Logger.error("PUBLISH message = " + str + "; byteLength=" + chatBuffer.byteLength)
    //
    // let pb = chat.TechwolfChatProtocol.decode(chatBuffer)
    // Logger.error("PUBLISH message = " + JSON.stringify(pb))
    // let chatBuffer2: Uint8Array = chat.TechwolfChatProtocol.encode(pb).finish();
    // let str2 = "["
    // chatBuffer2.forEach((value: number, index: number, array: Uint8Array) => {
    //   str2 += value + ", "
    // })
    // Logger.error("PUBLISH message = " + str2 + "; byteLength=" + chatBuffer2.byteLength)
    // Logger.error("PUBLISH byteLength=" + MessageFactory.areUint8ArraysEqual(chatBuffer, chatBuffer2))

    let arrayBuffer = chatBuffer2.buffer.slice(0, chatBuffer2.byteLength)
    // Logger.error("PUBLISH arrayBuffer=" + arrayBuffer.byteLength)
    MessageFactory.printArrayBuffer(arrayBuffer)
    MessageFactory.printArrayBuffer(chatBuffer.buffer.slice(0, chatBuffer.byteLength))

    return arrayBuffer;
  }

  public static toChatProtocol(data: Uint8Array): chat.TechwolfChatProtocol {
    return chat.TechwolfChatProtocol.decode(data)
  }

  public static areUint8ArraysEqual(arr1:Uint8Array, arr2:Uint8Array): boolean {
    if (arr1.length !== arr2.length) {
      return false; // 如果长度不同，直接返回不一致
    }

    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) {
        return false; // 如果有任何元素不同，返回不一致
      }
    }

    return true; // 所有元素都相同，返回一致
  }

  public static printArrayBuffer(array:ArrayBuffer):void {
    const view = new Uint8Array(array)
    Logger.error("PUBLISH message = " + Array.from(view))
  }

  public static createChatMessage(type: number): ChatMessage<Object|null>|null {
    switch (type) {
      case MessageMediaType.Text:
        return new TextMessage()
      default:
        return null
    }
  }

  public static protobufMessageArrayBuffer(msg: ProtobufMessage<Object>): ArrayBuffer |null {
    let message = msg.toProtobuf()
    if (!message) {
      return null
    }

    let chatProtocol = chat.TechwolfChatProtocol.create({
      type: msg.getMessageType(),
      version: MqttConfig.PROTOCOL_VERSION,
      domain: 0
    })

    switch (msg.getMessageType()) {
      case MqttConfig.CHAT_TYPE_PRESENCE:
        chatProtocol.presence = message as chat.ITechwolfPresence
        break
      case MqttConfig.CHAT_TYPE_MESSAGE:
        chatProtocol.messages = [message as chat.ITechwolfMessage]
        break
      case MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ:
        chatProtocol.messageRead = [message as chat.ITechwolfMessageRead]
        break
    }

    Logger.error("PUBLISH message = " + JSON.stringify(chatProtocol))
    return MessageFactory.pbToArrayBuffer(chatProtocol);
  }
}