import MessageRepository from './MessageRepositoty'


export class ChatBeanFactory {
  // 记录上次获取的 cmid 为了解决 同时发送时 cmid 一致问题
  private static lastClientMessageId = Date.now()

  public static getClientMessageId(): number {
    let clientMessageId = Date.now()
    if (clientMessageId <= ChatBeanFactory.lastClientMessageId) {
        return ++ChatBeanFactory.lastClientMessageId;
    }
    return ChatBeanFactory.lastClientMessageId = clientMessageId;
  }

  public static async getSortMessageId(): Promise<number> {
    return await MessageRepository.getInstance().getMaxMessageId() + 1;
  }
}