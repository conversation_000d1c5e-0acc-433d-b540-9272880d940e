import chat from './pb/chatprotocol'
import { MqttConfig } from './MqttConfig'
import { ChatBean } from './logic/ChatBean'
import { ChatMessageBean } from './logic/protobuf/ChatMessageBean'
import { ChatMessageBodyBean } from './logic/protobuf/ChatMessageBodyBean'
import ChatUserBean from './logic/protobuf/ChatUserBean'
import ChatIQResponseBean from './logic/protobuf/ChatIQResponseBean'

export default class MessageDecoder {
  static toMessageList(protocol: chat.TechwolfChatProtocol): Array<ChatBean>|null {
    if (!protocol || protocol.type != MqttConfig.CHAT_TYPE_MESSAGE) {
      return null
    }

    let list = new Array<ChatBean>()
    for (let message of protocol.messages) {
      let bean = new ChatBean()
      bean.msgType = MqttConfig.CHAT_TYPE_MESSAGE;
      bean.version = protocol.version;
      bean.message = MessageDecoder.toChatMessageBean(message);
      bean.messageSendTime = 0;
      bean.status = 1;
      if (bean.message == null) {
        continue;
      }
      bean.msgId = bean.message.id;
      bean.sortMsgId = bean.message.id;
      bean.time = bean.message.time;
      bean.clientTempMessageId = bean.message.clientTempMessageId;
      bean.domain = protocol.domain;

      list.push(bean);
    }

    return list
  }

  /**
   * 将 chat.TechwolfMessage 转换为 ChatMessageBean
   *
   * @param protocol
   * @return
   */
  private static toChatMessageBean(protocol: chat.ITechwolfMessage): ChatMessageBean|null {
    if (protocol == null) {
      return null
    }

    let bean: ChatMessageBean = new ChatMessageBean()
    bean.id = protocol.mid
    bean.fromUser = MessageDecoder.toChatUserBean(protocol.from)
    bean.toUser = MessageDecoder.toChatUserBean(protocol.to)
    bean.type = protocol.type
    bean.time = protocol.time
    bean.messageBody = MessageDecoder.toChatMessageBodyBean(protocol.body)
    bean.encryptedBody = protocol.encryptedBody
    bean.pushText = protocol.pushText ?? ''
    bean.soundUri = protocol.pushSound == 2 ? "android.resource://com.hpbr.bosszhipin/raw/gold_sound" : null
    bean.taskId = protocol.taskId
    bean.securityId = protocol.securityId ?? ''
    bean.isOffline = protocol.offline == undefined ? null : protocol.offline
    bean.clientTempMessageId = protocol.cmid
    bean.status = protocol.status ?? -1
    bean.unCount = protocol.uncount ?? -1
    bean.flag = protocol.flag ?? -1
    bean.bizId = protocol.bizId ?? ''
    bean.bizType = protocol.bizType ?? -1
    bean.quoteId = protocol.quoteId
    return bean
  }

  /**
   * 将 ChatProtocol.TechwolfUser 转换为 ChatUserBean
   *
   * @param protocol
   * @return
   */
  private static toChatUserBean(protocol: chat.ITechwolfUser): ChatUserBean|null {
    if (protocol == null) {
      return null;
    }
    let bean: ChatUserBean = new ChatUserBean();
    bean.id = protocol.uid
    //和服务器沟通，系统id之前是1000，后来修改成899，很早很早之前的逻辑了
    if (bean.id == 1000) {
      bean.id = MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID;
    }
    bean.name = protocol.name ?? ''
    bean.avatar = protocol.avatar ?? ''
    bean.friendSource = protocol.source ?? -1
    //        bean.company = protocol.getCompany();
    //        bean.headDefaultImageIndex = protocol.getHeadImg();
    return bean;
  }

  /**
   * 将 ChatProtocol.TechwolfMessageBody 转换为 ChatMessageBodyBean
   *
   * @param protocol
   * @return
   */
  public static toChatMessageBodyBean(protocol: chat.ITechwolfMessageBody): ChatMessageBodyBean|null {
    if (protocol == null) {
      return null
    }

    let bean = new ChatMessageBodyBean()
    bean.type = protocol.type
    bean.templateId = protocol.templateId
    bean.title = protocol.headTitle ?? ''
    bean.style = protocol.style ?? -1
    switch (bean.type) {
      case 1:
        bean.text = protocol.text ?? ''
        bean.extend = protocol.extend ?? ''
        // bean.atBean = changeAtBean(protocol.getAtInfo());
        break;
    }
    return bean
  }

  /**
   * 将ChatProtocol解码为ChatBean
   *
   * @param protocol
   * @return
   */
  public static toIqResponse(protocol: chat.TechwolfChatProtocol, chatBean: ChatBean): Array<ChatBean>|null {
    if (protocol == null || protocol.type != MqttConfig.CHAT_TYPE_IQ_RESPONSE || chatBean == null || !protocol.iqResponse) {
      return null
    }

    chatBean.msgType = MqttConfig.CHAT_TYPE_IQ_RESPONSE;
    chatBean.version = protocol.version
    chatBean.iqResponse = MessageDecoder.toChatIQResponseBean(protocol.iqResponse);
    if (chatBean.iqResponse == null) {
      return null;
    }

    if (chatBean.iqResponse.results == null || chatBean.iqResponse.results.size <= 0) {
      return null
    }

    let chatProtocol: chat.TechwolfChatProtocol = chat.TechwolfChatProtocol.create();
    chatProtocol.type = MqttConfig.CHAT_TYPE_MESSAGE
    chatProtocol.version = protocol.version
    for (let message of protocol.messages) {
      chatProtocol.messages.push(message)
    }
    return MessageDecoder.toMessageList(chatProtocol)
  }

  /**
   * 将 ChatProtocol.TechwolfIqResponse 转换为 ChatIQResponseBean
   *
   * @param protocol
   * @return
   */
  private static toChatIQResponseBean(protocol: chat.ITechwolfIqResponse): ChatIQResponseBean|null {
    if (protocol == null) return null

    let bean = new ChatIQResponseBean()
    bean.id = protocol.qid
    bean.query = protocol.query
    let results: chat.ITechwolfKVEntry[]  = protocol.results ?? []
    if (results != null && results.length > 0) {
        for (let kvEntry of results) {
            bean.results.set(kvEntry.key, kvEntry.value);
        }
    }
    return bean
  }
}