const JAVA_LONG_MAX_VALUE: bigint = BigInt("0x7fffffffffffffff")

export class MqttConfig {
 /*
 * 聊天协议版本
 */
 public static readonly PROTOCOL_VERSION: string = "1.3"

 /**
  * 聊天类型：消息
  */
 public static readonly CHAT_TYPE_MESSAGE: number = 1
 /**
  * 聊天类型：状态
  */
 public static readonly CHAT_TYPE_PRESENCE: number = 2
 /**
  * 聊天类型：同步
  */
 public static readonly CHAT_TYPE_IQ: number = 3
 /**
  * 聊天类型：同步结果 获取历史聊天记录 or 被推荐人的聊天记录
  */
 public static readonly CHAT_TYPE_IQ_RESPONSE: number = 4
 /**
  * 聊天类型：发送消息id反馈,（本地发送message的server id更新）
  */
 public static readonly CHAT_TYPE_SEND_MESSAGE_ID_RESPONSE: number = 5
 /**
  * 聊天类型：消息已读
  */
 public static readonly CHAT_TYPE_MESSAGE_HAS_READ: number = 6
 /**
  * 聊天类型：数据同步（更新个人详情等）
  */
 public static readonly CHAT_TYPE_DATA_SYNC: number = 7


 /**
  * 同步消息
  */
 public static readonly CHAT_TYPE_PRESENCE_PULL: number = 0x0100

 /**
  * 聊天服务状态，切换前台状态
  */
 public static readonly CHAT_TYPE_PRESENCE_ONLINE: number = 0x0200

 /**
  * 聊天服务状态，切换后台状态
  */
 public static readonly CHAT_TYPE_PRESENCE_OFFLINE: number = 0x0400


 /**
  * 同步最新聊天记录（出席消息触发的消息同步）
  */
 public static readonly CHAT_SYNC_RESPONSE: string = "/message/syncAll"
 /**
  * 获取历史聊天记录
  */
 public static readonly CHAT_HISTORY_RESPONSE: string = "/message/history"
 /**
  * NLP建议
  */
 public static readonly CHAT_MESSAGE_SUGGEST: string = "/message/suggest"

 public static readonly CHAT_MESSAGE_AUTH_STATUS: string = "/authStatus/sync"

 public static readonly CHAT_MESSAGE_CHAT_PROPOSAL: string = "/chat/proposal"


 /**
  * 获取推荐牛人的历史聊天记录
  */
 public static readonly CHAT_RECOMMEND_HISTORY_RESPONSE: string = "/message/share"

 public static readonly CHAT_HISTORY_MESSAGE_PULL: string = "/message/pull"


 ///////////////////////////////////////////////////////////////////////////
 // 一下系统下发的消息id
 ///////////////////////////////////////////////////////////////////////////

 /**
  * 系统微JDUserId
  * 不在f2显示
  */
 public static readonly CHAT_SYSTEM_SMALL_USER_ID: number = 994
 /**
  * 系统周报UserId
  * 不在f2显示
  */
 public static readonly CHAT_SYSTEM_WEEKLY_USER_ID: number = 993
 /**
  * 系统消息UserId
  * 通知 f2显示
  */
 public static readonly CHAT_SYSTEM_MESSAGE_USER_ID: number = 899

 /**
  * 道具通知用户Id
  * 通知 f2显示
  */
 public static readonly CHAT_ITEM_NOTIFY_USER_ID: number = 797

 /**
  * 同事推荐
  * 通知 f2显示
  */
 public static readonly CHAT_MATE_RECOMMEND: number = 793
 /**
  * 猎头服务
  * 通知 f2显示
  */
 public static readonly CHAT_HUNTER_SERVICE: number = 991
 /**
  * 职位通知
  * 通知 f2显示
  */
 public static readonly CHAT_POSITION_NOTICE: number = 794
 /**
  * VIP月报
  * 通知 f2显示
  */
 public static readonly CHAT_VIP_MONTH_REPORT: number = 792
 /**
  * 财务通知
  * 通知 f2显示
  */
 public static readonly CHAT_FINANCE_NOTICE: number = 795
 /**
  * 活动通知用户Id
  * 通知 f2显示
  */
 public static readonly CHAT_ITEM_ACTIVITY_USER_ID: number = 798

 /**
  * 泰坦星
  */
 public static readonly CHAT_TAI_TAN_STAR_ID: number = 888

 /**
  * Boss优聘UserId
  * 不在f2显示
  */
 public static readonly CHAT_YOU_PIN_USER_ID: number = 898

 /**
  * 公告消息UserId，最小值
  * 不在f2显示
  */
 public static readonly CHAT_ANNOUNCE_MIN: number = 700

 /**
  * 公告消息UserId，最大值
  * 不在f2显示
  */
 public static readonly CHAT_ANNOUNCE_MAX: number = 800

 /**
  * GPT 用户id
  */
 public static readonly CHAT_GPT_ID: number = 878

 /**
  * 蓝领求职群 管理员
  */
 public static readonly GROUP_ADMIN_RECRUIT: number = 883
 /**
  * 猎头服务
  */
 public static readonly HUNTER_SERVICE_IF: number = 885


 /**
  * get功能的虚拟好友id
  * 7.17版本 get的好友id由于 1400401 变更为 896
  * f2 显示
  */
 public static readonly CHAT_GET_CIRCLE1: number = 896


 /**
  * BOSS安全官
  */
 public static readonly CHAT_SECURITY_OFFICER: number = 887

 /**
  * 谈笑间 902
  * f2 显示
  */
 public static readonly CHAT_GET_TALK_JOY: number = 889

 /**
  * 圈子
  * f2 显示
  */
 public static readonly CHAT_FROM_CIRCLE: number = 897

 /**
  * 805版本 双叶草 891  GET 直联
  * f2 显示
  */
 public static readonly CHAT_GET_DIRECT: number = 891

 /**
  * 简单完善牛人头部提醒+召回促留存
  */
 public static readonly CHAT_COMPLETE_GEEK_INFO: number = 884


 /**
  * 客服Id包括 机器人和客服
  * f2 显示
  */
 public static readonly CHAT_CUSTOMER_ID: number = 1400400
 /**
  * get功能的虚拟好友id
  * f2 显示
  */
 public static readonly CHAT_GET_CIRCLE: number = 1400401

 /**
  * 直播招聘的id
  * f2 显示
  */
 public static readonly CHAT_LIVING_RECRUIT: number = 890

 /**
  * 【小直同学】的id
  * f2 显示
  */
 public static readonly CHAT_USER_COMMUNICATIONS_OFFICER: number = 879

 /**
  * 直课堂-课程提醒（1014.901）
  */
 public static readonly SYSTEM_TITAN_TAB1_USER_ID: number = 882

 /**
  * 直课堂-平台通知（1014.901）
  */
 public static readonly SYSTEM_TITAN_TAB2_USER_ID: number = 881

 /**
  * 直课堂-活动提醒（1014.901）
  */
 public static readonly SYSTEM_TITAN_TAB3_USER_ID: number = 880
 /**
  * 短视频招人消息助手（1125.922）
  */
 public static readonly SYSTEM_SHORT_VIDEO_USER_ID: number = 876



 /**
  * https://zhishu.zhipin.com/wiki/6ESSnaeQ650
  * 我的意向沟通订单
  */
 public static readonly SYSTEM_INTENT_CONTACT_ORDER: number = 874

 /**
  *  意向沟通付费专属列表有更新
  */
 public static readonly SYSTEM_INTENT_LIST_UPDATE: number = 873


 ///////////////////////////////////////////////////////////////////////////
 // 以上是系统下发的消息联系人id
 ///////////////////////////////////////////////////////////////////////////


 ///////////////////////////////////////////////////////////////////////////
 // 以下是客户端虚拟的联系人id
 ///////////////////////////////////////////////////////////////////////////

 /**
  * F2通知的friendId,用于和聊天消息做区分
  */
 public static readonly CHAT_NOTIFY_ID: bigint = JAVA_LONG_MAX_VALUE - BigInt(1)
 /**
  * 对我感兴趣好友id
  */
 public static readonly CHAT_INTERESTED_TO_ME: bigint = JAVA_LONG_MAX_VALUE - BigInt(2)
 /**
  * 查看过我的联系人
  */
 public static readonly CHAT_VIEW_ME: bigint = JAVA_LONG_MAX_VALUE - BigInt(3)
 /**
  * 不合适
  */
 public static readonly CHAT_UNFIT: bigint = JAVA_LONG_MAX_VALUE - BigInt(4)
 /**
  * 新职位或者新牛人
  */
 public static readonly CHAT_NEWER: bigint = JAVA_LONG_MAX_VALUE - BigInt(5)
 /**
  * Boss批量沟通牛人
  */
 public static readonly CHAT_BATCH_COMMUNICATION: bigint = JAVA_LONG_MAX_VALUE - BigInt(6)

 /**
  * 我方Vip筛选不符合要求牛人或者Boss
  */
 public static readonly CHAT_INFORMITY_GEEK: bigint = JAVA_LONG_MAX_VALUE - BigInt(8)

 /**
  * Boss收藏带沟通的牛人
  */
 public static readonly CHAT_BOSS_FAVOURITE_GEEK: bigint = JAVA_LONG_MAX_VALUE - BigInt(11)

 /**
  * 高端技术牛人
  */
 public static readonly CHAT_TECHNOLOGY_GEEK: bigint = JAVA_LONG_MAX_VALUE - BigInt(12)

 /**
  * 精选牛人
  */
 public static readonly CHAT_REFINED_GEEK: bigint = JAVA_LONG_MAX_VALUE - BigInt(13)

 /**
  * 牛人身份-为您推荐
  */
 public static readonly CHAT_RECOMMAND: bigint = JAVA_LONG_MAX_VALUE - BigInt(15)

 /**
  * 消息过滤设置
  */
 public static readonly CHAT_POSITION_REQUEST_SETTING: bigint = JAVA_LONG_MAX_VALUE - BigInt(17)

 /**
  * 新职位-猜你想看
  */
 public static readonly CHAT_GUESS_LOOK: bigint = JAVA_LONG_MAX_VALUE - BigInt(18)

 /**
  * 本地职位
  */
 public static readonly CHAT_LOCAL_JOB: bigint = JAVA_LONG_MAX_VALUE - BigInt(19)


 public static readonly CHAT_811_VIP_HIGH_GEEK: bigint = JAVA_LONG_MAX_VALUE - BigInt(20)

 //批量追聊
 public static readonly CHAT_CATCH_BACK_CHAT: bigint = JAVA_LONG_MAX_VALUE - BigInt(21)

 /**
  * GEEK不感兴趣
  */
 public static readonly CHAT_GEEK_UNINTEREST: bigint = JAVA_LONG_MAX_VALUE - BigInt(23)

 /**
  * 我方引力波ID
  */
 public static readonly CHAT_OUR_PART_GRAVITATION_WAVE: bigint = JAVA_LONG_MAX_VALUE - BigInt(7)

 /**
  * 有个社区圈子功能
  */
 public static readonly CHAT_OUR_PART_GRAVITATION_WAVE2: bigint = JAVA_LONG_MAX_VALUE - BigInt(22)


 /**
  * 直播运营
  */
 public static readonly CHAT_OUR_PART_GRAVITATION_WAVE3: bigint = JAVA_LONG_MAX_VALUE - BigInt(24)

 /**
  * 直猎邦官方
  * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=157185503
  */
 public static readonly CHAT_OUR_PART_ZHI_LIE_BANG: bigint = JAVA_LONG_MAX_VALUE - BigInt(25)

 /**
  * 学习服务
  * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=175715264
  */
 public static readonly CHAT_STUDY_SERVICE: bigint = JAVA_LONG_MAX_VALUE - BigInt(26)


 public static readonly CHAT_BOSS_NEW_DRAWER: bigint = JAVA_LONG_MAX_VALUE - BigInt(27)


 /*「悟空消息」盒子*/
 public static readonly CHAT_OUT_SOURCE: bigint = JAVA_LONG_MAX_VALUE - BigInt(28)

 /*https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=208195352*/
 public static readonly CHAT_GEEK_HUNTER: bigint = JAVA_LONG_MAX_VALUE - BigInt(29)

 public static readonly CHAT_OUR_PART_ZHI_LIE_BANG_LIGO: bigint = JAVA_LONG_MAX_VALUE - BigInt(30)

 /**
  * 1118.251【商业】消息页展示邀约牛人
  */
 public static readonly CHAT_INVITE_GEEK: bigint = JAVA_LONG_MAX_VALUE - BigInt(31)


 ///////////////////////////////////////////////////////////////////////////
 // 以上是客户端虚拟的联系人id
 ///////////////////////////////////////////////////////////////////////////


 /**
  * MQTT主题
  */
 public static readonly TOPIC_CHAT: string = "chat"
}

export enum ReceivedMessageStatus {
  Unread,
  Received,
  Read
}
