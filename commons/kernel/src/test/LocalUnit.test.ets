import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';
import { AllMessageHasReadMessage } from '../../Index';


export default function localUnitTest() {
  describe('localUnitTest',() => {
    // Defines a test suite. Two parameters are supported: test suite name and test suite function.
    beforeAll(() => {
      // Presets an action, which is performed only once before all test cases of the test suite start.
      // This API supports only one parameter: preset action function.
    });
    beforeEach(() => {
      // Presets an action, which is performed before each unit test case starts.
      // The number of execution times is the same as the number of test cases defined by **it**.
      // This API supports only one parameter: preset action function.
    });
    afterEach(() => {
      // Presets a clear action, which is performed after each unit test case ends.
      // The number of execution times is the same as the number of test cases defined by **it**.
      // This API supports only one parameter: clear action function.
    });
    afterAll(() => {
      // Presets a clear action, which is performed after all test cases of the test suite end.
      // This API supports only one parameter: clear action function.
    });
    it('assertDateUtils', 0, () => {
      let msg = new AllMessageHasReadMessage(100, 200, Date.now(), false, 1)
      let pbMsg = msg.toProtobuf()
      console.log(pbMsg.userId)
      console.log(pbMsg.messageId)
      console.log(pbMsg.readTime)
      console.log('' + pbMsg.sync)
      console.log('' + pbMsg.userSource)
    });
  });
}