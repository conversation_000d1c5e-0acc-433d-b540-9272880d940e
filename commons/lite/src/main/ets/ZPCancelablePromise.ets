import { Logger } from '@ohos/utils';

const TAG: string = '[ZPCancelablePromise]';

export class ZPCancelablePromise {
  key: string

  constructor(key: string) {
    this.key = key
  }

  promise?: Promise<void>
  reject?: () => void
  isCanceled: boolean = false

  run<T>(run: () => Promise<T>): Promise<T> {
    return new Promise(async (resolve, reject) => {

      const result = await run()

      this.reject = reject
      if (this.isCanceled) {
        reject(result)
        Logger.info(TAG, "reject")
        return
      }
      resolve(result)
      Logger.info(TAG, "resolve")
    });
  }

  cancel() {
    this.isCanceled = true;
    this.reject?.();
    Logger.info(TAG, "cancel")
  }

  checkKey(key: string) : boolean {
    Logger.info(TAG, "check key this.key" + this.key + ":" + key)
    return this.key == key;
  }
}