import { Logger } from '@ohos/utils'
import common from '@ohos.app.ability.common';
import router from '@ohos.router';

const TAG = '[ZPLite]'

type onZPEvent<T> = (event: T) => void;

export class ZPLite {
  private listeners: Map<keyof object, onZPEvent<keyof object>> = new Map<keyof object, onZPEvent<keyof object>>();
  private notifyListeners?: onZPEvent<keyof object> = undefined

  public sendEvent<E>(event: E) {
    Logger.info(TAG, "sendEvent " + event)

    const key = event as keyof object

    const hasKey = this.listeners.has(key)

    if (hasKey) {
      const func = this.listeners.get(key) as onZPEvent<E>
      func(event)
      Logger.info(TAG, "notify event = " + event)
    } else {
      Logger.info(TAG, "keep notify event. event = " + event)
    }

    if (this.notifyListeners != undefined) {
      this.notifyListeners(event as keyof object)
      Logger.info(TAG, "notify all event = " + event)
    }
  }

  public setOnEvent(event: onZPEvent<keyof object>) {
    this.notifyListeners = event;
    Logger.info(TAG, "set all Event")
  }

  public registerEvent<T>(key: T, event: onZPEvent<T>) {
    this.listeners.set(key as keyof object, event);
    Logger.info(TAG, "registerEvent key  " + key)
  }

  public unRegisterEvent() {
    const size = this.listeners.size
    this.listeners.clear();
    this.notifyListeners = undefined;
    Logger.info(TAG, "unRegisterEvent clear size " + size)
    this.onDisappear()
  }

  protected onDisappear(){
    Logger.info(TAG, "onDisappear")
  }

  delay(ms: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, ms);
    });
  }

  protected  go(url: string) {
    const bundleName = (getContext(this) as common.UIAbilityContext).applicationInfo.name;
    router.pushUrl({
      url: `@bundle:${bundleName}/` + url
    });
  }
}
