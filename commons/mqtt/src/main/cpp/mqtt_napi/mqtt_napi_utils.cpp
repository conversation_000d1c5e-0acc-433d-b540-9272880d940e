/*
 * Copyright (c) 2022 Huawei Device Co., Ltd.
 * Licensed under the  Eclipse Public License -v 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.eclipse.org/legal/epl-2.0/
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "mqtt_napi_utils.h"
#include <cstring>
#include <initializer_list>
#include <memory>

#include "securec.h"

namespace OHOS {
namespace PahoMqtt {
namespace NapiUtils {
static constexpr const int MAX_STRING_LENGTH = 65536;

static constexpr const char *GLOBAL_JSON = "JSON";

static constexpr const char *GLOBAL_JSON_STRINGIFY = "stringify";

static constexpr const char *GLOBAL_JSON_PARSE = "parse";

napi_valuetype GetValueType(napi_env env, napi_value value)
{
    if (value == nullptr) {
        return napi_undefined;
    }

    napi_valuetype valueType = napi_undefined;
    napi_typeof(env, value, &valueType);
    return valueType;
}

/* named property */
bool HasNamedProperty(napi_env env, napi_value object, const std::string &propertyName)
{
    bool hasProperty = false;
    napi_has_named_property(env, object, propertyName.c_str(), &hasProperty);
    return hasProperty;
}

napi_value GetNamedProperty(napi_env env, napi_value object, const std::string &propertyName)
{
    napi_value value = nullptr;
    napi_get_named_property(env, object, propertyName.c_str(), &value);
    return value;
}

void SetNamedProperty(napi_env env, napi_value object, const std::string &name, napi_value value)
{
    (void)napi_set_named_property(env, object, name.c_str(), value);
}

std::vector<std::string> GetPropertyNames(napi_env env, napi_value object)
{
    std::vector<std::string> ret;
    napi_value names = nullptr;
    napi_get_property_names(env, object, &names);
    uint32_t length = 0;
    napi_get_array_length(env, names, &length);
    for (uint32_t index = 0; index < length; ++index) {
        napi_value name = nullptr;
        if (napi_get_element(env, names, index, &name) != napi_ok) {
            continue;
        }
        if (GetValueType(env, name) != napi_string) {
            continue;
        }
        ret.emplace_back(GetStringFromValueUtf8(env, name));
    }
    return ret;
}

/* UINT32 */
napi_value CreateUint32(napi_env env, uint32_t code)
{
    napi_value value = nullptr;
    if (napi_create_uint32(env, code, &value) != napi_ok) {
        return nullptr;
    }
    return value;
}

uint32_t GetUint32FromValue(napi_env env, napi_value value)
{
    uint32_t ret = 0;
    napi_get_value_uint32(env, value, &ret);
    return ret;
}

uint32_t GetUint32Property(napi_env env, napi_value object, const std::string &propertyName)
{
    if (!HasNamedProperty(env, object, propertyName)) {
        return 0;
    }
    napi_value value = GetNamedProperty(env, object, propertyName);
    return GetUint32FromValue(env, value);
}

void SetUint32Property(napi_env env, napi_value object, const std::string &name, uint32_t value)
{
    napi_value jsValue = CreateUint32(env, value);
    if (GetValueType(env, jsValue) != napi_number) {
        return;
    }

    napi_set_named_property(env, object, name.c_str(), jsValue);
}

/* INT32 */
napi_value CreateInt32(napi_env env, int32_t code)
{
    napi_value value = nullptr;
    if (napi_create_int32(env, code, &value) != napi_ok) {
        return nullptr;
    }
    return value;
}

int32_t GetInt32FromValue(napi_env env, napi_value value)
{
    int32_t ret = 0;
    napi_get_value_int32(env, value, &ret);
    return ret;
}

int32_t GetInt32Property(napi_env env, napi_value object, const std::string &propertyName)
{
    if (!HasNamedProperty(env, object, propertyName)) {
        return 0;
    }
    napi_value value = GetNamedProperty(env, object, propertyName);
    return GetInt32FromValue(env, value);
}

void SetInt32Property(napi_env env, napi_value object, const std::string &name, int32_t value)
{
    napi_value jsValue = CreateInt32(env, value);
    if (GetValueType(env, jsValue) != napi_number) {
        return;
    }

    napi_set_named_property(env, object, name.c_str(), jsValue);
}

/* INT64 */
napi_value CreateInt64(napi_env env, int64_t code) {
    napi_value value = nullptr;
    if (napi_create_int64(env, code, &value) != napi_ok) {
        return nullptr;
    }
    return value;
}

int64_t GetInt64FromValue(napi_env env, napi_value value) {
    int64_t ret = 0;
    napi_get_value_int64(env, value, &ret);
    return ret;
}

int64_t GetInt64Property(napi_env env, napi_value object, const std::string &propertyName) {
    if (!HasNamedProperty(env, object, propertyName)) {
        return 0;
    }
    napi_value value = GetNamedProperty(env, object, propertyName);
    return GetInt64FromValue(env, value);
}

void SetInt64Property(napi_env env, napi_value object, const std::string &name, int64_t value) {
    napi_value jsValue = CreateInt64(env, value);
    if (GetValueType(env, jsValue) != napi_number) {
        return;
    }

    napi_set_named_property(env, object, name.c_str(), jsValue);
}

/* String UTF8 */
napi_value CreateStringUtf8(napi_env env, const std::string &str)
{
    napi_value value = nullptr;
    if (napi_create_string_utf8(env, str.c_str(), strlen(str.c_str()), &value) != napi_ok) {
        return nullptr;
    }
    return value;
}

std::string GetStringFromValueUtf8(napi_env env, napi_value value)
{
    std::string result;
    auto deleter = [](char *s) { free(reinterpret_cast<void *>(s)); };
    std::unique_ptr<char, decltype(deleter)> str(static_cast<char *>(malloc(MAX_STRING_LENGTH)), deleter);
    (void)memset_s(str.get(), MAX_STRING_LENGTH, 0, MAX_STRING_LENGTH);
    size_t length = 0;
    napi_get_value_string_utf8(env, value, str.get(), MAX_STRING_LENGTH, &length);
    if (length > 0) {
        return result.append(str.get(), length);
    }
    return result;
}

std::string GetStringPropertyUtf8(napi_env env, napi_value object, const std::string &propertyName)
{
    if (!HasNamedProperty(env, object, propertyName)) {
        return "";
    }
    napi_value value = GetNamedProperty(env, object, propertyName);
    return GetStringFromValueUtf8(env, value);
}

void SetStringPropertyUtf8(napi_env env, napi_value object, const std::string &name, const std::string &value)
{
    napi_value jsValue = CreateStringUtf8(env, value);
    if (GetValueType(env, jsValue) != napi_string) {
        return;
    }
    napi_set_named_property(env, object, name.c_str(), jsValue);
}

/* array buffer */
bool ValueIsArrayBuffer(napi_env env, napi_value value)
{
    if (value == nullptr) {
        return false;
    }
    bool isArrayBuffer = false;
    napi_is_arraybuffer(env, value, &isArrayBuffer);
    return isArrayBuffer;
}

void *GetInfoFromArrayBufferValue(napi_env env, napi_value value, size_t *length)
{
    if (length == nullptr) {
        return nullptr;
    }

    void *data = nullptr;
    napi_get_arraybuffer_info(env, value, &data, length);
    return data;
}

napi_value CreateArrayBuffer(napi_env env, size_t length, void *data)
{
    if (length == 0) {
        return nullptr;
    }
    napi_value result = nullptr;
    void *nativePtr = nullptr;
    napi_status status = napi_create_arraybuffer(env, length, &nativePtr, &result);
    if (status == napi_ok) {
        memcpy(nativePtr, data, length);
    }
    return result;
}

void *GetArrayBufferProperty(napi_env env, napi_value object, const std::string &propertyName, size_t* infoLen ) {
    if (!HasNamedProperty(env, object, propertyName)) {
        return nullptr;
    }
    napi_value value = GetNamedProperty(env, object, propertyName);
    
    return GetInfoFromArrayBufferValue(env, value, infoLen);
}

void SetArrayBufferProperty(napi_env env, napi_value object, const std::string &name,  size_t length, void *data) {
    napi_value jsValue = CreateArrayBuffer(env, length, data);
    if (!ValueIsArrayBuffer(env, jsValue)) {
        return;
    }
    napi_set_named_property(env, object, name.c_str(), jsValue);
}

/* object */
napi_value CreateObject(napi_env env)
{
    napi_value object = nullptr;
    napi_create_object(env, &object);
    return object;
}

/* undefined */
napi_value GetUndefined(napi_env env)
{
    napi_value undefined = nullptr;
    napi_get_undefined(env, &undefined);
    return undefined;
}

/* function */
napi_value CallFunction(napi_env env, napi_value recv, napi_value func, size_t argc, const napi_value *argv)
{
    napi_value res = nullptr;
    napi_call_function(env, recv, func, argc, argv, &res);
    return res;
}

napi_value CreateFunction(napi_env env, const std::string &name, napi_callback func, void *arg)
{
    napi_value res = nullptr;
    napi_create_function(env, name.c_str(), strlen(name.c_str()), func, arg, &res);
    return res;
}

/* reference */
napi_ref CreateReference(napi_env env, napi_value callback)
{
    napi_ref callbackRef = nullptr;
    napi_create_reference(env, callback, 1, &callbackRef);
    return callbackRef;
}

napi_value GetReference(napi_env env, napi_ref callbackRef)
{
    napi_value callback = nullptr;
    napi_get_reference_value(env, callbackRef, &callback);
    return callback;
}

void DeleteReference(napi_env env, napi_ref callbackRef)
{
    (void)napi_delete_reference(env, callbackRef);
}

/* boolean */
bool GetBooleanProperty(napi_env env, napi_value object, const std::string &propertyName)
{
    if (!HasNamedProperty(env, object, propertyName)) {
        return false;
    }
    napi_value value = GetNamedProperty(env, object, propertyName);
    bool ret = false;
    napi_get_value_bool(env, value, &ret);
    return ret;
}

void SetBooleanProperty(napi_env env, napi_value object, const std::string &name, bool value)
{
    napi_value jsValue = nullptr;
    napi_get_boolean(env, value, &jsValue);
    if (GetValueType(env, jsValue) != napi_boolean) {
        return;
    }

    napi_set_named_property(env, object, name.c_str(), jsValue);
}

napi_value GetBoolean(napi_env env, bool value)
{
    napi_value jsValue = nullptr;
    napi_get_boolean(env, value, &jsValue);
    return jsValue;
}

/* define properties */
void DefineProperties(napi_env env, napi_value object,
                      const std::initializer_list<napi_property_descriptor> &properties)
{
    napi_property_descriptor descriptors[properties.size()];
    std::copy(properties.begin(), properties.end(), descriptors);

    (void)napi_define_properties(env, object, properties.size(), descriptors);
}

/* JSON */
napi_value JsonStringify(napi_env env, napi_value object)
{
    napi_value undefined = GetUndefined(env);

    if (GetValueType(env, object) != napi_object) {
        return undefined;
    }

    napi_value global = nullptr;
    napi_get_global(env, &global);
    napi_value json = nullptr;
    napi_get_named_property(env, global, GLOBAL_JSON, &json);
    napi_value stringify = nullptr;
    napi_get_named_property(env, json, GLOBAL_JSON_STRINGIFY, &stringify);
    if (GetValueType(env, stringify) != napi_function) {
        return undefined;
    }

    napi_value res = nullptr;
    napi_value argv[1] = {object};
    napi_call_function(env, json, stringify, 1, argv, &res);
    return res;
}

napi_value JsonParse(napi_env env, napi_value str)
{
    napi_value undefined = GetUndefined(env);

    if (GetValueType(env, str) != napi_string) {
        return undefined;
    }

    napi_value global = nullptr;
    napi_get_global(env, &global);
    napi_value json = nullptr;
    napi_get_named_property(env, global, GLOBAL_JSON, &json);
    napi_value parse = nullptr;
    napi_get_named_property(env, json, GLOBAL_JSON_PARSE, &parse);
    if (GetValueType(env, parse) != napi_function) {
        return undefined;
    }

    napi_value res = nullptr;
    napi_value argv[1] = {str};
    napi_call_function(env, json, parse, 1, argv, &res);
    return res;
}

/* libuv */
void CreateUvQueueWork(napi_env env, void *data, void(Handler)(uv_work_t *, int status))
{
    uv_loop_s *loop = nullptr;
    napi_get_uv_event_loop(env, &loop);

    auto work = new uv_work_t;
    work->data = data;

//    (void)uv_queue_work(
//            loop, work, [](uv_work_t *) {}, Handler);
}

/* scope */
napi_handle_scope OpenScope(napi_env env)
{
    napi_handle_scope scope = nullptr;
    napi_open_handle_scope(env, &scope);
    return scope;
}

void CloseScope(napi_env env, napi_handle_scope scope)
{
    (void)napi_close_handle_scope(env, scope);
}


std::vector<std::string> GetStringArrayProperty(napi_env env, napi_value object, const std::string &propertyName)
{
    std::vector<std::string> stringVector;
    napi_value array = nullptr;
    napi_status status = napi_get_named_property(env, object, propertyName.c_str(), &array);
    if (status != napi_ok) {
        return stringVector;
    }
    bool isArray = false;
    status = napi_is_array(env, array, &isArray);
    if (!(status == napi_ok && isArray)) {
        return stringVector;
    }
    uint32_t len = 0;
    status = napi_get_array_length(env, array, &len);
    if (!(status == napi_ok && len > 0)) {
        return stringVector;
    }
    for (int i = 0; i < len; i++) {
        napi_value value;
        status = napi_get_element(env, array, i, &value);
        if (status != napi_ok) {
            continue;
        }
        stringVector.push_back(GetStringFromValueUtf8(env, value));
    }
    return stringVector;
}

void CreateTsFunction(napi_env env, napi_value func, napi_value asyncResource, const std::string &resourceName,
                      uint32_t maxQueueSize, uint32_t threadCnt, void *threadFinalizeData, napi_finalize threadFinalizeCb, void *context,
                      napi_threadsafe_function_call_js callJsCallback, napi_threadsafe_function *result)
{
    LOG("AsyncMqtt napi_create_threadsafe_function start");
    napi_value resourceValue = CreateStringUtf8(env, resourceName);
    napi_create_threadsafe_function(env, func, asyncResource, resourceValue, maxQueueSize, threadCnt,
            threadFinalizeData, threadFinalizeCb, context, callJsCallback, result);
    LOG("AsyncMqtt napi_create_threadsafe_function end");
}
}
}
} // namespace OHOS::PahoMqtt::NapiUtils