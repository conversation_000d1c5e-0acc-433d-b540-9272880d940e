<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2021 Huawei Device Co., Ltd.

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.

    This is the configuration file template for OpenHarmony OSS Audit Tool, please copy it to your project root dir and modify it refer to OpenHarmony/tools_oat/README.
    All configurations in this file will be merged to OAT-Default.xml, if you have any questions or concerns, please create issue in OpenHarmony/tools_oat and @jalenchen or chenyaxun.

    licensefile:
    1.If the project don't have "LICENSE" in root dir, please define all the license files in this project in , OAT will check license files according to this rule.

    policylist:
    1. policy: If the OAT-Default.xml policies do not meet your requirements, please add policies here.
    2. policyitem: The fields type, name, path, desc is required, and the fields rule, group, filefilter is optional,the default value is:
    <policyitem type="" name="" path="" desc="" rule="may" group="defaultGroup" filefilter="defaultPolicyFilter"/>
    3. policyitem type:
        "compatibility" is used to check license compatibility in the specified path;
        "license" is used to check source license header in the specified path;
        "copyright" is used to check source copyright header in the specified path;
        "import" is used to check source dependency in the specified path, such as import ... ,include ...
        "filetype" is used to check file type in the specified path, supported file types: archive, binary
        "filename" is used to check whether the specified file exists in the specified path(projectroot means the root dir of the project), supported file names: LICENSE, README, README.OpenSource
    4. policyitem name: This field is used for define the license, copyright, "*" means match all, the "!" prefix means could not match this value. For example, "!GPL" means can not use GPL license.
    5. policyitem path: This field is used for define the source file scope to apply this policyitem, the "!" prefix means exclude the files. For example, "!.*/lib/.*" means files in lib dir will be exclude while process this policyitem.
    6. policyitem rule and group: These two fields are used together to merge policy results. "may" policyitems in the same group means any one in this group passed, the result will be passed.
    7. policyitem filefilter: Used to bind filefilter which define filter rules.
    7. policyitem desc: Used to describe the reason of this policy item, committers will check this while merging the code.
    8. filefilter: Filter rules, the type filename is used to filter file name, the type filepath is used to filter file path.

    Note:If the text contains special characters, please escape them according to the following rules:
    " == &gt;
    & == &gt;
    ' == &gt;
    < == &gt;
    > == &gt;
-->

<configuration>
    <oatconfig>
        <filefilterlist>
            <filefilter name="binaryFileTypePolicyFilter" desc="Filters for binary file policies">
                <filteritem type="filename" name="LICENSE" desc="官方自带文件"/>
            </filefilter>
        </filefilterlist>
    </oatconfig>
</configuration>
