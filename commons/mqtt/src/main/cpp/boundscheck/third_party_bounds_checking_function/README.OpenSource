[{"Name": "bounds_checking_function", "License": "Mulan Permissive Software License，Version 2", "License File": "LICENSE", "Version Number": "v1.1.11", "Owner": "<EMAIL>", "Upstream URL": "https://gitee.com/openeuler/libboundscheck", "Description": "following the standard of C11 Annex K (bound-checking interfaces), functions of the common memory/string operation classes, such as memcpy_s, strcpy_s, are selected and implemented."}]