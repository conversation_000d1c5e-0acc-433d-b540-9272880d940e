name: "Builds for MacOS"
on: [push, pull_request] 

jobs:
  build:
    runs-on: macos-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v2
      - name: Install dependencies
        run: |
          brew update
          brew install doxygen
      - name: Build
        run: |
          rm -rf build.paho
          mkdir build.paho
          cd build.paho
          echo "pwd $PWD"
          cmake -DPAHO_BUILD_STATIC=FALSE -DPAHO_BUILD_SHARED=TRUE -DCMAKE_BUILD_TYPE=Debug -DPAHO_WITH_SSL=TRUE -DOPENSSL_ROOT_DIR=/usr/local/opt/openssl@1.1 -DPAHO_BUILD_DOCUMENTATION=FALSE -DPAHO_BUILD_SAMPLES=TRUE -DPAHO_HIGH_PERFORMANCE=TRUE ..
          cmake --build .
      - name: Start test broker
        run: |
          git clone https://github.com/eclipse/paho.mqtt.testing.git
          cd paho.mqtt.testing/interoperability
          python3 startbroker.py -c localhost_testing.conf &
      - name: Start test proxy
        run: |
          python3 test/mqttsas.py &
      - name: run tests
        run: |
          cd build.paho
          ctest -VV --timeout 600 
      - name: clean up
        run: |
          killall python3 || true
          sleep 3 # allow broker time to terminate and report
      - name: package
        run: |
          cd build.paho
          cpack --verbose
