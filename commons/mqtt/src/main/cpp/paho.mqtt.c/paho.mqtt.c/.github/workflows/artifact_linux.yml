name: "Upload release artifact for Linux"
on: [workflow_dispatch]

jobs:
  build:
    runs-on: ubuntu-18.04
    steps:
      - name: Check out code
        uses: actions/checkout@v2
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install doxygen
      - name: Build
        run: |
          rm -rf build.paho
          mkdir build.paho
          cd build.paho
          echo "pwd $PWD"
          cmake -DPAHO_BUILD_STATIC=FALSE -DPAHO_BUILD_SHARED=TRUE -DCMAKE_BUILD_TYPE=Debug -DPAHO_WITH_SSL=TRUE -DOPENSSL_ROOT_DIR= -DPAHO_BUILD_DOCUMENTATION=FALSE -DPAHO_BUILD_SAMPLES=TRUE -DPAHO_HIGH_PERFORMANCE=TRUE ..
          cmake --build .
      - name: package
        run: |
          cd build.paho
          cpack --verbose         
      - name: Upload artifact
        uses: actions/upload-artifact@v3
        with:
          name: Eclipse-Paho-MQTT-C-1.3.12-Linux.tar.gz
          path: /home/<USER>/work/paho.mqtt.c/paho.mqtt.c/build.paho/Eclipse-Paho-MQTT-C-1.3.12-Linux.tar.gz
