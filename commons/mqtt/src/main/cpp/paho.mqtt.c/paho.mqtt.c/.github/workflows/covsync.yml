name: "Synchronise Coverity Scan branches on a weekly basis"

on:
  workflow_dispatch:
  schedule:
    - cron: "7 3 * * 0"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - run: |
          git checkout -b coverity-develop origin/develop
          git pull origin coverity-develop	
          git push origin coverity-develop
