# Example: Android Native Library makefile for paho.mqtt.c
# contributed by <PERSON> <<EMAIL>>

LOCAL_PATH := $(call my-dir)
libpaho-mqtt3_lib_path := ../src
libpaho-mqtt3_c_includes := $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path) \
	external/hdc/android-ifaddrs \
	external/openssl/include \
	external/zlib

# build sample util
define build_sample_util
__sample_module:= $1
__sample_lib:= $2
include $(CLEAR_VARS)
LOCAL_C_INCLUDES := $(libpaho-mqtt3_c_includes)
LOCAL_SHARED_LIBRARIES := $$(__sample_lib)
LOCAL_MODULE := $$(__sample_module)
LOCAL_SRC_FILES := $(libpaho-mqtt3_lib_path)/samples/$$(__sample_module).c
include $(BUILD_EXECUTABLE)
endef

libpaho-mqtt3_local_src_c_files_common := \
	$(libpaho-mqtt3_lib_path)/MQTTProtocolClient.c \
	$(libpaho-mqtt3_lib_path)/Tree.c \
	$(libpaho-mqtt3_lib_path)/Heap.c \
	$(libpaho-mqtt3_lib_path)/MQTTPacket.c \
	$(libpaho-mqtt3_lib_path)/Clients.c \
	$(libpaho-mqtt3_lib_path)/Thread.c \
	$(libpaho-mqtt3_lib_path)/utf-8.c \
	$(libpaho-mqtt3_lib_path)/StackTrace.c \
	$(libpaho-mqtt3_lib_path)/MQTTProtocolOut.c \
	$(libpaho-mqtt3_lib_path)/Socket.c \
	$(libpaho-mqtt3_lib_path)/Log.c \
	$(libpaho-mqtt3_lib_path)/Messages.c \
	$(libpaho-mqtt3_lib_path)/LinkedList.c \
	$(libpaho-mqtt3_lib_path)/MQTTPersistence.c \
	$(libpaho-mqtt3_lib_path)/MQTTPacketOut.c \
	$(libpaho-mqtt3_lib_path)/SocketBuffer.c \
	$(libpaho-mqtt3_lib_path)/MQTTPersistenceDefault.c \

libpaho-mqtt3_local_src_c_files_c := \
	$(libpaho-mqtt3_lib_path)/MQTTClient.c \

libpaho-mqtt3_local_src_c_files_cs := \
	$(libpaho-mqtt3_lib_path)/MQTTClient.c \
	$(libpaho-mqtt3_lib_path)/SSLSocket.c \

libpaho-mqtt3_local_src_c_files_a := \
	$(libpaho-mqtt3_lib_path)/MQTTAsync.c \

libpaho-mqtt3_local_src_c_files_as := \
	$(libpaho-mqtt3_lib_path)/MQTTAsync.c \
	$(libpaho-mqtt3_lib_path)/SSLSocket.c \

# update the header file which normally generated by cmake
$(shell (cp -f $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)/VersionInfo.h.in $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)/VersionInfo.h))
$(shell (sed -i "s/@CLIENT_VERSION@/1.2.0/g" $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)/VersionInfo.h))
$(shell ( sed -i "s/@BUILD_TIMESTAMP@/$(shell date)/g" $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)/VersionInfo.h))

# building static libraries

include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3c
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_c)
include $(BUILD_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3cs
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_CFLAGS += -DOPENSSL
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_cs)
include $(BUILD_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3a
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/${libpaho-mqtt3_lib_path}
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_a)
include $(BUILD_STATIC_LIBRARY)
  
include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3as
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/${libpaho-mqtt3_lib_path}
LOCAL_CFLAGS += -DOPENSSL
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_as)
include $(BUILD_STATIC_LIBRARY)

# building shared libraries

include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3c
LOCAL_SHARED_LIBRARIES := libdl
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_c)
include $(BUILD_SHARED_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3cs
LOCAL_SHARED_LIBRARIES := libcrypto libssl libdl
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/$(libpaho-mqtt3_lib_path)
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_CFLAGS += -DOPENSSL
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_cs)
include $(BUILD_SHARED_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3a
LOCAL_SHARED_LIBRARIES := libdl
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/${libpaho-mqtt3_lib_path}
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_a)
include $(BUILD_SHARED_LIBRARY)
 
include $(CLEAR_VARS)
LOCAL_MODULE    := libpaho-mqtt3as
LOCAL_SHARED_LIBRARIES := libcrypto libssl libdl
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/${libpaho-mqtt3_lib_path}
LOCAL_CFLAGS += -DOPENSSL
LOCAL_C_INCLUDES:= $(libpaho-mqtt3_c_includes)
LOCAL_SRC_FILES := $(libpaho-mqtt3_local_src_c_files_common) $(libpaho-mqtt3_local_src_c_files_as)
include $(BUILD_SHARED_LIBRARY)

# building samples

$(eval $(call build_sample_util, MQTTAsync_subscribe, libpaho-mqtt3a ) )
$(eval $(call build_sample_util, MQTTAsync_publish, libpaho-mqtt3a ) )
$(eval $(call build_sample_util, MQTTClient_publish, libpaho-mqtt3c ) )
$(eval $(call build_sample_util, MQTTClient_publish_async, libpaho-mqtt3c ) )
$(eval $(call build_sample_util, MQTTClient_subscribe, libpaho-mqtt3c ) )
$(eval $(call build_sample_util, paho_c_pub, libpaho-mqtt3a ) )
$(eval $(call build_sample_util, paho_c_sub, libpaho-mqtt3a ) )
$(eval $(call build_sample_util, paho_cs_pub, libpaho-mqtt3c ) )
$(eval $(call build_sample_util, paho_cs_sub, libpaho-mqtt3c ) )

