eclipse.preferences.version=1
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/BUILD_TYPE/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/BUILD_TYPE/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/BUILD_TYPE/value=debug
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/CFLAGS/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/CFLAGS/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/CFLAGS/value=-m32
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/LDFLAGS/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/LDFLAGS/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/LDFLAGS/value=-m32
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/MACHINETYPE/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/MACHINETYPE/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/MACHINETYPE/value=x86
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/OSTYPE/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/OSTYPE/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/OSTYPE/value=Linux
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/append=true
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.128299804/appendContributed=true
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/BUILD_TYPE/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/BUILD_TYPE/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/BUILD_TYPE/value=debug
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/CC/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/CC/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/CC/value=arm-linux-gnueabihf-gcc
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/CFLAGS/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/CFLAGS/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/CFLAGS/value=-Idep/arm-linux-gnueabihf/include -Idep/arm-linux-gnueabihf/include/arm-linux-gnueabihf
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/LDFLAGS/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/LDFLAGS/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/LDFLAGS/value=-Ldep/arm-linux-gnueabihf/lib
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/MACHINETYPE/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/MACHINETYPE/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/MACHINETYPE/value=arm-linux-gnueabihf
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/OSTYPE/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/OSTYPE/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/OSTYPE/value=Linux
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/append=true
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643.1588218084/appendContributed=true
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643/BUILD_TYPE/delimiter=\:
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643/BUILD_TYPE/operation=append
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643/BUILD_TYPE/value=debug
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643/append=true
environment/project/cdt.managedbuild.toolchain.gnu.base.2116872643/appendContributed=true
