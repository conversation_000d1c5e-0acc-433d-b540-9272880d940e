<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">MQTTAsync.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="_m_q_t_t_async_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*******************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2009, 2022 IBM Corp., Ian Craggs and others</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * All rights reserved. This program and the accompanying materials</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * are made available under the terms of the Eclipse Public License v2.0</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * and Eclipse Distribution License v1.0 which accompany this distribution.</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> * The Eclipse Public License is available at</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *    https://www.eclipse.org/legal/epl-2.0/</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * and the Eclipse Distribution License is available at</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *   http://www.eclipse.org/org/documents/edl-v10.php.</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * Contributors:</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *    Ian Craggs - initial API and implementation</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> *    Ian Craggs, Allan Stockdill-Mander - SSL connections</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> *    Ian Craggs - multiple server connection support</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> *    Ian Craggs - MQTT 3.1.1 support</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> *    Ian Craggs - fix for bug 444103 - success/failure callbacks not invoked</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> *    Ian Craggs - automatic reconnect and offline buffering (send while disconnected)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> *    Ian Craggs - binary will message</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> *    Ian Craggs - binary password</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> *    Ian Craggs - remove const on eyecatchers #168</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *    Ian Craggs - MQTT 5.0</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> *******************************************************************************/</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="comment">/********************************************************************/</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="preprocessor">#if !defined(MQTTASYNC_H)</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="preprocessor">#define MQTTASYNC_H</span></div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160; </div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="preprocessor">#if defined(__cplusplus)</span></div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160; <span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> {</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160; </div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160; </div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="preprocessor">#include &quot;MQTTExportDeclarations.h&quot;</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_properties_8h.html">MQTTProperties.h</a>&quot;</span></div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_reason_codes_8h.html">MQTTReasonCodes.h</a>&quot;</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_subscribe_opts_8h.html">MQTTSubscribeOpts.h</a>&quot;</span></div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">#if !defined(NO_PERSISTENCE)</span></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_m_q_t_t_client_persistence_8h.html">MQTTClientPersistence.h</a>&quot;</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">  113</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_SUCCESS 0</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">  118</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_FAILURE -1</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="comment">/* error code -2 is MQTTAsync_PERSISTENCE_ERROR */</span></div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160; </div>
<div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a4edf1249c75abd4975fec8ddeae2cdc9">  122</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_PERSISTENCE_ERROR -2</span></div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; </div>
<div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a66c0f30b329bc770145c2f04b3929df6">  127</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_DISCONNECTED -3</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160; </div>
<div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ad577286d43c72fbc49818aac42f4e24a">  132</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_MAX_MESSAGES_INFLIGHT -4</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160; </div>
<div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a80cbe091930c11b67ca719b3e385aa26">  136</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_BAD_UTF8_STRING -5</span></div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab88e1ebcee991099a72429e52a8253fd">  140</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_NULL_PARAMETER -6</span></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160; </div>
<div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a77a7106d97ff60be3fe70f90b1867800">  146</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_TOPICNAME_TRUNCATED -7</span></div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; </div>
<div class="line"><a name="l00151"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a241fc8db46dca132d591bc2be92247ba">  151</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_BAD_STRUCTURE -8</span></div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a64d111778ce4e0d3a62808f6db11f224">  155</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_BAD_QOS -9</span></div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160; </div>
<div class="line"><a name="l00159"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab0f54d0bae2c74849022a8009e5d6ff7">  159</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_NO_MORE_MSGIDS -10</span></div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160; </div>
<div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aee1b79d0632bec0fe49eb7ea1abd3b2e">  163</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_OPERATION_INCOMPLETE -11</span></div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160; </div>
<div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a4e338072cfd5291b579e4f0c99a6e773">  167</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_MAX_BUFFERED_MESSAGES -12</span></div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160; </div>
<div class="line"><a name="l00171"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6992c00553db1608aef9e162c161d73c">  171</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_SSL_NOT_SUPPORTED -13</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160; </div>
<div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a785250cd4a1938ffeeff67b3538abfba">  182</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_BAD_PROTOCOL -14</span></div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160; </div>
<div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#af6f97562573876867ba77460a51ca1d1">  186</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_BAD_MQTT_OPTION -15</span></div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160; </div>
<div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#af5df806e9767e1e3182fe089a8ee551b">  190</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_WRONG_MQTT_VERSION -16</span></div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160; </div>
<div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a47b3aed75983f48a503e1cad6c862004">  194</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_0_LEN_WILL_TOPIC -17</span></div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="comment"> * Return code: connect or disconnect command ignored because there is already a connect or disconnect</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;<span class="comment"> * command at the head of the list waiting to be processed. Use the onSuccess/onFailure callbacks to wait</span></div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;<span class="comment"> * for the previous connect or disconnect command to be complete.</span></div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;<span class="comment"> */</span></div>
<div class="line"><a name="l00200"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a8278cf4b50dd818c31fa12e45f074b5c">  200</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_COMMAND_IGNORED -18</span></div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160; <span class="comment">/*</span></div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="comment">  * Return code: maxBufferedMessages in the connect options must be &gt;= 0</span></div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;<span class="comment">  */</span></div>
<div class="line"><a name="l00204"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2efee8e190e2c3690c680bde060f78ab">  204</a></span>&#160;<span class="preprocessor"> #define MQTTASYNC_MAX_BUFFERED -19</span></div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160; </div>
<div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">  209</a></span>&#160;<span class="preprocessor">#define MQTTVERSION_DEFAULT 0</span></div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160; </div>
<div class="line"><a name="l00213"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a4603b988e76872e1f23f135d225ce2fb">  213</a></span>&#160;<span class="preprocessor">#define MQTTVERSION_3_1 3</span></div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160; </div>
<div class="line"><a name="l00217"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">  217</a></span>&#160;<span class="preprocessor">#define MQTTVERSION_3_1_1 4</span></div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160; </div>
<div class="line"><a name="l00221"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">  221</a></span>&#160;<span class="preprocessor">#define MQTTVERSION_5 5</span></div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160; </div>
<div class="line"><a name="l00225"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">  225</a></span>&#160;<span class="preprocessor">#define MQTT_BAD_SUBSCRIBE 0x80</span></div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160; </div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html">  231</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;{</div>
<div class="line"><a name="l00234"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html#aa5326df180cb23c59afbcab711a06479">  234</a></span>&#160;        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a name="l00236"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html#a0761a5e5be0383882e42924de8e51f82">  236</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__init__options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l00238"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__init__options.html#a5929146596391e2838ef95feb89776da">  238</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__init__options.html#a5929146596391e2838ef95feb89776da">do_openssl_init</a>;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;} <a class="code" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a>;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160; </div>
<div class="line"><a name="l00241"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a866e023f70141969d48597930c0ee313">  241</a></span>&#160;<span class="preprocessor">#define MQTTAsync_init_options_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;G&#39;}, 0, 0 }</span></div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160; </div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;LIBMQTT_API <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">MQTTAsync_global_init</a>(<a class="code" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a>* inits);</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160; </div>
<div class="line"><a name="l00253"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">  253</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span>* <a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>;</div>
<div class="line"><a name="l00263"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">  263</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>;</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160; </div>
<div class="line"><a name="l00271"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html">  271</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;{</div>
<div class="line"><a name="l00274"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#aa5326df180cb23c59afbcab711a06479">  274</a></span>&#160;        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a name="l00277"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a0761a5e5be0383882e42924de8e51f82">  277</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__message.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l00279"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">  279</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">payloadlen</a>;</div>
<div class="line"><a name="l00281"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">  281</a></span>&#160;        <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">payload</a>;</div>
<div class="line"><a name="l00295"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">  295</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a name="l00314"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">  314</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a>;</div>
<div class="line"><a name="l00321"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#adc4cf3f551bb367858644559d69cfdf5">  321</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__message.html#adc4cf3f551bb367858644559d69cfdf5">dup</a>;</div>
<div class="line"><a name="l00327"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a6174c42da8c55c86e7255be2848dc4ac">  327</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__message.html#a6174c42da8c55c86e7255be2848dc4ac">msgid</a>;</div>
<div class="line"><a name="l00331"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__message.html#a1594008402f7307e4de8fa6131656dde">  331</a></span>&#160;        <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code" href="struct_m_q_t_t_async__message.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;} <a class="code" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>;</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160; </div>
<div class="line"><a name="l00334"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6a85061dadab532f28e96e5ab3c600e9">  334</a></span>&#160;<span class="preprocessor">#define MQTTAsync_message_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;M&#39;}, 1, 0, NULL, 0, 0, 0, 0, MQTTProperties_initializer }</span></div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160; </div>
<div class="line"><a name="l00369"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">  369</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* topicName, <span class="keywordtype">int</span> topicLen, <a class="code" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>* message);</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160; </div>
<div class="line"><a name="l00392"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">  392</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a>(<span class="keywordtype">void</span>* context, <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token);</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160; </div>
<div class="line"><a name="l00412"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">  412</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* cause);</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160; </div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160; </div>
<div class="line"><a name="l00430"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">  430</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a>(<span class="keywordtype">void</span>* context, <span class="keywordtype">char</span>* cause);</div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160; </div>
<div class="line"><a name="l00444"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">  444</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a>(<span class="keywordtype">void</span>* context, <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a>* properties,</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;                <span class="keyword">enum</span> <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode);</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160; </div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">MQTTAsync_setDisconnected</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a>* co);</div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160; </div>
<div class="line"><a name="l00465"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html">  465</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;{</div>
<div class="line"><a name="l00468"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#aa5326df180cb23c59afbcab711a06479">  468</a></span>&#160;        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a name="l00470"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#a0761a5e5be0383882e42924de8e51f82">  470</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_data.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l00477"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#aba2dfcdfda80edcb531a5a7115d3e043">  477</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__connect_data.html#aba2dfcdfda80edcb531a5a7115d3e043">username</a>;</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;        <span class="keyword">struct </span>{</div>
<div class="line"><a name="l00484"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#afed088663f8704004425cdae2120b9b3">  484</a></span>&#160;                <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_data.html#afed088663f8704004425cdae2120b9b3">len</a>;           </div>
<div class="line"><a name="l00485"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_data.html#a0d49d74db4c035719c3867723cf7e779">  485</a></span>&#160;                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__connect_data.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;        } binarypwd;</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;} <a class="code" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a>;</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160; </div>
<div class="line"><a name="l00489"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2e415e68016ae56f6bbbbdc9840a9c6e">  489</a></span>&#160;<span class="preprocessor">#define MQTTAsync_connectData_initializer {{&#39;M&#39;, &#39;Q&#39;, &#39;C&#39;, &#39;D&#39;}, 0, NULL, {0, NULL}}</span></div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160; </div>
<div class="line"><a name="l00497"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">  497</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a>(<span class="keywordtype">void</span>* context, <a class="code" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a>* data);</div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160; </div>
<div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">MQTTAsync_setUpdateConnectOptions</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a>* co);</div>
<div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160; </div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">MQTTAsync_setBeforePersistenceWrite</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a>* co);</div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160; </div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160; </div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">MQTTAsync_setAfterPersistenceRead</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* context, <a class="code" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a>* co);</div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160; </div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160; </div>
<div class="line"><a name="l00535"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html">  535</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;{</div>
<div class="line"><a name="l00538"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">  538</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code" href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a name="l00540"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">  540</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">code</a>;</div>
<div class="line"><a name="l00542"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data.html#a254bf0858da09c96a48daf64404eb4f8">  542</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="struct_m_q_t_t_async__failure_data.html#a254bf0858da09c96a48daf64404eb4f8">message</a>;</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;} <a class="code" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a>;</div>
<div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160; </div>
<div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160; </div>
<div class="line"><a name="l00547"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html">  547</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;{</div>
<div class="line"><a name="l00550"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#aa5326df180cb23c59afbcab711a06479">  550</a></span>&#160;        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a name="l00552"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a0761a5e5be0383882e42924de8e51f82">  552</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__failure_data5.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l00554"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#af8f771e67d284379111151b003c0d810">  554</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code" href="struct_m_q_t_t_async__failure_data5.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a name="l00556"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">  556</a></span>&#160;        <span class="keyword">enum</span> <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode;</div>
<div class="line"><a name="l00558"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a1594008402f7307e4de8fa6131656dde">  558</a></span>&#160;        <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code" href="struct_m_q_t_t_async__failure_data5.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a name="l00560"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a45a5b7c00a796a23f01673cef1dbe0a9">  560</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__failure_data5.html#a45a5b7c00a796a23f01673cef1dbe0a9">code</a>;</div>
<div class="line"><a name="l00562"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a254bf0858da09c96a48daf64404eb4f8">  562</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="struct_m_q_t_t_async__failure_data5.html#a254bf0858da09c96a48daf64404eb4f8">message</a>;</div>
<div class="line"><a name="l00564"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__failure_data5.html#a38dfee9f038f473c95af46fcef5dd3e9">  564</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__failure_data5.html#a38dfee9f038f473c95af46fcef5dd3e9">packet_type</a>;</div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;} <a class="code" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a>;</div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160; </div>
<div class="line"><a name="l00567"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a53ce2002ae2c2579575bb41c48c51c29">  567</a></span>&#160;<span class="preprocessor">#define MQTTAsync_failureData5_initializer {{&#39;M&#39;, &#39;Q&#39;, &#39;F&#39;, &#39;D&#39;}, 0, 0, MQTTREASONCODE_SUCCESS, MQTTProperties_initializer, 0, NULL, 0}</span></div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160; </div>
<div class="line"><a name="l00570"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html">  570</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;{</div>
<div class="line"><a name="l00573"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">  573</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code" href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;        <span class="keyword">union</span></div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;        {</div>
<div class="line"><a name="l00579"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a35738099155a0e4f54050da474bab2e7">  579</a></span>&#160;                <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__success_data.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a name="l00582"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a82786d9ba5cae39873f378a48b36c23b">  582</a></span>&#160;                <span class="keywordtype">int</span>* <a class="code" href="struct_m_q_t_t_async__success_data.html#a82786d9ba5cae39873f378a48b36c23b">qosList</a>;</div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;                <span class="keyword">struct</span></div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;                {</div>
<div class="line"><a name="l00586"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a6ed8403758cecd2f762af6ba5e0ae525">  586</a></span>&#160;                        <a class="code" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> <a class="code" href="struct_m_q_t_t_async__success_data.html#a6ed8403758cecd2f762af6ba5e0ae525">message</a>; </div>
<div class="line"><a name="l00587"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#ae25f4a1d2a3fa952d052a965376d8fef">  587</a></span>&#160;                        <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__success_data.html#ae25f4a1d2a3fa952d052a965376d8fef">destinationName</a>;     </div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;                } pub;</div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;                <span class="comment">/* For connect, the server connected to, MQTT version used, and sessionPresent flag */</span></div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;                <span class="keyword">struct</span></div>
<div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;                {</div>
<div class="line"><a name="l00592"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a95309fdf27015b12bc4adf56306e557b">  592</a></span>&#160;                        <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__success_data.html#a95309fdf27015b12bc4adf56306e557b">serverURI</a>; </div>
<div class="line"><a name="l00593"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a12d546fd0ccf4e1091b18e1b735c7240">  593</a></span>&#160;                        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__success_data.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>; </div>
<div class="line"><a name="l00594"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data.html#a44baf2cb9a0bbcec3ed2eace43f832d1">  594</a></span>&#160;                        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__success_data.html#a44baf2cb9a0bbcec3ed2eace43f832d1">sessionPresent</a>; </div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;                } connect;</div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;        } alt;</div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;} <a class="code" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a>;</div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160; </div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160; </div>
<div class="line"><a name="l00601"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html">  601</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;{</div>
<div class="line"><a name="l00603"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#aa5326df180cb23c59afbcab711a06479">  603</a></span>&#160;        <span class="keywordtype">char</span> struct_id[4];      </div>
<div class="line"><a name="l00604"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a0761a5e5be0383882e42924de8e51f82">  604</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__success_data5.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;     </div>
<div class="line"><a name="l00606"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#af8f771e67d284379111151b003c0d810">  606</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code" href="struct_m_q_t_t_async__success_data5.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a name="l00607"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a580d8a8ecb285f5a86c2a3865438f8ee">  607</a></span>&#160;        <span class="keyword">enum</span> <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode;        </div>
<div class="line"><a name="l00608"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a1594008402f7307e4de8fa6131656dde">  608</a></span>&#160;        <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code" href="struct_m_q_t_t_async__success_data5.html#a1594008402f7307e4de8fa6131656dde">properties</a>;              </div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;        <span class="keyword">union</span></div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;        {</div>
<div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;                <span class="keyword">struct</span></div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;                {</div>
<div class="line"><a name="l00615"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#ac97316626bd4faa6b71277c221275f4b">  615</a></span>&#160;                        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__success_data5.html#ac97316626bd4faa6b71277c221275f4b">reasonCodeCount</a>; </div>
<div class="line"><a name="l00616"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a2199c9d905dbfa279895cf8123c10f4f">  616</a></span>&#160;                        <span class="keyword">enum</span> <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>* <a class="code" href="struct_m_q_t_t_async__success_data5.html#a2199c9d905dbfa279895cf8123c10f4f">reasonCodes</a>; </div>
<div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;                } sub;</div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;                <span class="keyword">struct</span></div>
<div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;                {</div>
<div class="line"><a name="l00621"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a6ed8403758cecd2f762af6ba5e0ae525">  621</a></span>&#160;                        <a class="code" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> <a class="code" href="struct_m_q_t_t_async__success_data5.html#a6ed8403758cecd2f762af6ba5e0ae525">message</a>; </div>
<div class="line"><a name="l00622"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#ae25f4a1d2a3fa952d052a965376d8fef">  622</a></span>&#160;                        <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__success_data5.html#ae25f4a1d2a3fa952d052a965376d8fef">destinationName</a>;     </div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;                } pub;</div>
<div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;                <span class="comment">/* For connect, the server connected to, MQTT version used, and sessionPresent flag */</span></div>
<div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;                <span class="keyword">struct</span></div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;                {</div>
<div class="line"><a name="l00627"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a95309fdf27015b12bc4adf56306e557b">  627</a></span>&#160;                        <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__success_data5.html#a95309fdf27015b12bc4adf56306e557b">serverURI</a>;  </div>
<div class="line"><a name="l00628"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a12d546fd0ccf4e1091b18e1b735c7240">  628</a></span>&#160;                        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__success_data5.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;  </div>
<div class="line"><a name="l00629"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__success_data5.html#a44baf2cb9a0bbcec3ed2eace43f832d1">  629</a></span>&#160;                        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__success_data5.html#a44baf2cb9a0bbcec3ed2eace43f832d1">sessionPresent</a>;  </div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;                } connect;</div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;                <span class="keyword">struct</span></div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;                {</div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;                        <span class="keywordtype">int</span> reasonCodeCount; </div>
<div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;                        <span class="keyword">enum</span> <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>* reasonCodes; </div>
<div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;                } unsub;</div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;        } alt;</div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;} <a class="code" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a>;</div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160; </div>
<div class="line"><a name="l00640"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6182ec90ec4a134465f627b324ac5a41">  640</a></span>&#160;<span class="preprocessor">#define MQTTAsync_successData5_initializer {{&#39;M&#39;, &#39;Q&#39;, &#39;S&#39;, &#39;D&#39;}, 0, 0, MQTTREASONCODE_SUCCESS, MQTTProperties_initializer, {.sub={0,0}}}</span></div>
<div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160; </div>
<div class="line"><a name="l00655"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">  655</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>(<span class="keywordtype">void</span>* context, <a class="code" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a>* response);</div>
<div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160; </div>
<div class="line"><a name="l00671"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">  671</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>(<span class="keywordtype">void</span>* context, <a class="code" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a>* response);</div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160; </div>
<div class="line"><a name="l00686"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">  686</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>(<span class="keywordtype">void</span>* context,  <a class="code" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a>* response);</div>
<div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160; </div>
<div class="line"><a name="l00701"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">  701</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>(<span class="keywordtype">void</span>* context,  <a class="code" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a>* response);</div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160; </div>
<div class="line"><a name="l00708"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html">  708</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;{</div>
<div class="line"><a name="l00711"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">  711</a></span>&#160;        <span class="keywordtype">char</span> <a class="code" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a name="l00714"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a0761a5e5be0383882e42924de8e51f82">  714</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__response_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l00720"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">  720</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* <a class="code" href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a>;</div>
<div class="line"><a name="l00726"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">  726</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* <a class="code" href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a>;</div>
<div class="line"><a name="l00732"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">  732</a></span>&#160;        <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>;</div>
<div class="line"><a name="l00739"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#af8f771e67d284379111151b003c0d810">  739</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> <a class="code" href="struct_m_q_t_t_async__response_options.html#af8f771e67d284379111151b003c0d810">token</a>;</div>
<div class="line"><a name="l00745"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a1c23c490f06428725345de68a4ff0a3e">  745</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* <a class="code" href="struct_m_q_t_t_async__response_options.html#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a>;</div>
<div class="line"><a name="l00751"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">  751</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* <a class="code" href="struct_m_q_t_t_async__response_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a>;</div>
<div class="line"><a name="l00755"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a1594008402f7307e4de8fa6131656dde">  755</a></span>&#160;        <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code" href="struct_m_q_t_t_async__response_options.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;<span class="comment">         * MQTT V5 subscribe options, when used with subscribe only.</span></div>
<div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l00759"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a16a3cd2a8c69669e9ed6e420ccd9c517">  759</a></span>&#160;        <a class="code" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a> <a class="code" href="struct_m_q_t_t_async__response_options.html#a16a3cd2a8c69669e9ed6e420ccd9c517">subscribeOptions</a>;</div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;<span class="comment">         * MQTT V5 subscribe option count, when used with subscribeMany only.</span></div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;<span class="comment">         * The number of entries in the subscribe_options_list array.</span></div>
<div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l00764"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a1a4b9bb2780472ec7bb65d0df1bf5d26">  764</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__response_options.html#a1a4b9bb2780472ec7bb65d0df1bf5d26">subscribeOptionsCount</a>;</div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;<span class="comment">         * MQTT V5 subscribe option array, when used with subscribeMany only.</span></div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l00768"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__response_options.html#a98f71c5d03dc5ee86fd9dc0119ccb961">  768</a></span>&#160;        <a class="code" href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a>* <a class="code" href="struct_m_q_t_t_async__response_options.html#a98f71c5d03dc5ee86fd9dc0119ccb961">subscribeOptionsList</a>;</div>
<div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;} <a class="code" href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">MQTTAsync_responseOptions</a>;</div>
<div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160; </div>
<div class="line"><a name="l00771"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">  771</a></span>&#160;<span class="preprocessor">#define MQTTAsync_responseOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;R&#39;}, 1, NULL, NULL, 0, 0, NULL, NULL, MQTTProperties_initializer, MQTTSubscribe_options_initializer, 0, NULL}</span></div>
<div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160; </div>
<div class="line"><a name="l00774"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">  774</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> <a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_callOptions</a>;</div>
<div class="line"><a name="l00775"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a570185766fc8a9da410a6f84915b6df5">  775</a></span>&#160;<span class="preprocessor">#define MQTTAsync_callOptions_initializer MQTTAsync_responseOptions_initializer</span></div>
<div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160; </div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>, <a class="code" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a>* cl,</div>
<div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;                                                                         <a class="code" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a>* ma, <a class="code" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a>* dc);</div>
<div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160; </div>
<div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">MQTTAsync_setConnectionLostCallback</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>,</div>
<div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;                                                                                                  <a class="code" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a>* cl);</div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160; </div>
<div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">MQTTAsync_setMessageArrivedCallback</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>,</div>
<div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;                                                                                                  <a class="code" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a>* ma);</div>
<div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160; </div>
<div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">MQTTAsync_setDeliveryCompleteCallback</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>,</div>
<div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;                                                                                                        <a class="code" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a>* dc);</div>
<div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160; </div>
<div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">MQTTAsync_setConnected</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>, <a class="code" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a>* co);</div>
<div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160; </div>
<div class="line"><a name="l00892"></a><span class="lineno">  892</span>&#160; </div>
<div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">MQTTAsync_reconnect</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle);</div>
<div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160; </div>
<div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160; </div>
<div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>* handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* serverURI, <span class="keyword">const</span> <span class="keywordtype">char</span>* clientId,</div>
<div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;                <span class="keywordtype">int</span> persistence_type, <span class="keywordtype">void</span>* persistence_context);</div>
<div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160; </div>
<div class="line"><a name="l00960"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html">  960</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;{</div>
<div class="line"><a name="l00963"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#aa5326df180cb23c59afbcab711a06479">  963</a></span>&#160;        <span class="keywordtype">char</span> <a class="code" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a name="l00969"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a0761a5e5be0383882e42924de8e51f82">  969</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l00971"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a078cd68d8f896ce7eac0cc83d4486a2c">  971</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#a078cd68d8f896ce7eac0cc83d4486a2c">sendWhileDisconnected</a>;</div>
<div class="line"><a name="l00975"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a3b74acf6f315bb5fe36266bc9647ee97">  975</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#a3b74acf6f315bb5fe36266bc9647ee97">maxBufferedMessages</a>;</div>
<div class="line"><a name="l00981"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">  981</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;</div>
<div class="line"><a name="l00985"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#abe7fdbe18bfd3577a75d3b386d69406c">  985</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#abe7fdbe18bfd3577a75d3b386d69406c">allowDisconnectedSendAtAnyTime</a>;</div>
<div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;<span class="comment">         * When the maximum number of buffered messages is reached, delete the oldest rather than the newest.</span></div>
<div class="line"><a name="l00988"></a><span class="lineno">  988</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l00989"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a76de37b3cff885e83db204a347fe0a2d">  989</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#a76de37b3cff885e83db204a347fe0a2d">deleteOldestMessages</a>;</div>
<div class="line"><a name="l00990"></a><span class="lineno">  990</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;<span class="comment">         * Restore messages from persistence on create - or clear it.</span></div>
<div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l00993"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a231b8890c3bc2ea07f7c599896f30691">  993</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#a231b8890c3bc2ea07f7c599896f30691">restoreMessages</a>;</div>
<div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;<span class="comment">         * Persist QoS0 publish commands - an option to not persist them.</span></div>
<div class="line"><a name="l00996"></a><span class="lineno">  996</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l00997"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__create_options.html#a0c3ea2641e188542c787e71e2c521a0b">  997</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__create_options.html#a0c3ea2641e188542c787e71e2c521a0b">persistQoS0</a>;</div>
<div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;} <a class="code" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a>;</div>
<div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160; </div>
<div class="line"><a name="l01000"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5fedeafef4753f09b1bcb92773564786"> 1000</a></span>&#160;<span class="preprocessor">#define MQTTAsync_createOptions_initializer  { {&#39;M&#39;, &#39;Q&#39;, &#39;C&#39;, &#39;O&#39;}, 2, 0, 100, MQTTVERSION_DEFAULT, 0, 0, 1, 1}</span></div>
<div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160; </div>
<div class="line"><a name="l01002"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a0008776a46e7268ccbef4774ce3d4579"> 1002</a></span>&#160;<span class="preprocessor">#define MQTTAsync_createOptions_initializer5 { {&#39;M&#39;, &#39;Q&#39;, &#39;C&#39;, &#39;O&#39;}, 2, 0, 100, MQTTVERSION_5, 0, 0, 1, 1}</span></div>
<div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160; </div>
<div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160; </div>
<div class="line"><a name="l01005"></a><span class="lineno"> 1005</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync_createWithOptions</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>* handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* serverURI, <span class="keyword">const</span> <span class="keywordtype">char</span>* clientId,</div>
<div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;                <span class="keywordtype">int</span> persistence_type, <span class="keywordtype">void</span>* persistence_context, <a class="code" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a>* options);</div>
<div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160; </div>
<div class="line"><a name="l01020"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html"> 1020</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160;{</div>
<div class="line"><a name="l01023"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#aa5326df180cb23c59afbcab711a06479"> 1023</a></span>&#160;        <span class="keywordtype">char</span> <a class="code" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a name="l01027"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a0761a5e5be0383882e42924de8e51f82"> 1027</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__will_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l01029"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a0e20a7b350881d05108d6342884198a5"> 1029</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__will_options.html#a0e20a7b350881d05108d6342884198a5">topicName</a>;</div>
<div class="line"><a name="l01031"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a254bf0858da09c96a48daf64404eb4f8"> 1031</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__will_options.html#a254bf0858da09c96a48daf64404eb4f8">message</a>;</div>
<div class="line"><a name="l01035"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc"> 1035</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">retained</a>;</div>
<div class="line"><a name="l01040"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a35738099155a0e4f54050da474bab2e7"> 1040</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__will_options.html#a35738099155a0e4f54050da474bab2e7">qos</a>;</div>
<div class="line"><a name="l01042"></a><span class="lineno"> 1042</span>&#160;        <span class="keyword">struct</span></div>
<div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;        {</div>
<div class="line"><a name="l01044"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#afed088663f8704004425cdae2120b9b3"> 1044</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__will_options.html#afed088663f8704004425cdae2120b9b3">len</a>;            </div>
<div class="line"><a name="l01045"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__will_options.html#a0d49d74db4c035719c3867723cf7e779"> 1045</a></span>&#160;                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__will_options.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a name="l01046"></a><span class="lineno"> 1046</span>&#160;        } payload;</div>
<div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;} <a class="code" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a>;</div>
<div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160; </div>
<div class="line"><a name="l01049"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a6c45768e1b28844f2ac0f6ac68709730"> 1049</a></span>&#160;<span class="preprocessor">#define MQTTAsync_willOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;W&#39;}, 1, NULL, NULL, 0, 0, { 0, NULL } }</span></div>
<div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160; </div>
<div class="line"><a name="l01051"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2549ea897af26c76198284731db9e721"> 1051</a></span>&#160;<span class="preprocessor">#define MQTT_SSL_VERSION_DEFAULT 0</span></div>
<div class="line"><a name="l01052"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d"> 1052</a></span>&#160;<span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_0 1</span></div>
<div class="line"><a name="l01053"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#abdff87efa3f2ee473a1591e10638b537"> 1053</a></span>&#160;<span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_1 2</span></div>
<div class="line"><a name="l01054"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab"> 1054</a></span>&#160;<span class="preprocessor">#define MQTT_SSL_VERSION_TLS_1_2 3</span></div>
<div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160; </div>
<div class="line"><a name="l01068"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html"> 1068</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l01069"></a><span class="lineno"> 1069</span>&#160;{</div>
<div class="line"><a name="l01071"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#aa5326df180cb23c59afbcab711a06479"> 1071</a></span>&#160;        <span class="keywordtype">char</span> <a class="code" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160; </div>
<div class="line"><a name="l01080"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82"> 1080</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l01081"></a><span class="lineno"> 1081</span>&#160; </div>
<div class="line"><a name="l01083"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0"> 1083</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">trustStore</a>;</div>
<div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160; </div>
<div class="line"><a name="l01088"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a32b476382955289ce427112b59f21c3e"> 1088</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">keyStore</a>;</div>
<div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160; </div>
<div class="line"><a name="l01093"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4"> 1093</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">privateKey</a>;</div>
<div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160; </div>
<div class="line"><a name="l01096"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031"> 1096</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">privateKeyPassword</a>;</div>
<div class="line"><a name="l01097"></a><span class="lineno"> 1097</span>&#160; </div>
<div class="line"><a name="l01106"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab"> 1106</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">enabledCipherSuites</a>;</div>
<div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160; </div>
<div class="line"><a name="l01109"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be"> 1109</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">enableServerCertAuth</a>;</div>
<div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160; </div>
<div class="line"><a name="l01115"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45"> 1115</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">sslVersion</a>;</div>
<div class="line"><a name="l01116"></a><span class="lineno"> 1116</span>&#160; </div>
<div class="line"><a name="l01122"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce"> 1122</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">verify</a>;</div>
<div class="line"><a name="l01123"></a><span class="lineno"> 1123</span>&#160; </div>
<div class="line"><a name="l01129"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d"> 1129</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">CApath</a>;</div>
<div class="line"><a name="l01130"></a><span class="lineno"> 1130</span>&#160; </div>
<div class="line"><a name="l01135"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a76c7b40e2e258d2f898b53165ada2b70"> 1135</a></span>&#160;    int (*ssl_error_cb) (<span class="keyword">const</span> <span class="keywordtype">char</span> *str, <span class="keywordtype">size_t</span> len, <span class="keywordtype">void</span> *u);</div>
<div class="line"><a name="l01136"></a><span class="lineno"> 1136</span>&#160; </div>
<div class="line"><a name="l01141"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f"> 1141</a></span>&#160;    <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">ssl_error_context</a>;</div>
<div class="line"><a name="l01142"></a><span class="lineno"> 1142</span>&#160; </div>
<div class="line"><a name="l01148"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a83c1245259a491ea9daf872ce04b5e46"> 1148</a></span>&#160;        <span class="keywordtype">unsigned</span> int (*ssl_psk_cb) (<span class="keyword">const</span> <span class="keywordtype">char</span> *hint, <span class="keywordtype">char</span> *identity, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> max_identity_len, <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> *psk, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> max_psk_len, <span class="keywordtype">void</span> *u);</div>
<div class="line"><a name="l01149"></a><span class="lineno"> 1149</span>&#160; </div>
<div class="line"><a name="l01154"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f"> 1154</a></span>&#160;        <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">ssl_psk_context</a>;</div>
<div class="line"><a name="l01155"></a><span class="lineno"> 1155</span>&#160; </div>
<div class="line"><a name="l01161"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b"> 1161</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">disableDefaultTrustStore</a>;</div>
<div class="line"><a name="l01162"></a><span class="lineno"> 1162</span>&#160; </div>
<div class="line"><a name="l01170"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5"> 1170</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> *<a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">protos</a>;</div>
<div class="line"><a name="l01171"></a><span class="lineno"> 1171</span>&#160; </div>
<div class="line"><a name="l01176"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42"> 1176</a></span>&#160;        <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">protos_len</a>;</div>
<div class="line"><a name="l01177"></a><span class="lineno"> 1177</span>&#160;} <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a>;</div>
<div class="line"><a name="l01178"></a><span class="lineno"> 1178</span>&#160; </div>
<div class="line"><a name="l01179"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aac935e2e9d770a53ee8189f128530511"> 1179</a></span>&#160;<span class="preprocessor">#define MQTTAsync_SSLOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;S&#39;}, 5, NULL, NULL, NULL, NULL, NULL, 1, MQTT_SSL_VERSION_DEFAULT, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }</span></div>
<div class="line"><a name="l01180"></a><span class="lineno"> 1180</span>&#160; </div>
<div class="line"><a name="l01182"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__name_value.html"> 1182</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l01183"></a><span class="lineno"> 1183</span>&#160;{</div>
<div class="line"><a name="l01184"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__name_value.html#a8f8f80d37794cde9472343e4487ba3eb"> 1184</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">name</a>; </div>
<div class="line"><a name="l01185"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__name_value.html#a8556878012feffc9e0beb86cd78f424d"> 1185</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__name_value.html#a8556878012feffc9e0beb86cd78f424d">value</a>; </div>
<div class="line"><a name="l01186"></a><span class="lineno"> 1186</span>&#160;} <a class="code" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>;</div>
<div class="line"><a name="l01187"></a><span class="lineno"> 1187</span>&#160; </div>
<div class="line"><a name="l01198"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html"> 1198</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l01199"></a><span class="lineno"> 1199</span>&#160;{</div>
<div class="line"><a name="l01201"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aa5326df180cb23c59afbcab711a06479"> 1201</a></span>&#160;        <span class="keywordtype">char</span> <a class="code" href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">struct_id</a>[4];</div>
<div class="line"><a name="l01212"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a0761a5e5be0383882e42924de8e51f82"> 1212</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l01223"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d"> 1223</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">keepAliveInterval</a>;</div>
<div class="line"><a name="l01245"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"> 1245</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">cleansession</a>;</div>
<div class="line"><a name="l01249"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a5c9d6c557453232a1b25cbbec5a31e8c"> 1249</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a5c9d6c557453232a1b25cbbec5a31e8c">maxInflight</a>;</div>
<div class="line"><a name="l01255"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a7a9c5105542460d6fd9323facca66648"> 1255</a></span>&#160;        <a class="code" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#a7a9c5105542460d6fd9323facca66648">will</a>;</div>
<div class="line"><a name="l01261"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043"> 1261</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">username</a>;</div>
<div class="line"><a name="l01267"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aa4a2ebcb494493f648ae1e6975672575"> 1267</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">password</a>;</div>
<div class="line"><a name="l01271"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a38c6aa24b36d981c49405db425c24db0"> 1271</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a38c6aa24b36d981c49405db425c24db0">connectTimeout</a>;</div>
<div class="line"><a name="l01279"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac73f57846c42bcaa9a47e6721a957748"> 1279</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">retryInterval</a>;</div>
<div class="line"><a name="l01284"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a86fd59846f3ba2082fd99906c6b496a6"> 1284</a></span>&#160;        <a class="code" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#a86fd59846f3ba2082fd99906c6b496a6">ssl</a>;</div>
<div class="line"><a name="l01290"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb"> 1290</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a>;</div>
<div class="line"><a name="l01296"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290"> 1296</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a>;</div>
<div class="line"><a name="l01302"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0"> 1302</a></span>&#160;        <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>;</div>
<div class="line"><a name="l01306"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aa82629005937abd92e97084a428cd61f"> 1306</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#aa82629005937abd92e97084a428cd61f">serverURIcount</a>;</div>
<div class="line"><a name="l01318"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#aba22d81c407fb2ba590dba476240d3e9"> 1318</a></span>&#160;        <span class="keywordtype">char</span>* <span class="keyword">const</span>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">serverURIs</a>;</div>
<div class="line"><a name="l01325"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240"> 1325</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTVersion</a>;</div>
<div class="line"><a name="l01329"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a7902ce4d11b96d8b19582bdd1f82b630"> 1329</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a7902ce4d11b96d8b19582bdd1f82b630">automaticReconnect</a>;</div>
<div class="line"><a name="l01333"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a166ac1b967f09326b0187f66be3e69af"> 1333</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a166ac1b967f09326b0187f66be3e69af">minRetryInterval</a>;</div>
<div class="line"><a name="l01337"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a035ba380dd97a284db04f4eaae5e113b"> 1337</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#a035ba380dd97a284db04f4eaae5e113b">maxRetryInterval</a>;</div>
<div class="line"><a name="l01341"></a><span class="lineno"> 1341</span>&#160;        <span class="keyword">struct </span>{</div>
<div class="line"><a name="l01342"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#afed088663f8704004425cdae2120b9b3"> 1342</a></span>&#160;                <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#afed088663f8704004425cdae2120b9b3">len</a>;            </div>
<div class="line"><a name="l01343"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a0d49d74db4c035719c3867723cf7e779"> 1343</a></span>&#160;                <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#a0d49d74db4c035719c3867723cf7e779">data</a>;  </div>
<div class="line"><a name="l01344"></a><span class="lineno"> 1344</span>&#160;        } binarypwd;</div>
<div class="line"><a name="l01345"></a><span class="lineno"> 1345</span>&#160;        <span class="comment">/*</span></div>
<div class="line"><a name="l01346"></a><span class="lineno"> 1346</span>&#160;<span class="comment">         * MQTT V5 clean start flag.  Only clears state at the beginning of the session.</span></div>
<div class="line"><a name="l01347"></a><span class="lineno"> 1347</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l01348"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#acdcb75a5d5981da027bce83849140f7b"> 1348</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html#acdcb75a5d5981da027bce83849140f7b">cleanstart</a>;</div>
<div class="line"><a name="l01352"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a9f8b7ffb4a698eb151a3b090548b82e8"> 1352</a></span>&#160;        <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *<a class="code" href="struct_m_q_t_t_async__connect_options.html#a9f8b7ffb4a698eb151a3b090548b82e8">connectProperties</a>;</div>
<div class="line"><a name="l01356"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac31f13e964ffb7e3696caef47ecc0641"> 1356</a></span>&#160;        <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *<a class="code" href="struct_m_q_t_t_async__connect_options.html#ac31f13e964ffb7e3696caef47ecc0641">willProperties</a>;</div>
<div class="line"><a name="l01362"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a1c23c490f06428725345de68a4ff0a3e"> 1362</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a>;</div>
<div class="line"><a name="l01368"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2"> 1368</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a>;</div>
<div class="line"><a name="l01372"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#ac4098248961a1ee89f40353eeebab58b"> 1372</a></span>&#160;        <span class="keyword">const</span> <a class="code" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#ac4098248961a1ee89f40353eeebab58b">httpHeaders</a>;</div>
<div class="line"><a name="l01376"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#add124780ab2de397a96780576c2f112c"> 1376</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#add124780ab2de397a96780576c2f112c">httpProxy</a>;</div>
<div class="line"><a name="l01380"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__connect_options.html#a388b78d8a75658928238f700f207ad92"> 1380</a></span>&#160;        <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="struct_m_q_t_t_async__connect_options.html#a388b78d8a75658928238f700f207ad92">httpsProxy</a>;</div>
<div class="line"><a name="l01381"></a><span class="lineno"> 1381</span>&#160;} <a class="code" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a>;</div>
<div class="line"><a name="l01382"></a><span class="lineno"> 1382</span>&#160; </div>
<div class="line"><a name="l01384"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492"> 1384</a></span>&#160;<span class="preprocessor">#define MQTTAsync_connectOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 60, 1, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a name="l01385"></a><span class="lineno"> 1385</span>&#160;<span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_DEFAULT, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
<div class="line"><a name="l01386"></a><span class="lineno"> 1386</span>&#160; </div>
<div class="line"><a name="l01388"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#abd403ce21f7aa0348ae1d3eefd031a5d"> 1388</a></span>&#160;<span class="preprocessor">#define MQTTAsync_connectOptions_initializer5 { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 60, 0, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a name="l01389"></a><span class="lineno"> 1389</span>&#160;<span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_5, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
<div class="line"><a name="l01390"></a><span class="lineno"> 1390</span>&#160; </div>
<div class="line"><a name="l01394"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a080951d916d7a58c4ceff8c6bacfe313"> 1394</a></span>&#160;<span class="preprocessor">#define MQTTAsync_connectOptions_initializer_ws { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 45, 1, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a name="l01395"></a><span class="lineno"> 1395</span>&#160;<span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_DEFAULT, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
<div class="line"><a name="l01396"></a><span class="lineno"> 1396</span>&#160; </div>
<div class="line"><a name="l01400"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a513bfbec7b7d39c827240db75aa4044b"> 1400</a></span>&#160;<span class="preprocessor">#define MQTTAsync_connectOptions_initializer5_ws { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;C&#39;}, 8, 45, 0, 65535, NULL, NULL, NULL, 30, 0,\</span></div>
<div class="line"><a name="l01401"></a><span class="lineno"> 1401</span>&#160;<span class="preprocessor">NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_5, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</span></div>
<div class="line"><a name="l01402"></a><span class="lineno"> 1402</span>&#160; </div>
<div class="line"><a name="l01403"></a><span class="lineno"> 1403</span>&#160; </div>
<div class="line"><a name="l01424"></a><span class="lineno"> 1424</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <a class="code" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a>* options);</div>
<div class="line"><a name="l01425"></a><span class="lineno"> 1425</span>&#160; </div>
<div class="line"><a name="l01427"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html"> 1427</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a name="l01428"></a><span class="lineno"> 1428</span>&#160;{</div>
<div class="line"><a name="l01430"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#aa5326df180cb23c59afbcab711a06479"> 1430</a></span>&#160;        <span class="keywordtype">char</span> struct_id[4];</div>
<div class="line"><a name="l01432"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a0761a5e5be0383882e42924de8e51f82"> 1432</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#a0761a5e5be0383882e42924de8e51f82">struct_version</a>;</div>
<div class="line"><a name="l01437"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a493b57f443cc38b3d3df9c1e584d9d82"> 1437</a></span>&#160;        <span class="keywordtype">int</span> <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#a493b57f443cc38b3d3df9c1e584d9d82">timeout</a>;</div>
<div class="line"><a name="l01443"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb"> 1443</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>* <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb">onSuccess</a>;</div>
<div class="line"><a name="l01449"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290"> 1449</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>* <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290">onFailure</a>;</div>
<div class="line"><a name="l01455"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0"> 1455</a></span>&#160;        <span class="keywordtype">void</span>* <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0">context</a>;</div>
<div class="line"><a name="l01459"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a1594008402f7307e4de8fa6131656dde"> 1459</a></span>&#160;        <a class="code" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#a1594008402f7307e4de8fa6131656dde">properties</a>;</div>
<div class="line"><a name="l01463"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a580d8a8ecb285f5a86c2a3865438f8ee"> 1463</a></span>&#160;        <span class="keyword">enum</span> <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode;</div>
<div class="line"><a name="l01469"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a1c23c490f06428725345de68a4ff0a3e"> 1469</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>* <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#a1c23c490f06428725345de68a4ff0a3e">onSuccess5</a>;</div>
<div class="line"><a name="l01475"></a><span class="lineno"><a class="line" href="struct_m_q_t_t_async__disconnect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2"> 1475</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>* <a class="code" href="struct_m_q_t_t_async__disconnect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">onFailure5</a>;</div>
<div class="line"><a name="l01476"></a><span class="lineno"> 1476</span>&#160;} <a class="code" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a>;</div>
<div class="line"><a name="l01477"></a><span class="lineno"> 1477</span>&#160; </div>
<div class="line"><a name="l01478"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707"> 1478</a></span>&#160;<span class="preprocessor">#define MQTTAsync_disconnectOptions_initializer { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;D&#39;}, 0, 0, NULL, NULL, NULL,\</span></div>
<div class="line"><a name="l01479"></a><span class="lineno"> 1479</span>&#160;<span class="preprocessor">        MQTTProperties_initializer, MQTTREASONCODE_SUCCESS, NULL, NULL }</span></div>
<div class="line"><a name="l01480"></a><span class="lineno"> 1480</span>&#160; </div>
<div class="line"><a name="l01481"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#aaa278001953dc129003eff83c8e7b3db"> 1481</a></span>&#160;<span class="preprocessor">#define MQTTAsync_disconnectOptions_initializer5 { {&#39;M&#39;, &#39;Q&#39;, &#39;T&#39;, &#39;D&#39;}, 1, 0, NULL, NULL, NULL,\</span></div>
<div class="line"><a name="l01482"></a><span class="lineno"> 1482</span>&#160;<span class="preprocessor">        MQTTProperties_initializer, MQTTREASONCODE_SUCCESS, NULL, NULL }</span></div>
<div class="line"><a name="l01483"></a><span class="lineno"> 1483</span>&#160; </div>
<div class="line"><a name="l01502"></a><span class="lineno"> 1502</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <a class="code" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a>* options);</div>
<div class="line"><a name="l01503"></a><span class="lineno"> 1503</span>&#160; </div>
<div class="line"><a name="l01504"></a><span class="lineno"> 1504</span>&#160; </div>
<div class="line"><a name="l01512"></a><span class="lineno"> 1512</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457">MQTTAsync_isConnected</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle);</div>
<div class="line"><a name="l01513"></a><span class="lineno"> 1513</span>&#160; </div>
<div class="line"><a name="l01514"></a><span class="lineno"> 1514</span>&#160; </div>
<div class="line"><a name="l01529"></a><span class="lineno"> 1529</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync_subscribe</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic, <span class="keywordtype">int</span> qos, <a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a name="l01530"></a><span class="lineno"> 1530</span>&#160; </div>
<div class="line"><a name="l01531"></a><span class="lineno"> 1531</span>&#160; </div>
<div class="line"><a name="l01549"></a><span class="lineno"> 1549</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync_subscribeMany</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic, <span class="keyword">const</span> <span class="keywordtype">int</span>* qos, <a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a name="l01550"></a><span class="lineno"> 1550</span>&#160; </div>
<div class="line"><a name="l01563"></a><span class="lineno"> 1563</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab">MQTTAsync_unsubscribe</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* topic, <a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a name="l01564"></a><span class="lineno"> 1564</span>&#160; </div>
<div class="line"><a name="l01577"></a><span class="lineno"> 1577</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311">MQTTAsync_unsubscribeMany</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keywordtype">int</span> count, <span class="keywordtype">char</span>* <span class="keyword">const</span>* topic, <a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a name="l01578"></a><span class="lineno"> 1578</span>&#160; </div>
<div class="line"><a name="l01579"></a><span class="lineno"> 1579</span>&#160; </div>
<div class="line"><a name="l01599"></a><span class="lineno"> 1599</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync_send</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* destinationName, <span class="keywordtype">int</span> payloadlen, <span class="keyword">const</span> <span class="keywordtype">void</span>* payload, <span class="keywordtype">int</span> qos,</div>
<div class="line"><a name="l01600"></a><span class="lineno"> 1600</span>&#160;                <span class="keywordtype">int</span> retained, <a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a name="l01601"></a><span class="lineno"> 1601</span>&#160; </div>
<div class="line"><a name="l01618"></a><span class="lineno"> 1618</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <span class="keyword">const</span> <span class="keywordtype">char</span>* destinationName, <span class="keyword">const</span> <a class="code" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>* msg, <a class="code" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>* response);</div>
<div class="line"><a name="l01619"></a><span class="lineno"> 1619</span>&#160; </div>
<div class="line"><a name="l01620"></a><span class="lineno"> 1620</span>&#160; </div>
<div class="line"><a name="l01639"></a><span class="lineno"> 1639</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync_getPendingTokens</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> **tokens);</div>
<div class="line"><a name="l01640"></a><span class="lineno"> 1640</span>&#160; </div>
<div class="line"><a name="l01649"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a61e6ee632e63312d382e2fcbe427f01a"> 1649</a></span>&#160;<span class="preprocessor">#define MQTTASYNC_TRUE 1</span></div>
<div class="line"><a name="l01650"></a><span class="lineno"> 1650</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4">MQTTAsync_isComplete</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token);</div>
<div class="line"><a name="l01651"></a><span class="lineno"> 1651</span>&#160; </div>
<div class="line"><a name="l01652"></a><span class="lineno"> 1652</span>&#160; </div>
<div class="line"><a name="l01665"></a><span class="lineno"> 1665</span>&#160;LIBMQTT_API <span class="keywordtype">int</span> <a class="code" href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync_waitForCompletion</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="code" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token, <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> timeout);</div>
<div class="line"><a name="l01666"></a><span class="lineno"> 1666</span>&#160; </div>
<div class="line"><a name="l01667"></a><span class="lineno"> 1667</span>&#160; </div>
<div class="line"><a name="l01678"></a><span class="lineno"> 1678</span>&#160;LIBMQTT_API <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync_freeMessage</a>(<a class="code" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a>** msg);</div>
<div class="line"><a name="l01679"></a><span class="lineno"> 1679</span>&#160; </div>
<div class="line"><a name="l01688"></a><span class="lineno"> 1688</span>&#160;LIBMQTT_API <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync_free</a>(<span class="keywordtype">void</span>* ptr);</div>
<div class="line"><a name="l01689"></a><span class="lineno"> 1689</span>&#160; </div>
<div class="line"><a name="l01697"></a><span class="lineno"> 1697</span>&#160;LIBMQTT_API <span class="keywordtype">void</span>* <a class="code" href="_m_q_t_t_async_8h.html#af5500ba58592afafaade2fcabdc61e61">MQTTAsync_malloc</a>(<span class="keywordtype">size_t</span> size);</div>
<div class="line"><a name="l01698"></a><span class="lineno"> 1698</span>&#160; </div>
<div class="line"><a name="l01706"></a><span class="lineno"> 1706</span>&#160;LIBMQTT_API <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy</a>(<a class="code" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>* handle);</div>
<div class="line"><a name="l01707"></a><span class="lineno"> 1707</span>&#160; </div>
<div class="line"><a name="l01708"></a><span class="lineno"> 1708</span>&#160; </div>
<div class="line"><a name="l01709"></a><span class="lineno"> 1709</span>&#160; </div>
<div class="line"><a name="l01710"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5"> 1710</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a></div>
<div class="line"><a name="l01711"></a><span class="lineno"> 1711</span>&#160;{</div>
<div class="line"><a name="l01712"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1"> 1712</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1">MQTTASYNC_TRACE_MAXIMUM</a> = 1,</div>
<div class="line"><a name="l01713"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce"> 1713</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce">MQTTASYNC_TRACE_MEDIUM</a>,</div>
<div class="line"><a name="l01714"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b"> 1714</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b">MQTTASYNC_TRACE_MINIMUM</a>,</div>
<div class="line"><a name="l01715"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e"> 1715</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e">MQTTASYNC_TRACE_PROTOCOL</a>,</div>
<div class="line"><a name="l01716"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8"> 1716</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8">MQTTASYNC_TRACE_ERROR</a>,</div>
<div class="line"><a name="l01717"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40"> 1717</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40">MQTTASYNC_TRACE_SEVERE</a>,</div>
<div class="line"><a name="l01718"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295"> 1718</a></span>&#160;        <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295">MQTTASYNC_TRACE_FATAL</a>,</div>
<div class="line"><a name="l01719"></a><span class="lineno"> 1719</span>&#160;};</div>
<div class="line"><a name="l01720"></a><span class="lineno"> 1720</span>&#160; </div>
<div class="line"><a name="l01721"></a><span class="lineno"> 1721</span>&#160; </div>
<div class="line"><a name="l01727"></a><span class="lineno"> 1727</span>&#160;LIBMQTT_API <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">MQTTAsync_setTraceLevel</a>(<span class="keyword">enum</span> <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> level);</div>
<div class="line"><a name="l01728"></a><span class="lineno"> 1728</span>&#160; </div>
<div class="line"><a name="l01729"></a><span class="lineno"> 1729</span>&#160; </div>
<div class="line"><a name="l01739"></a><span class="lineno"><a class="line" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02"> 1739</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a>(<span class="keyword">enum</span> <a class="code" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> level, <span class="keywordtype">char</span>* message);</div>
<div class="line"><a name="l01740"></a><span class="lineno"> 1740</span>&#160; </div>
<div class="line"><a name="l01747"></a><span class="lineno"> 1747</span>&#160;LIBMQTT_API <span class="keywordtype">void</span> <a class="code" href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388">MQTTAsync_setTraceCallback</a>(<a class="code" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a>* callback);</div>
<div class="line"><a name="l01748"></a><span class="lineno"> 1748</span>&#160; </div>
<div class="line"><a name="l01755"></a><span class="lineno"> 1755</span>&#160;LIBMQTT_API <a class="code" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>* <a class="code" href="_m_q_t_t_async_8h.html#a7cf29b785a1d4ff1de2e67e2f916b658">MQTTAsync_getVersionInfo</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a name="l01756"></a><span class="lineno"> 1756</span>&#160; </div>
<div class="line"><a name="l01763"></a><span class="lineno"> 1763</span>&#160;LIBMQTT_API <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="_m_q_t_t_async_8h.html#a875cd089a8b23eb3fd50c0406fc75d9f">MQTTAsync_strerror</a>(<span class="keywordtype">int</span> code);</div>
<div class="line"><a name="l01764"></a><span class="lineno"> 1764</span>&#160; </div>
<div class="line"><a name="l01765"></a><span class="lineno"> 1765</span>&#160; </div>
<div class="line"><a name="l02379"></a><span class="lineno"> 2379</span>&#160;<span class="preprocessor">#if defined(__cplusplus)</span></div>
<div class="line"><a name="l02380"></a><span class="lineno"> 2380</span>&#160;     }</div>
<div class="line"><a name="l02381"></a><span class="lineno"> 2381</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l02382"></a><span class="lineno"> 2382</span>&#160; </div>
<div class="line"><a name="l02383"></a><span class="lineno"> 2383</span>&#160;<span class="preprocessor">#endif</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="a_m_q_t_t_async_8h_html_a46c332245c379629ae11f457fc179457"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457">MQTTAsync_isConnected</a></div><div class="ttdeci">int MQTTAsync_isConnected(MQTTAsync handle)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a26f5d839c92f9772c2a5d05486277a42"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a26f5d839c92f9772c2a5d05486277a42">MQTTAsync_SSLOptions::protos_len</a></div><div class="ttdeci">unsigned int protos_len</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1176</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a4fe09cc9c976b1cf424e13765d6cd8c9"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync_waitForCompletion</a></div><div class="ttdeci">int MQTTAsync_waitForCompletion(MQTTAsync handle, MQTTAsync_token token, unsigned long timeout)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a82786d9ba5cae39873f378a48b36c23b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a82786d9ba5cae39873f378a48b36c23b">MQTTAsync_successData::qosList</a></div><div class="ttdeci">int * qosList</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:582</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac4098248961a1ee89f40353eeebab58b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac4098248961a1ee89f40353eeebab58b">MQTTAsync_connectOptions::httpHeaders</a></div><div class="ttdeci">const MQTTAsync_nameValue * httpHeaders</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1372</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_responseOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:732</div></div>
<div class="ttc" id="a_m_q_t_t_properties_8h_html"><div class="ttname"><a href="_m_q_t_t_properties_8h.html">MQTTProperties.h</a></div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a44baf2cb9a0bbcec3ed2eace43f832d1"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a44baf2cb9a0bbcec3ed2eace43f832d1">MQTTAsync_successData5::sessionPresent</a></div><div class="ttdeci">int sessionPresent</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:629</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a7ca6d2a1813f2bbd0bc3af2771e46ba4"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a></div><div class="ttdeci">int MQTTAsync_token</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:263</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_successData::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:593</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_abe7fdbe18bfd3577a75d3b386d69406c"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#abe7fdbe18bfd3577a75d3b386d69406c">MQTTAsync_createOptions::allowDisconnectedSendAtAnyTime</a></div><div class="ttdeci">int allowDisconnectedSendAtAnyTime</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:985</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_createOptions::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:981</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ada4dd26d23c8849c51e4ab8200339040"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">MQTTAsync_setDisconnected</a></div><div class="ttdeci">int MQTTAsync_setDisconnected(MQTTAsync handle, void *context, MQTTAsync_disconnected *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a78cbe1b851fea48001112f7ba9e4ea62"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync_createWithOptions</a></div><div class="ttdeci">int MQTTAsync_createWithOptions(MQTTAsync *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context, MQTTAsync_createOptions *options)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_createOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:969</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a7cf29b785a1d4ff1de2e67e2f916b658"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a7cf29b785a1d4ff1de2e67e2f916b658">MQTTAsync_getVersionInfo</a></div><div class="ttdeci">MQTTAsync_nameValue * MQTTAsync_getVersionInfo(void)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae10bd009934b3bb4a9f4abae7424a611"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync_subscribe</a></div><div class="ttdeci">int MQTTAsync_subscribe(MQTTAsync handle, const char *topic, int qos, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0db1d736cdc0c864fe41abb3afd605bd"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a></div><div class="ttdeci">void * MQTTAsync</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:253</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_add124780ab2de397a96780576c2f112c"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#add124780ab2de397a96780576c2f112c">MQTTAsync_connectOptions::httpProxy</a></div><div class="ttdeci">const char * httpProxy</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1376</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_connectOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1212</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a95309fdf27015b12bc4adf56306e557b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a95309fdf27015b12bc4adf56306e557b">MQTTAsync_successData::serverURI</a></div><div class="ttdeci">char * serverURI</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:592</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a44baf2cb9a0bbcec3ed2eace43f832d1"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a44baf2cb9a0bbcec3ed2eace43f832d1">MQTTAsync_successData::sessionPresent</a></div><div class="ttdeci">int sessionPresent</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:594</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__init__options_html_a5929146596391e2838ef95feb89776da"><div class="ttname"><a href="struct_m_q_t_t_async__init__options.html#a5929146596391e2838ef95feb89776da">MQTTAsync_init_options::do_openssl_init</a></div><div class="ttdeci">int do_openssl_init</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:238</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0b350581324a4ff0eaee71e7a6721388"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388">MQTTAsync_setTraceCallback</a></div><div class="ttdeci">void MQTTAsync_setTraceCallback(MQTTAsync_traceCallback *callback)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a44abc360051b918a39b0596a137775ae"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">MQTTAsync_setMessageArrivedCallback</a></div><div class="ttdeci">int MQTTAsync_setMessageArrivedCallback(MQTTAsync handle, void *context, MQTTAsync_messageArrived *ma)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:601</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ab10296618e266b3c02fd117d6616b15d"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a></div><div class="ttdeci">void MQTTAsync_deliveryComplete(void *context, MQTTAsync_token token)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:392</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ab207095cab6f9a48b52cdb593b8456f4"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4">MQTTAsync_isComplete</a></div><div class="ttdeci">int MQTTAsync_isComplete(MQTTAsync handle, MQTTAsync_token token)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_acdcb75a5d5981da027bce83849140f7b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#acdcb75a5d5981da027bce83849140f7b">MQTTAsync_connectOptions::cleanstart</a></div><div class="ttdeci">int cleanstart</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1348</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#af8f771e67d284379111151b003c0d810">MQTTAsync_successData5::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:606</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTAsync_message::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:314</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:547</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_a254bf0858da09c96a48daf64404eb4f8"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#a254bf0858da09c96a48daf64404eb4f8">MQTTAsync_failureData::message</a></div><div class="ttdeci">const char * message</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:542</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_SSLOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1080</div></div>
<div class="ttc" id="astruct_m_q_t_t_properties_html"><div class="ttname"><a href="struct_m_q_t_t_properties.html">MQTTProperties</a></div><div class="ttdef"><b>Definition:</b> MQTTProperties.h:112</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a032835d4c4a1c1e19b53c330a673a6e0"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a032835d4c4a1c1e19b53c330a673a6e0">MQTTAsync_SSLOptions::trustStore</a></div><div class="ttdeci">const char * trustStore</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1083</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_responseOptions::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:755</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a7902ce4d11b96d8b19582bdd1f82b630"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a7902ce4d11b96d8b19582bdd1f82b630">MQTTAsync_connectOptions::automaticReconnect</a></div><div class="ttdeci">int automaticReconnect</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1329</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a7dd436cbb916fba200595c3519f09ec4"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a7dd436cbb916fba200595c3519f09ec4">MQTTAsync_SSLOptions::privateKey</a></div><div class="ttdeci">const char * privateKey</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1093</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_successData5::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:604</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a6174c42da8c55c86e7255be2848dc4ac"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a6174c42da8c55c86e7255be2848dc4ac">MQTTAsync_message::msgid</a></div><div class="ttdeci">int msgid</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:327</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a45a5b7c00a796a23f01673cef1dbe0a9"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a45a5b7c00a796a23f01673cef1dbe0a9">MQTTAsync_failureData5::code</a></div><div class="ttdeci">int code</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:560</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aba22d81c407fb2ba590dba476240d3e9"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aba22d81c407fb2ba590dba476240d3e9">MQTTAsync_connectOptions::serverURIs</a></div><div class="ttdeci">char *const  * serverURIs</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1318</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_aa5326df180cb23c59afbcab711a06479"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#aa5326df180cb23c59afbcab711a06479">MQTTAsync_responseOptions::struct_id</a></div><div class="ttdeci">char struct_id[4]</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:711</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a38dfee9f038f473c95af46fcef5dd3e9"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a38dfee9f038f473c95af46fcef5dd3e9">MQTTAsync_failureData5::packet_type</a></div><div class="ttdeci">int packet_type</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:564</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_disconnectOptions::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1459</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae9ae8d61023e7029ef5a19f5219c3599"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks</a></div><div class="ttdeci">int MQTTAsync_setCallbacks(MQTTAsync handle, void *context, MQTTAsync_connectionLost *cl, MQTTAsync_messageArrived *ma, MQTTAsync_deliveryComplete *dc)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5687171e67e98f9ea590c9e3b64cde18"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage</a></div><div class="ttdeci">int MQTTAsync_sendMessage(MQTTAsync handle, const char *destinationName, const MQTTAsync_message *msg, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a7a9c5105542460d6fd9323facca66648"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a7a9c5105542460d6fd9323facca66648">MQTTAsync_connectOptions::will</a></div><div class="ttdeci">MQTTAsync_willOptions * will</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1255</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a3078b3c824cc9753a57898072445c34d"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a3078b3c824cc9753a57898072445c34d">MQTTAsync_SSLOptions::CApath</a></div><div class="ttdeci">const char * CApath</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1129</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#af8f771e67d284379111151b003c0d810">MQTTAsync_failureData::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:538</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a63c66a311ab16239a4175ff671871bf2"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync_send</a></div><div class="ttdeci">int MQTTAsync_send(MQTTAsync handle, const char *destinationName, int payloadlen, const void *payload, int qos, int retained, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1068</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_af5500ba58592afafaade2fcabdc61e61"><div class="ttname"><a href="_m_q_t_t_async_8h.html#af5500ba58592afafaade2fcabdc61e61">MQTTAsync_malloc</a></div><div class="ttdeci">void * MQTTAsync_malloc(size_t size)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_adc69afa4725f8321bdaa5a05aec5cfd5"><div class="ttname"><a href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a></div><div class="ttdeci">int MQTTAsync_disconnect(MQTTAsync handle, const MQTTAsync_disconnectOptions *options)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40">MQTTASYNC_TRACE_SEVERE</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_SEVERE</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1717</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ac78620b33434a187255bd1a3faec1578"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync_subscribeMany</a></div><div class="ttdeci">int MQTTAsync_subscribeMany(MQTTAsync handle, int count, char *const *topic, const int *qos, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac31f13e964ffb7e3696caef47ecc0641"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac31f13e964ffb7e3696caef47ecc0641">MQTTAsync_connectOptions::willProperties</a></div><div class="ttdeci">MQTTProperties * willProperties</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1356</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a1c23c490f06428725345de68a4ff0a3e"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a1c23c490f06428725345de68a4ff0a3e">MQTTAsync_connectOptions::onSuccess5</a></div><div class="ttdeci">MQTTAsync_onSuccess5 * onSuccess5</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1362</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_aa3cb44feb3ae6d11b3a4cad2d94cb33a"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#aa3cb44feb3ae6d11b3a4cad2d94cb33a">MQTTAsync_message::payloadlen</a></div><div class="ttdeci">int payloadlen</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:279</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a3543ea1481b68d73cdde833280bb9c45"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a3543ea1481b68d73cdde833280bb9c45">MQTTAsync_SSLOptions::sslVersion</a></div><div class="ttdeci">int sslVersion</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1115</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_successData5::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:628</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_aba2dfcdfda80edcb531a5a7115d3e043"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#aba2dfcdfda80edcb531a5a7115d3e043">MQTTAsync_connectData::username</a></div><div class="ttdeci">const char * username</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:477</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a32b476382955289ce427112b59f21c3e"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a32b476382955289ce427112b59f21c3e">MQTTAsync_SSLOptions::keyStore</a></div><div class="ttdeci">const char * keyStore</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1088</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ad5562f9dc71fbd93d25ad20b328cb887"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy</a></div><div class="ttdeci">void MQTTAsync_destroy(MQTTAsync *handle)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a69fd433ce1b9b6a1b3b453c4793a9311"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311">MQTTAsync_unsubscribeMany</a></div><div class="ttdeci">int MQTTAsync_unsubscribeMany(MQTTAsync handle, int count, char *const *topic, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aa82629005937abd92e97084a428cd61f"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aa82629005937abd92e97084a428cd61f">MQTTAsync_connectOptions::serverURIcount</a></div><div class="ttdeci">int serverURIcount</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1306</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a4dad726f2b6f79ca5847689c5f2f2ec2"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">MQTTAsync_disconnectOptions::onFailure5</a></div><div class="ttdeci">MQTTAsync_onFailure5 * onFailure5</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1475</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#afed088663f8704004425cdae2120b9b3">MQTTAsync_connectData::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:484</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a6ed8403758cecd2f762af6ba5e0ae525"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a6ed8403758cecd2f762af6ba5e0ae525">MQTTAsync_successData5::message</a></div><div class="ttdeci">MQTTAsync_message message</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:621</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a4dad726f2b6f79ca5847689c5f2f2ec2"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">MQTTAsync_connectOptions::onFailure5</a></div><div class="ttdeci">MQTTAsync_onFailure5 * onFailure5</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1368</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a38c6aa24b36d981c49405db425c24db0"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a38c6aa24b36d981c49405db425c24db0">MQTTAsync_connectOptions::connectTimeout</a></div><div class="ttdeci">int connectTimeout</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1271</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#af8f771e67d284379111151b003c0d810">MQTTAsync_failureData5::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:554</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ac7fbab13a0b2e5dd4ee11efbbb9f6a3a"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">MQTTAsync_setTraceLevel</a></div><div class="ttdeci">void MQTTAsync_setTraceLevel(enum MQTTASYNC_TRACE_LEVELS level)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a388b78d8a75658928238f700f207ad92"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a388b78d8a75658928238f700f207ad92">MQTTAsync_connectOptions::httpsProxy</a></div><div class="ttdeci">const char * httpsProxy</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1380</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a9f8b7ffb4a698eb151a3b090548b82e8"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a9f8b7ffb4a698eb151a3b090548b82e8">MQTTAsync_connectOptions::connectProperties</a></div><div class="ttdeci">MQTTProperties * connectProperties</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1352</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a035ba380dd97a284db04f4eaae5e113b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a035ba380dd97a284db04f4eaae5e113b">MQTTAsync_connectOptions::maxRetryInterval</a></div><div class="ttdeci">int maxRetryInterval</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1337</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:570</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a1002b09c62a096578c9b3e0135eb98c1"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">MQTTAsync_setBeforePersistenceWrite</a></div><div class="ttdeci">int MQTTAsync_setBeforePersistenceWrite(MQTTAsync handle, void *context, MQTTPersistence_beforeWrite *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_aee15bbd9224efd9dcce9b4ae491b2e2e"><div class="ttname"><a href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">MQTTAsync_setConnectionLostCallback</a></div><div class="ttdeci">int MQTTAsync_setConnectionLostCallback(MQTTAsync handle, void *context, MQTTAsync_connectionLost *cl)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_disconnectOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1443</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295">MQTTASYNC_TRACE_FATAL</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_FATAL</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1718</div></div>
<div class="ttc" id="a_m_q_t_t_subscribe_opts_8h_html"><div class="ttname"><a href="_m_q_t_t_subscribe_opts_8h.html">MQTTSubscribeOpts.h</a></div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a6060c25c2641e878803aef76fefb31ee"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a></div><div class="ttdeci">void MQTTAsync_onFailure(void *context, MQTTAsync_failureData *response)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:686</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a4dad726f2b6f79ca5847689c5f2f2ec2"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a4dad726f2b6f79ca5847689c5f2f2ec2">MQTTAsync_responseOptions::onFailure5</a></div><div class="ttdeci">MQTTAsync_onFailure5 * onFailure5</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:751</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_aa078aec3eba83481f63db3c3939a5da9"><div class="ttname"><a href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">MQTTAsync_setUpdateConnectOptions</a></div><div class="ttdeci">int MQTTAsync_setUpdateConnectOptions(MQTTAsync handle, void *context, MQTTAsync_updateConnectOptions *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a65aba1caeae9b5af5d5b6c5598a75b02"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a></div><div class="ttdeci">void MQTTAsync_traceCallback(enum MQTTASYNC_TRACE_LEVELS level, char *message)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1739</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_abc92f60743fc471643b473abbc987be0"><div class="ttname"><a href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync_getPendingTokens</a></div><div class="ttdeci">int MQTTAsync_getPendingTokens(MQTTAsync handle, MQTTAsync_token **tokens)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5e44304a2c011a7d61b72c779ad83979"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a></div><div class="ttdeci">int MQTTAsync_updateConnectOptions(void *context, MQTTAsync_connectData *data)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:497</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac73f57846c42bcaa9a47e6721a957748"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac73f57846c42bcaa9a47e6721a957748">MQTTAsync_connectOptions::retryInterval</a></div><div class="ttdeci">int retryInterval</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1279</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_disconnectOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1455</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a6ed8403758cecd2f762af6ba5e0ae525"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a6ed8403758cecd2f762af6ba5e0ae525">MQTTAsync_successData::message</a></div><div class="ttdeci">MQTTAsync_message message</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:586</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a166ac1b967f09326b0187f66be3e69af"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a166ac1b967f09326b0187f66be3e69af">MQTTAsync_connectOptions::minRetryInterval</a></div><div class="ttdeci">int minRetryInterval</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1333</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_failureData5::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:552</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:708</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:465</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_ab7f597518dd5b9db5a515081f8e0bd1f"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#ab7f597518dd5b9db5a515081f8e0bd1f">MQTTAsync_SSLOptions::ssl_psk_context</a></div><div class="ttdeci">void * ssl_psk_context</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1154</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_successData5::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:608</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1">MQTTASYNC_TRACE_MAXIMUM</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_MAXIMUM</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1712</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_ab865640a1cc53b68622004c5a2d29fae"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a></div><div class="ttdeci">int MQTTPersistence_beforeWrite(void *context, int bufcount, char *buffers[], int buflens[])</div><div class="ttdef"><b>Definition:</b> MQTTClientPersistence.h:264</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5462c4618d0a229116db5fbadacf95d2"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create</a></div><div class="ttdeci">int MQTTAsync_create(MQTTAsync *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a0e20a7b350881d05108d6342884198a5"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a0e20a7b350881d05108d6342884198a5">MQTTAsync_willOptions::topicName</a></div><div class="ttdeci">const char * topicName</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1029</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a3900a98d7b1d58ad6e686bfce298bb6c"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a></div><div class="ttdeci">void MQTTAsync_connectionLost(void *context, char *cause)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:412</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a1705e75a48999cb45bf85c15608478f5"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">MQTTAsync_global_init</a></div><div class="ttdeci">void MQTTAsync_global_init(MQTTAsync_init_options *inits)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__init__options_html"><div class="ttname"><a href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:231</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a8c5023e04d5c3e9805d5dae76df21f4c"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a></div><div class="ttdeci">void MQTTAsync_onFailure5(void *context, MQTTAsync_failureData5 *response)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:701</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac8dd0930672a9c7d71fc645aa1f0521d"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac8dd0930672a9c7d71fc645aa1f0521d">MQTTAsync_connectOptions::keepAliveInterval</a></div><div class="ttdeci">int keepAliveInterval</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1223</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_ae25f4a1d2a3fa952d052a965376d8fef"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#ae25f4a1d2a3fa952d052a965376d8fef">MQTTAsync_successData5::destinationName</a></div><div class="ttdeci">char * destinationName</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:622</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a1c23c490f06428725345de68a4ff0a3e"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a1c23c490f06428725345de68a4ff0a3e">MQTTAsync_disconnectOptions::onSuccess5</a></div><div class="ttdeci">MQTTAsync_onSuccess5 * onSuccess5</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1469</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_aa683926d52134077f27d6dc67bda13ab"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#aa683926d52134077f27d6dc67bda13ab">MQTTAsync_SSLOptions::enabledCipherSuites</a></div><div class="ttdeci">const char * enabledCipherSuites</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1106</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_message::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:295</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html">MQTTReasonCodes.h</a></div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_disconnectOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1432</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a08d18ece91c1b011011354570d8ac1ab"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab">MQTTAsync_unsubscribe</a></div><div class="ttdeci">int MQTTAsync_unsubscribe(MQTTAsync handle, const char *topic, MQTTAsync_responseOptions *response)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a52a1d9ab6e5d5064a3de42d0eec88f57"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a></div><div class="ttdeci">void MQTTAsync_disconnected(void *context, MQTTProperties *properties, enum MQTTReasonCodes reasonCode)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:444</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a3918ead59b56816a8d7544def184e48e"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a></div><div class="ttdeci">int MQTTAsync_messageArrived(void *context, char *topicName, int topicLen, MQTTAsync_message *message)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:369</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1020</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a9eff55064941fb604452abb0050ea99d"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a9eff55064941fb604452abb0050ea99d">MQTTAsync_message::payload</a></div><div class="ttdeci">void * payload</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:281</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b">MQTTASYNC_TRACE_MINIMUM</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_MINIMUM</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1714</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#af8f771e67d284379111151b003c0d810">MQTTAsync_responseOptions::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:739</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_abd3ea01869b89ff23f9522640479c395"><div class="ttname"><a href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">MQTTAsync_reconnect</a></div><div class="ttdeci">int MQTTAsync_reconnect(MQTTAsync handle)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTAsync_connectOptions::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1343</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#a0d49d74db4c035719c3867723cf7e779">MQTTAsync_connectData::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:485</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a></div><div class="ttdeci">MQTTASYNC_TRACE_LEVELS</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1710</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_willOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1027</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__name_value_html_a8f8f80d37794cde9472343e4487ba3eb"><div class="ttname"><a href="struct_m_q_t_t_async__name_value.html#a8f8f80d37794cde9472343e4487ba3eb">MQTTAsync_nameValue::name</a></div><div class="ttdeci">const char * name</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1184</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a94ec624ee22cc01d2ca58a9e646a2665"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">MQTTAsync_setDeliveryCompleteCallback</a></div><div class="ttdeci">int MQTTAsync_setDeliveryCompleteCallback(MQTTAsync handle, void *context, MQTTAsync_deliveryComplete *dc)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_ae25f4a1d2a3fa952d052a965376d8fef"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#ae25f4a1d2a3fa952d052a965376d8fef">MQTTAsync_successData::destinationName</a></div><div class="ttdeci">char * destinationName</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:587</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a0388b226a414b09fa733f6d65004ec32"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a></div><div class="ttdeci">int MQTTAsync_connect(MQTTAsync handle, const MQTTAsync_connectOptions *options)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a078cd68d8f896ce7eac0cc83d4486a2c"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a078cd68d8f896ce7eac0cc83d4486a2c">MQTTAsync_createOptions::sendWhileDisconnected</a></div><div class="ttdeci">int sendWhileDisconnected</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:971</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html"><div class="ttname"><a href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:271</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_responseOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:726</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a12d546fd0ccf4e1091b18e1b735c7240"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a12d546fd0ccf4e1091b18e1b735c7240">MQTTAsync_connectOptions::MQTTVersion</a></div><div class="ttdeci">int MQTTVersion</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1325</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a4f8661600fb8bacf031150f8dcd293a5"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a4f8661600fb8bacf031150f8dcd293a5">MQTTAsync_SSLOptions::protos</a></div><div class="ttdeci">const unsigned char * protos</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1170</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_data_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__connect_data.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_connectData::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:470</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aba2dfcdfda80edcb531a5a7115d3e043"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aba2dfcdfda80edcb531a5a7115d3e043">MQTTAsync_connectOptions::username</a></div><div class="ttdeci">const char * username</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1261</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a2b836f58612a2c4627e40ae848da190d"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync_free</a></div><div class="ttdeci">void MQTTAsync_free(void *ptr)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1198</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a1c23c490f06428725345de68a4ff0a3e"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a1c23c490f06428725345de68a4ff0a3e">MQTTAsync_responseOptions::onSuccess5</a></div><div class="ttdeci">MQTTAsync_onSuccess5 * onSuccess5</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:745</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_failureData5::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:558</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e">MQTTASYNC_TRACE_PROTOCOL</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_PROTOCOL</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1715</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a875cd089a8b23eb3fd50c0406fc75d9f"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a875cd089a8b23eb3fd50c0406fc75d9f">MQTTAsync_strerror</a></div><div class="ttdeci">const char * MQTTAsync_strerror(int code)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a94900629685d5ed08f66fd2931f573ce"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a94900629685d5ed08f66fd2931f573ce">MQTTAsync_SSLOptions::verify</a></div><div class="ttdeci">int verify</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1122</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a95309fdf27015b12bc4adf56306e557b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a95309fdf27015b12bc4adf56306e557b">MQTTAsync_successData5::serverURI</a></div><div class="ttdeci">char * serverURI</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:627</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__name_value_html"><div class="ttname"><a href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1182</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_abb427571ba37b51f6985f1a6906ca031"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#abb427571ba37b51f6985f1a6906ca031">MQTTAsync_SSLOptions::privateKeyPassword</a></div><div class="ttdeci">const char * privateKeyPassword</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1096</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:535</div></div>
<div class="ttc" id="astruct_m_q_t_t_subscribe__options_html"><div class="ttname"><a href="struct_m_q_t_t_subscribe__options.html">MQTTSubscribe_options</a></div><div class="ttdef"><b>Definition:</b> MQTTSubscribeOpts.h:21</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a0826fcae7c2816e04772c61542c6846b"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a0826fcae7c2816e04772c61542c6846b">MQTTAsync_SSLOptions::disableDefaultTrustStore</a></div><div class="ttdeci">int disableDefaultTrustStore</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1161</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a0d49d74db4c035719c3867723cf7e779"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a0d49d74db4c035719c3867723cf7e779">MQTTAsync_willOptions::data</a></div><div class="ttdeci">const void * data</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1045</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a5c9d6c557453232a1b25cbbec5a31e8c"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a5c9d6c557453232a1b25cbbec5a31e8c">MQTTAsync_connectOptions::maxInflight</a></div><div class="ttdeci">int maxInflight</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1249</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_responseOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:720</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a86fd59846f3ba2082fd99906c6b496a6"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a86fd59846f3ba2082fd99906c6b496a6">MQTTAsync_connectOptions::ssl</a></div><div class="ttdeci">MQTTAsync_SSLOptions * ssl</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1284</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ae1568d96d6418004cc79466c06f3d791"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">MQTTAsync_responseOptions</a></div><div class="ttdeci">struct MQTTAsync_responseOptions MQTTAsync_responseOptions</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a0c3ea2641e188542c787e71e2c521a0b"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a0c3ea2641e188542c787e71e2c521a0b">MQTTAsync_createOptions::persistQoS0</a></div><div class="ttdeci">int persistQoS0</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:997</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a6a4904c112507a43e7dc8495b62cc0fc"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a6a4904c112507a43e7dc8495b62cc0fc">MQTTAsync_willOptions::retained</a></div><div class="ttdeci">int retained</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1035</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a3b74acf6f315bb5fe36266bc9647ee97"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a3b74acf6f315bb5fe36266bc9647ee97">MQTTAsync_createOptions::maxBufferedMessages</a></div><div class="ttdeci">int maxBufferedMessages</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:975</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a76de37b3cff885e83db204a347fe0a2d"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a76de37b3cff885e83db204a347fe0a2d">MQTTAsync_createOptions::deleteOldestMessages</a></div><div class="ttdeci">int deleteOldestMessages</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:989</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_a2199c9d905dbfa279895cf8123c10f4f"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#a2199c9d905dbfa279895cf8123c10f4f">MQTTAsync_successData5::reasonCodes</a></div><div class="ttdeci">enum MQTTReasonCodes * reasonCodes</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:616</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_aa4a2ebcb494493f648ae1e6975672575"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#aa4a2ebcb494493f648ae1e6975672575">MQTTAsync_connectOptions::password</a></div><div class="ttdeci">const char * password</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1267</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data_html_a45a5b7c00a796a23f01673cef1dbe0a9"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data.html#a45a5b7c00a796a23f01673cef1dbe0a9">MQTTAsync_failureData::code</a></div><div class="ttdeci">int code</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:540</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a16a3cd2a8c69669e9ed6e420ccd9c517"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a16a3cd2a8c69669e9ed6e420ccd9c517">MQTTAsync_responseOptions::subscribeOptions</a></div><div class="ttdeci">MQTTSubscribe_options subscribeOptions</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:759</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a254bf0858da09c96a48daf64404eb4f8"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a254bf0858da09c96a48daf64404eb4f8">MQTTAsync_willOptions::message</a></div><div class="ttdeci">const char * message</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1031</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_disconnectOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1449</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__name_value_html_a8556878012feffc9e0beb86cd78f424d"><div class="ttname"><a href="struct_m_q_t_t_async__name_value.html#a8556878012feffc9e0beb86cd78f424d">MQTTAsync_nameValue::value</a></div><div class="ttdeci">const char * value</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1185</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a18cc19740d9b00c629dc53a4420ecf1f"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">MQTTAsync_setConnected</a></div><div class="ttdeci">int MQTTAsync_setConnected(MQTTAsync handle, void *context, MQTTAsync_connected *co)</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__failure_data5_html_a254bf0858da09c96a48daf64404eb4f8"><div class="ttname"><a href="struct_m_q_t_t_async__failure_data5.html#a254bf0858da09c96a48daf64404eb4f8">MQTTAsync_failureData5::message</a></div><div class="ttdeci">const char * message</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:562</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1427</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a98f71c5d03dc5ee86fd9dc0119ccb961"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a98f71c5d03dc5ee86fd9dc0119ccb961">MQTTAsync_responseOptions::subscribeOptionsList</a></div><div class="ttdeci">MQTTSubscribe_options * subscribeOptionsList</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:768</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html_af5a966a574c6ad7a35f1ebb7edd5c1c4"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a></div><div class="ttdeci">int MQTTPersistence_afterRead(void *context, char **buffer, int *buflen)</div><div class="ttdef"><b>Definition:</b> MQTTClientPersistence.h:275</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a1a4b9bb2780472ec7bb65d0df1bf5d26"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a1a4b9bb2780472ec7bb65d0df1bf5d26">MQTTAsync_responseOptions::subscribeOptionsCount</a></div><div class="ttdeci">int subscribeOptionsCount</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:764</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#afed088663f8704004425cdae2120b9b3">MQTTAsync_connectOptions::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1342</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8">MQTTASYNC_TRACE_ERROR</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_ERROR</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1716</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__init__options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__init__options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_init_options::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:236</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a34bb8d321e9d368780b5c832c058f223"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a></div><div class="ttdeci">void MQTTAsync_connected(void *context, char *cause)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:430</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a75f6c13b7634e15f96dd9f17db6cf0be"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a75f6c13b7634e15f96dd9f17db6cf0be">MQTTAsync_SSLOptions::enableServerCertAuth</a></div><div class="ttdeci">int enableServerCertAuth</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1109</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_ab4d16e3c57502be6a7d1b1d3bcc382f3"><div class="ttname"><a href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">MQTTAsync_setAfterPersistenceRead</a></div><div class="ttdeci">int MQTTAsync_setAfterPersistenceRead(MQTTAsync handle, void *context, MQTTPersistence_afterRead *co)</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a892cf122e6e8d8f6cd38c4c8efe8fb67"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a></div><div class="ttdeci">void MQTTAsync_onSuccess5(void *context, MQTTAsync_successData5 *response)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:671</div></div>
<div class="ttc" id="a_m_q_t_t_client_persistence_8h_html"><div class="ttname"><a href="_m_q_t_t_client_persistence_8h.html">MQTTClientPersistence.h</a></div><div class="ttdoc">This structure represents a persistent data store, used to store outbound and inbound messages,...</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data5_html_ac97316626bd4faa6b71277c221275f4b"><div class="ttname"><a href="struct_m_q_t_t_async__success_data5.html#ac97316626bd4faa6b71277c221275f4b">MQTTAsync_successData5::reasonCodeCount</a></div><div class="ttdeci">int reasonCodeCount</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:615</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_successData::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:579</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__disconnect_options_html_a493b57f443cc38b3d3df9c1e584d9d82"><div class="ttname"><a href="struct_m_q_t_t_async__disconnect_options.html#a493b57f443cc38b3d3df9c1e584d9d82">MQTTAsync_disconnectOptions::timeout</a></div><div class="ttdeci">int timeout</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1437</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_adc4cf3f551bb367858644559d69cfdf5"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#adc4cf3f551bb367858644559d69cfdf5">MQTTAsync_message::dup</a></div><div class="ttdeci">int dup</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:321</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a1594008402f7307e4de8fa6131656dde"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a1594008402f7307e4de8fa6131656dde">MQTTAsync_message::properties</a></div><div class="ttdeci">MQTTProperties properties</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:331</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__message_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__message.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_message::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:277</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_afed088663f8704004425cdae2120b9b3"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#afed088663f8704004425cdae2120b9b3">MQTTAsync_willOptions::len</a></div><div class="ttdeci">int len</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1044</div></div>
<div class="ttc" id="astruct_m_q_t_t_async___s_s_l_options_html_a189f11195f4d5a70024adffdb050885f"><div class="ttname"><a href="struct_m_q_t_t_async___s_s_l_options.html#a189f11195f4d5a70024adffdb050885f">MQTTAsync_SSLOptions::ssl_error_context</a></div><div class="ttdeci">void * ssl_error_context</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1141</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__response_options_html_a0761a5e5be0383882e42924de8e51f82"><div class="ttname"><a href="struct_m_q_t_t_async__response_options.html#a0761a5e5be0383882e42924de8e51f82">MQTTAsync_responseOptions::struct_version</a></div><div class="ttdeci">int struct_version</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:714</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__will_options_html_a35738099155a0e4f54050da474bab2e7"><div class="ttname"><a href="struct_m_q_t_t_async__will_options.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_willOptions::qos</a></div><div class="ttdeci">int qos</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1040</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__success_data_html_af8f771e67d284379111151b003c0d810"><div class="ttname"><a href="struct_m_q_t_t_async__success_data.html#af8f771e67d284379111151b003c0d810">MQTTAsync_successData::token</a></div><div class="ttdeci">MQTTAsync_token token</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:573</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a036c36a2a4d3a3ffae9ab4dd8b3e7f7b"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTAsync_connectOptions::cleansession</a></div><div class="ttdeci">int cleansession</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1245</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html_a231b8890c3bc2ea07f7c599896f30691"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html#a231b8890c3bc2ea07f7c599896f30691">MQTTAsync_createOptions::restoreMessages</a></div><div class="ttdeci">int restoreMessages</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:993</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce">MQTTASYNC_TRACE_MEDIUM</a></div><div class="ttdeci">@ MQTTASYNC_TRACE_MEDIUM</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1713</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a9b45db63052fe29ab1fad22d2a00c91c"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync_freeMessage</a></div><div class="ttdeci">void MQTTAsync_freeMessage(MQTTAsync_message **msg)</div></div>
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a></div><div class="ttdeci">MQTTReasonCodes</div><div class="ttdef"><b>Definition:</b> MQTTReasonCodes.h:23</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ae376f130b17d169ee51be68077a89ed0"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ae376f130b17d169ee51be68077a89ed0">MQTTAsync_connectOptions::context</a></div><div class="ttdeci">void * context</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1302</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a7b0c18a0e29e2ce73f3ea109bc32617b"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a></div><div class="ttdeci">void MQTTAsync_onSuccess(void *context, MQTTAsync_successData *response)</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:655</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_a09ce26d7cff24e14a6844eaae7b15290"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#a09ce26d7cff24e14a6844eaae7b15290">MQTTAsync_connectOptions::onFailure</a></div><div class="ttdeci">MQTTAsync_onFailure * onFailure</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1296</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__create_options_html"><div class="ttname"><a href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a></div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:960</div></div>
<div class="ttc" id="astruct_m_q_t_t_async__connect_options_html_ac13fb68f736854fcab131b34756bfceb"><div class="ttname"><a href="struct_m_q_t_t_async__connect_options.html#ac13fb68f736854fcab131b34756bfceb">MQTTAsync_connectOptions::onSuccess</a></div><div class="ttdeci">MQTTAsync_onSuccess * onSuccess</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:1290</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:45 for Paho Asynchronous MQTT C Client Library by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
