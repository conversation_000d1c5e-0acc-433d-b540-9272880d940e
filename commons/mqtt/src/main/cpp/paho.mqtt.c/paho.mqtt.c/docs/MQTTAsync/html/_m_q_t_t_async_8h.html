<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTAsync.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">MQTTAsync.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;MQTTExportDeclarations.h&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_properties_8h_source.html">MQTTProperties.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_reason_codes_8h_source.html">MQTTReasonCodes.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_subscribe_opts_8h_source.html">MQTTSubscribeOpts.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="_m_q_t_t_client_persistence_8h_source.html">MQTTClientPersistence.h</a>&quot;</code><br />
</div>
<p><a href="_m_q_t_t_async_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__will_options.html">MQTTAsync_willOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async___s_s_l_options.html">MQTTAsync_SSLOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:afe0cffcce8efe25186f79c51ac44e16f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:afe0cffcce8efe25186f79c51ac44e16f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c8230fef85fc04b8a1035501f3be406"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a>&#160;&#160;&#160;-1</td></tr>
<tr class="separator:a7c8230fef85fc04b8a1035501f3be406"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4edf1249c75abd4975fec8ddeae2cdc9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a4edf1249c75abd4975fec8ddeae2cdc9">MQTTASYNC_PERSISTENCE_ERROR</a>&#160;&#160;&#160;-2</td></tr>
<tr class="separator:a4edf1249c75abd4975fec8ddeae2cdc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c0f30b329bc770145c2f04b3929df6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a66c0f30b329bc770145c2f04b3929df6">MQTTASYNC_DISCONNECTED</a>&#160;&#160;&#160;-3</td></tr>
<tr class="separator:a66c0f30b329bc770145c2f04b3929df6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad577286d43c72fbc49818aac42f4e24a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ad577286d43c72fbc49818aac42f4e24a">MQTTASYNC_MAX_MESSAGES_INFLIGHT</a>&#160;&#160;&#160;-4</td></tr>
<tr class="separator:ad577286d43c72fbc49818aac42f4e24a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80cbe091930c11b67ca719b3e385aa26"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a80cbe091930c11b67ca719b3e385aa26">MQTTASYNC_BAD_UTF8_STRING</a>&#160;&#160;&#160;-5</td></tr>
<tr class="separator:a80cbe091930c11b67ca719b3e385aa26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab88e1ebcee991099a72429e52a8253fd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ab88e1ebcee991099a72429e52a8253fd">MQTTASYNC_NULL_PARAMETER</a>&#160;&#160;&#160;-6</td></tr>
<tr class="separator:ab88e1ebcee991099a72429e52a8253fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77a7106d97ff60be3fe70f90b1867800"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a77a7106d97ff60be3fe70f90b1867800">MQTTASYNC_TOPICNAME_TRUNCATED</a>&#160;&#160;&#160;-7</td></tr>
<tr class="separator:a77a7106d97ff60be3fe70f90b1867800"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a241fc8db46dca132d591bc2be92247ba"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a241fc8db46dca132d591bc2be92247ba">MQTTASYNC_BAD_STRUCTURE</a>&#160;&#160;&#160;-8</td></tr>
<tr class="separator:a241fc8db46dca132d591bc2be92247ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64d111778ce4e0d3a62808f6db11f224"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a64d111778ce4e0d3a62808f6db11f224">MQTTASYNC_BAD_QOS</a>&#160;&#160;&#160;-9</td></tr>
<tr class="separator:a64d111778ce4e0d3a62808f6db11f224"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0f54d0bae2c74849022a8009e5d6ff7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ab0f54d0bae2c74849022a8009e5d6ff7">MQTTASYNC_NO_MORE_MSGIDS</a>&#160;&#160;&#160;-10</td></tr>
<tr class="separator:ab0f54d0bae2c74849022a8009e5d6ff7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee1b79d0632bec0fe49eb7ea1abd3b2e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#aee1b79d0632bec0fe49eb7ea1abd3b2e">MQTTASYNC_OPERATION_INCOMPLETE</a>&#160;&#160;&#160;-11</td></tr>
<tr class="separator:aee1b79d0632bec0fe49eb7ea1abd3b2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e338072cfd5291b579e4f0c99a6e773"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a4e338072cfd5291b579e4f0c99a6e773">MQTTASYNC_MAX_BUFFERED_MESSAGES</a>&#160;&#160;&#160;-12</td></tr>
<tr class="separator:a4e338072cfd5291b579e4f0c99a6e773"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6992c00553db1608aef9e162c161d73c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a6992c00553db1608aef9e162c161d73c">MQTTASYNC_SSL_NOT_SUPPORTED</a>&#160;&#160;&#160;-13</td></tr>
<tr class="separator:a6992c00553db1608aef9e162c161d73c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a785250cd4a1938ffeeff67b3538abfba"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a785250cd4a1938ffeeff67b3538abfba">MQTTASYNC_BAD_PROTOCOL</a>&#160;&#160;&#160;-14</td></tr>
<tr class="separator:a785250cd4a1938ffeeff67b3538abfba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6f97562573876867ba77460a51ca1d1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#af6f97562573876867ba77460a51ca1d1">MQTTASYNC_BAD_MQTT_OPTION</a>&#160;&#160;&#160;-15</td></tr>
<tr class="separator:af6f97562573876867ba77460a51ca1d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5df806e9767e1e3182fe089a8ee551b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#af5df806e9767e1e3182fe089a8ee551b">MQTTASYNC_WRONG_MQTT_VERSION</a>&#160;&#160;&#160;-16</td></tr>
<tr class="separator:af5df806e9767e1e3182fe089a8ee551b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47b3aed75983f48a503e1cad6c862004"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a47b3aed75983f48a503e1cad6c862004">MQTTASYNC_0_LEN_WILL_TOPIC</a>&#160;&#160;&#160;-17</td></tr>
<tr class="separator:a47b3aed75983f48a503e1cad6c862004"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8278cf4b50dd818c31fa12e45f074b5c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a8278cf4b50dd818c31fa12e45f074b5c">MQTTASYNC_COMMAND_IGNORED</a>&#160;&#160;&#160;-18</td></tr>
<tr class="separator:a8278cf4b50dd818c31fa12e45f074b5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2efee8e190e2c3690c680bde060f78ab"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a2efee8e190e2c3690c680bde060f78ab">MQTTASYNC_MAX_BUFFERED</a>&#160;&#160;&#160;-19</td></tr>
<tr class="separator:a2efee8e190e2c3690c680bde060f78ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75b80b01f98d5a1ffa2a4d42995a8397"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a75b80b01f98d5a1ffa2a4d42995a8397"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4603b988e76872e1f23f135d225ce2fb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a4603b988e76872e1f23f135d225ce2fb">MQTTVERSION_3_1</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:a4603b988e76872e1f23f135d225ce2fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac79cc6fdeaa9e3f4ee12c3418898b1ef"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">MQTTVERSION_3_1_1</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:ac79cc6fdeaa9e3f4ee12c3418898b1ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8b176fa4d5b89789767ce972338e1e3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:af8b176fa4d5b89789767ce972338e1e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade337b363b7f4bc7c1a7b2858e0380bd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">MQTT_BAD_SUBSCRIBE</a>&#160;&#160;&#160;0x80</td></tr>
<tr class="separator:ade337b363b7f4bc7c1a7b2858e0380bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a866e023f70141969d48597930c0ee313"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a866e023f70141969d48597930c0ee313">MQTTAsync_init_options_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'G'}, 0, 0 }</td></tr>
<tr class="separator:a866e023f70141969d48597930c0ee313"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a85061dadab532f28e96e5ab3c600e9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a6a85061dadab532f28e96e5ab3c600e9">MQTTAsync_message_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'M'}, 1, 0, NULL, 0, 0, 0, 0, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a> }</td></tr>
<tr class="separator:a6a85061dadab532f28e96e5ab3c600e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e415e68016ae56f6bbbbdc9840a9c6e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a2e415e68016ae56f6bbbbdc9840a9c6e">MQTTAsync_connectData_initializer</a>&#160;&#160;&#160;{{'M', 'Q', 'C', 'D'}, 0, NULL, {0, NULL}}</td></tr>
<tr class="separator:a2e415e68016ae56f6bbbbdc9840a9c6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53ce2002ae2c2579575bb41c48c51c29"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a53ce2002ae2c2579575bb41c48c51c29">MQTTAsync_failureData5_initializer</a>&#160;&#160;&#160;{{'M', 'Q', 'F', 'D'}, 0, 0, <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a>, 0, NULL, 0}</td></tr>
<tr class="separator:a53ce2002ae2c2579575bb41c48c51c29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6182ec90ec4a134465f627b324ac5a41"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a6182ec90ec4a134465f627b324ac5a41">MQTTAsync_successData5_initializer</a>&#160;&#160;&#160;{{'M', 'Q', 'S', 'D'}, 0, 0, <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a>, {.sub={0,0}}}</td></tr>
<tr class="separator:a6182ec90ec4a134465f627b324ac5a41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f8b408243b5c2369bc9758f2edf0878"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">MQTTAsync_responseOptions_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'R'}, 1, NULL, NULL, 0, 0, NULL, NULL, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a>, <a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aec3b45fd0367106eea344396f87cfda7">MQTTSubscribe_options_initializer</a>, 0, NULL}</td></tr>
<tr class="separator:a3f8b408243b5c2369bc9758f2edf0878"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a570185766fc8a9da410a6f84915b6df5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a570185766fc8a9da410a6f84915b6df5">MQTTAsync_callOptions_initializer</a>&#160;&#160;&#160;<a class="el" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">MQTTAsync_responseOptions_initializer</a></td></tr>
<tr class="separator:a570185766fc8a9da410a6f84915b6df5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5fedeafef4753f09b1bcb92773564786"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a5fedeafef4753f09b1bcb92773564786">MQTTAsync_createOptions_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'C', 'O'}, 2, 0, 100, <a class="el" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>, 0, 0, 1, 1}</td></tr>
<tr class="separator:a5fedeafef4753f09b1bcb92773564786"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0008776a46e7268ccbef4774ce3d4579"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a0008776a46e7268ccbef4774ce3d4579">MQTTAsync_createOptions_initializer5</a>&#160;&#160;&#160;{ {'M', 'Q', 'C', 'O'}, 2, 0, 100, <a class="el" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>, 0, 0, 1, 1}</td></tr>
<tr class="separator:a0008776a46e7268ccbef4774ce3d4579"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c45768e1b28844f2ac0f6ac68709730"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a6c45768e1b28844f2ac0f6ac68709730">MQTTAsync_willOptions_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'W'}, 1, NULL, NULL, 0, 0, { 0, NULL } }</td></tr>
<tr class="separator:a6c45768e1b28844f2ac0f6ac68709730"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2549ea897af26c76198284731db9e721"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a2549ea897af26c76198284731db9e721">MQTT_SSL_VERSION_DEFAULT</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a2549ea897af26c76198284731db9e721"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e5da3d6f0d2b53409bbfcf6e56f3d2d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">MQTT_SSL_VERSION_TLS_1_0</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:a7e5da3d6f0d2b53409bbfcf6e56f3d2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdff87efa3f2ee473a1591e10638b537"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#abdff87efa3f2ee473a1591e10638b537">MQTT_SSL_VERSION_TLS_1_1</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:abdff87efa3f2ee473a1591e10638b537"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a94dbdeafbb73c73a068e7c2085fbab"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab">MQTT_SSL_VERSION_TLS_1_2</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:a3a94dbdeafbb73c73a068e7c2085fbab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac935e2e9d770a53ee8189f128530511"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#aac935e2e9d770a53ee8189f128530511">MQTTAsync_SSLOptions_initializer</a>&#160;&#160;&#160;{ {'M', 'Q', 'T', 'S'}, 5, NULL, NULL, NULL, NULL, NULL, 1, <a class="el" href="_m_q_t_t_async_8h.html#a2549ea897af26c76198284731db9e721">MQTT_SSL_VERSION_DEFAULT</a>, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }</td></tr>
<tr class="separator:aac935e2e9d770a53ee8189f128530511"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae18b51f22784a43803eb809d6a0c2492"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492">MQTTAsync_connectOptions_initializer</a></td></tr>
<tr class="separator:ae18b51f22784a43803eb809d6a0c2492"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd403ce21f7aa0348ae1d3eefd031a5d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#abd403ce21f7aa0348ae1d3eefd031a5d">MQTTAsync_connectOptions_initializer5</a></td></tr>
<tr class="separator:abd403ce21f7aa0348ae1d3eefd031a5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a080951d916d7a58c4ceff8c6bacfe313"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a080951d916d7a58c4ceff8c6bacfe313">MQTTAsync_connectOptions_initializer_ws</a></td></tr>
<tr class="separator:a080951d916d7a58c4ceff8c6bacfe313"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a513bfbec7b7d39c827240db75aa4044b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a513bfbec7b7d39c827240db75aa4044b">MQTTAsync_connectOptions_initializer5_ws</a></td></tr>
<tr class="separator:a513bfbec7b7d39c827240db75aa4044b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fd5d6df31928ae468f3f2e522b9c707"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707">MQTTAsync_disconnectOptions_initializer</a></td></tr>
<tr class="separator:a2fd5d6df31928ae468f3f2e522b9c707"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa278001953dc129003eff83c8e7b3db"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#aaa278001953dc129003eff83c8e7b3db">MQTTAsync_disconnectOptions_initializer5</a></td></tr>
<tr class="separator:aaa278001953dc129003eff83c8e7b3db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61e6ee632e63312d382e2fcbe427f01a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a61e6ee632e63312d382e2fcbe427f01a">MQTTASYNC_TRUE</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:a61e6ee632e63312d382e2fcbe427f01a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a0db1d736cdc0c864fe41abb3afd605bd"><td class="memItemLeft" align="right" valign="top">typedef void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a></td></tr>
<tr class="separator:a0db1d736cdc0c864fe41abb3afd605bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ca6d2a1813f2bbd0bc3af2771e46ba4"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a></td></tr>
<tr class="separator:a7ca6d2a1813f2bbd0bc3af2771e46ba4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3918ead59b56816a8d7544def184e48e"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a>(void *context, char *topicName, int topicLen, <a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> *message)</td></tr>
<tr class="separator:a3918ead59b56816a8d7544def184e48e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab10296618e266b3c02fd117d6616b15d"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a>(void *context, <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token)</td></tr>
<tr class="separator:ab10296618e266b3c02fd117d6616b15d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3900a98d7b1d58ad6e686bfce298bb6c"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a>(void *context, char *cause)</td></tr>
<tr class="separator:a3900a98d7b1d58ad6e686bfce298bb6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34bb8d321e9d368780b5c832c058f223"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a>(void *context, char *cause)</td></tr>
<tr class="separator:a34bb8d321e9d368780b5c832c058f223"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52a1d9ab6e5d5064a3de42d0eec88f57"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a>(void *context, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode)</td></tr>
<tr class="separator:a52a1d9ab6e5d5064a3de42d0eec88f57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e44304a2c011a7d61b72c779ad83979"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a>(void *context, <a class="el" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a> *data)</td></tr>
<tr class="separator:a5e44304a2c011a7d61b72c779ad83979"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b0c18a0e29e2ce73f3ea109bc32617b"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>(void *context, <a class="el" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a> *response)</td></tr>
<tr class="separator:a7b0c18a0e29e2ce73f3ea109bc32617b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a892cf122e6e8d8f6cd38c4c8efe8fb67"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync_onSuccess5</a>(void *context, <a class="el" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a> *response)</td></tr>
<tr class="separator:a892cf122e6e8d8f6cd38c4c8efe8fb67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6060c25c2641e878803aef76fefb31ee"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>(void *context, <a class="el" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a> *response)</td></tr>
<tr class="separator:a6060c25c2641e878803aef76fefb31ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c5023e04d5c3e9805d5dae76df21f4c"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync_onFailure5</a>(void *context, <a class="el" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a> *response)</td></tr>
<tr class="separator:a8c5023e04d5c3e9805d5dae76df21f4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae1568d96d6418004cc79466c06f3d791"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">MQTTAsync_responseOptions</a></td></tr>
<tr class="separator:ae1568d96d6418004cc79466c06f3d791"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6bfa6beae93c259220e1a131ba1cf9c"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">MQTTAsync_callOptions</a></td></tr>
<tr class="separator:ab6bfa6beae93c259220e1a131ba1cf9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65aba1caeae9b5af5d5b6c5598a75b02"><td class="memItemLeft" align="right" valign="top">typedef void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a>(enum <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> level, char *message)</td></tr>
<tr class="separator:a65aba1caeae9b5af5d5b6c5598a75b02"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a5de816f986b318947709a34e0787eda5"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> { <br />
&#160;&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1">MQTTASYNC_TRACE_MAXIMUM</a> = 1, 
<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce">MQTTASYNC_TRACE_MEDIUM</a>, 
<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b">MQTTASYNC_TRACE_MINIMUM</a>, 
<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e">MQTTASYNC_TRACE_PROTOCOL</a>, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8">MQTTASYNC_TRACE_ERROR</a>, 
<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40">MQTTASYNC_TRACE_SEVERE</a>, 
<a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295">MQTTASYNC_TRACE_FATAL</a>
<br />
 }</td></tr>
<tr class="separator:a5de816f986b318947709a34e0787eda5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a1705e75a48999cb45bf85c15608478f5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">MQTTAsync_global_init</a> (<a class="el" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a> *inits)</td></tr>
<tr class="separator:a1705e75a48999cb45bf85c15608478f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada4dd26d23c8849c51e4ab8200339040"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">MQTTAsync_setDisconnected</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a> *co)</td></tr>
<tr class="separator:ada4dd26d23c8849c51e4ab8200339040"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa078aec3eba83481f63db3c3939a5da9"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">MQTTAsync_setUpdateConnectOptions</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a> *co)</td></tr>
<tr class="separator:aa078aec3eba83481f63db3c3939a5da9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1002b09c62a096578c9b3e0135eb98c1"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">MQTTAsync_setBeforePersistenceWrite</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a> *co)</td></tr>
<tr class="separator:a1002b09c62a096578c9b3e0135eb98c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4d16e3c57502be6a7d1b1d3bcc382f3"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">MQTTAsync_setAfterPersistenceRead</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a> *co)</td></tr>
<tr class="separator:ab4d16e3c57502be6a7d1b1d3bcc382f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9ae8d61023e7029ef5a19f5219c3599"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a> *cl, <a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a> *ma, <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a> *dc)</td></tr>
<tr class="separator:ae9ae8d61023e7029ef5a19f5219c3599"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee15bbd9224efd9dcce9b4ae491b2e2e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">MQTTAsync_setConnectionLostCallback</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a> *cl)</td></tr>
<tr class="separator:aee15bbd9224efd9dcce9b4ae491b2e2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44abc360051b918a39b0596a137775ae"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">MQTTAsync_setMessageArrivedCallback</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a> *ma)</td></tr>
<tr class="separator:a44abc360051b918a39b0596a137775ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94ec624ee22cc01d2ca58a9e646a2665"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">MQTTAsync_setDeliveryCompleteCallback</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a> *dc)</td></tr>
<tr class="separator:a94ec624ee22cc01d2ca58a9e646a2665"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18cc19740d9b00c629dc53a4420ecf1f"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">MQTTAsync_setConnected</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, void *context, <a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a> *co)</td></tr>
<tr class="separator:a18cc19740d9b00c629dc53a4420ecf1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd3ea01869b89ff23f9522640479c395"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">MQTTAsync_reconnect</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle)</td></tr>
<tr class="separator:abd3ea01869b89ff23f9522640479c395"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5462c4618d0a229116db5fbadacf95d2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)</td></tr>
<tr class="separator:a5462c4618d0a229116db5fbadacf95d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78cbe1b851fea48001112f7ba9e4ea62"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync_createWithOptions</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context, <a class="el" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a> *options)</td></tr>
<tr class="separator:a78cbe1b851fea48001112f7ba9e4ea62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0388b226a414b09fa733f6d65004ec32"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, const <a class="el" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a> *options)</td></tr>
<tr class="separator:a0388b226a414b09fa733f6d65004ec32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc69afa4725f8321bdaa5a05aec5cfd5"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync_disconnect</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, const <a class="el" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a> *options)</td></tr>
<tr class="separator:adc69afa4725f8321bdaa5a05aec5cfd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46c332245c379629ae11f457fc179457"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457">MQTTAsync_isConnected</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle)</td></tr>
<tr class="separator:a46c332245c379629ae11f457fc179457"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae10bd009934b3bb4a9f4abae7424a611"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync_subscribe</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, const char *topic, int qos, <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *response)</td></tr>
<tr class="separator:ae10bd009934b3bb4a9f4abae7424a611"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac78620b33434a187255bd1a3faec1578"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync_subscribeMany</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, int count, char *const *topic, const int *qos, <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *response)</td></tr>
<tr class="separator:ac78620b33434a187255bd1a3faec1578"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08d18ece91c1b011011354570d8ac1ab"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab">MQTTAsync_unsubscribe</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, const char *topic, <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *response)</td></tr>
<tr class="separator:a08d18ece91c1b011011354570d8ac1ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69fd433ce1b9b6a1b3b453c4793a9311"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311">MQTTAsync_unsubscribeMany</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, int count, char *const *topic, <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *response)</td></tr>
<tr class="separator:a69fd433ce1b9b6a1b3b453c4793a9311"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63c66a311ab16239a4175ff671871bf2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync_send</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, const char *destinationName, int payloadlen, const void *payload, int qos, int retained, <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *response)</td></tr>
<tr class="separator:a63c66a311ab16239a4175ff671871bf2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5687171e67e98f9ea590c9e3b64cde18"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, const char *destinationName, const <a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> *msg, <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *response)</td></tr>
<tr class="separator:a5687171e67e98f9ea590c9e3b64cde18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc92f60743fc471643b473abbc987be0"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync_getPendingTokens</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> **tokens)</td></tr>
<tr class="separator:abc92f60743fc471643b473abbc987be0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab207095cab6f9a48b52cdb593b8456f4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4">MQTTAsync_isComplete</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token)</td></tr>
<tr class="separator:ab207095cab6f9a48b52cdb593b8456f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4fe09cc9c976b1cf424e13765d6cd8c9"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync_waitForCompletion</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle, <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token, unsigned long timeout)</td></tr>
<tr class="separator:a4fe09cc9c976b1cf424e13765d6cd8c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b45db63052fe29ab1fad22d2a00c91c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync_freeMessage</a> (<a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> **msg)</td></tr>
<tr class="separator:a9b45db63052fe29ab1fad22d2a00c91c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b836f58612a2c4627e40ae848da190d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync_free</a> (void *ptr)</td></tr>
<tr class="separator:a2b836f58612a2c4627e40ae848da190d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5500ba58592afafaade2fcabdc61e61"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#af5500ba58592afafaade2fcabdc61e61">MQTTAsync_malloc</a> (size_t size)</td></tr>
<tr class="separator:af5500ba58592afafaade2fcabdc61e61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5562f9dc71fbd93d25ad20b328cb887"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy</a> (<a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> *handle)</td></tr>
<tr class="separator:ad5562f9dc71fbd93d25ad20b328cb887"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7fbab13a0b2e5dd4ee11efbbb9f6a3a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">MQTTAsync_setTraceLevel</a> (enum <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> level)</td></tr>
<tr class="separator:ac7fbab13a0b2e5dd4ee11efbbb9f6a3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b350581324a4ff0eaee71e7a6721388"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388">MQTTAsync_setTraceCallback</a> (<a class="el" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a> *callback)</td></tr>
<tr class="separator:a0b350581324a4ff0eaee71e7a6721388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7cf29b785a1d4ff1de2e67e2f916b658"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a7cf29b785a1d4ff1de2e67e2f916b658">MQTTAsync_getVersionInfo</a> (void)</td></tr>
<tr class="separator:a7cf29b785a1d4ff1de2e67e2f916b658"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a875cd089a8b23eb3fd50c0406fc75d9f"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_async_8h.html#a875cd089a8b23eb3fd50c0406fc75d9f">MQTTAsync_strerror</a> (int code)</td></tr>
<tr class="separator:a875cd089a8b23eb3fd50c0406fc75d9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="afe0cffcce8efe25186f79c51ac44e16f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afe0cffcce8efe25186f79c51ac44e16f">&#9670;&nbsp;</a></span>MQTTASYNC_SUCCESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_SUCCESS&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: No error. Indicates successful completion of an MQTT client operation. </p>

</div>
</div>
<a id="a7c8230fef85fc04b8a1035501f3be406"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7c8230fef85fc04b8a1035501f3be406">&#9670;&nbsp;</a></span>MQTTASYNC_FAILURE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_FAILURE&#160;&#160;&#160;-1</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A generic error code indicating the failure of an MQTT client operation. </p>

</div>
</div>
<a id="a4edf1249c75abd4975fec8ddeae2cdc9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4edf1249c75abd4975fec8ddeae2cdc9">&#9670;&nbsp;</a></span>MQTTASYNC_PERSISTENCE_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_PERSISTENCE_ERROR&#160;&#160;&#160;-2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a66c0f30b329bc770145c2f04b3929df6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66c0f30b329bc770145c2f04b3929df6">&#9670;&nbsp;</a></span>MQTTASYNC_DISCONNECTED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_DISCONNECTED&#160;&#160;&#160;-3</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: The client is disconnected. </p>

</div>
</div>
<a id="ad577286d43c72fbc49818aac42f4e24a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad577286d43c72fbc49818aac42f4e24a">&#9670;&nbsp;</a></span>MQTTASYNC_MAX_MESSAGES_INFLIGHT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_MAX_MESSAGES_INFLIGHT&#160;&#160;&#160;-4</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: The maximum number of messages allowed to be simultaneously in-flight has been reached. </p>

</div>
</div>
<a id="a80cbe091930c11b67ca719b3e385aa26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a80cbe091930c11b67ca719b3e385aa26">&#9670;&nbsp;</a></span>MQTTASYNC_BAD_UTF8_STRING</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_BAD_UTF8_STRING&#160;&#160;&#160;-5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: An invalid UTF-8 string has been detected. </p>

</div>
</div>
<a id="ab88e1ebcee991099a72429e52a8253fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab88e1ebcee991099a72429e52a8253fd">&#9670;&nbsp;</a></span>MQTTASYNC_NULL_PARAMETER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_NULL_PARAMETER&#160;&#160;&#160;-6</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A NULL parameter has been supplied when this is invalid. </p>

</div>
</div>
<a id="a77a7106d97ff60be3fe70f90b1867800"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77a7106d97ff60be3fe70f90b1867800">&#9670;&nbsp;</a></span>MQTTASYNC_TOPICNAME_TRUNCATED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_TOPICNAME_TRUNCATED&#160;&#160;&#160;-7</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: The topic has been truncated (the topic string includes embedded NULL characters). String functions will not access the full topic. Use the topic length value to access the full topic. </p>

</div>
</div>
<a id="a241fc8db46dca132d591bc2be92247ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a241fc8db46dca132d591bc2be92247ba">&#9670;&nbsp;</a></span>MQTTASYNC_BAD_STRUCTURE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_BAD_STRUCTURE&#160;&#160;&#160;-8</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A structure parameter does not have the correct eyecatcher and version number. </p>

</div>
</div>
<a id="a64d111778ce4e0d3a62808f6db11f224"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a64d111778ce4e0d3a62808f6db11f224">&#9670;&nbsp;</a></span>MQTTASYNC_BAD_QOS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_BAD_QOS&#160;&#160;&#160;-9</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: A qos parameter is not 0, 1 or 2 </p>

</div>
</div>
<a id="ab0f54d0bae2c74849022a8009e5d6ff7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab0f54d0bae2c74849022a8009e5d6ff7">&#9670;&nbsp;</a></span>MQTTASYNC_NO_MORE_MSGIDS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_NO_MORE_MSGIDS&#160;&#160;&#160;-10</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: All 65535 MQTT msgids are being used </p>

</div>
</div>
<a id="aee1b79d0632bec0fe49eb7ea1abd3b2e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee1b79d0632bec0fe49eb7ea1abd3b2e">&#9670;&nbsp;</a></span>MQTTASYNC_OPERATION_INCOMPLETE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_OPERATION_INCOMPLETE&#160;&#160;&#160;-11</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: the request is being discarded when not complete </p>

</div>
</div>
<a id="a4e338072cfd5291b579e4f0c99a6e773"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e338072cfd5291b579e4f0c99a6e773">&#9670;&nbsp;</a></span>MQTTASYNC_MAX_BUFFERED_MESSAGES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_MAX_BUFFERED_MESSAGES&#160;&#160;&#160;-12</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: no more messages can be buffered </p>

</div>
</div>
<a id="a6992c00553db1608aef9e162c161d73c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6992c00553db1608aef9e162c161d73c">&#9670;&nbsp;</a></span>MQTTASYNC_SSL_NOT_SUPPORTED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_SSL_NOT_SUPPORTED&#160;&#160;&#160;-13</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: Attempting SSL connection using non-SSL version of library </p>

</div>
</div>
<a id="a785250cd4a1938ffeeff67b3538abfba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a785250cd4a1938ffeeff67b3538abfba">&#9670;&nbsp;</a></span>MQTTASYNC_BAD_PROTOCOL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_BAD_PROTOCOL&#160;&#160;&#160;-14</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: protocol prefix in serverURI should be: </p><ul>
<li><em>tcp://</em> or <em>mqtt://</em> - Insecure TCP </li>
<li><em>ssl://</em> or <em>mqtts://</em> - Encrypted SSL/TLS </li>
<li><em>ws://</em> - Insecure websockets </li>
<li><em>wss://</em> - Secure web sockets</li>
</ul>
<p>The TLS enabled prefixes (ssl, mqtts, wss) are only valid if the TLS version of the library is linked with. </p>

</div>
</div>
<a id="af6f97562573876867ba77460a51ca1d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af6f97562573876867ba77460a51ca1d1">&#9670;&nbsp;</a></span>MQTTASYNC_BAD_MQTT_OPTION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_BAD_MQTT_OPTION&#160;&#160;&#160;-15</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: don't use options for another version of MQTT </p>

</div>
</div>
<a id="af5df806e9767e1e3182fe089a8ee551b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af5df806e9767e1e3182fe089a8ee551b">&#9670;&nbsp;</a></span>MQTTASYNC_WRONG_MQTT_VERSION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_WRONG_MQTT_VERSION&#160;&#160;&#160;-16</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: call not applicable to the client's version of MQTT </p>

</div>
</div>
<a id="a47b3aed75983f48a503e1cad6c862004"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a47b3aed75983f48a503e1cad6c862004">&#9670;&nbsp;</a></span>MQTTASYNC_0_LEN_WILL_TOPIC</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_0_LEN_WILL_TOPIC&#160;&#160;&#160;-17</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return code: 0 length will topic </p>

</div>
</div>
<a id="a8278cf4b50dd818c31fa12e45f074b5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8278cf4b50dd818c31fa12e45f074b5c">&#9670;&nbsp;</a></span>MQTTASYNC_COMMAND_IGNORED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_COMMAND_IGNORED&#160;&#160;&#160;-18</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2efee8e190e2c3690c680bde060f78ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2efee8e190e2c3690c680bde060f78ab">&#9670;&nbsp;</a></span>MQTTASYNC_MAX_BUFFERED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_MAX_BUFFERED&#160;&#160;&#160;-19</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a75b80b01f98d5a1ffa2a4d42995a8397"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a75b80b01f98d5a1ffa2a4d42995a8397">&#9670;&nbsp;</a></span>MQTTVERSION_DEFAULT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_DEFAULT&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Default MQTT version to connect with. Use 3.1.1 then fall back to 3.1 </p>

</div>
</div>
<a id="a4603b988e76872e1f23f135d225ce2fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4603b988e76872e1f23f135d225ce2fb">&#9670;&nbsp;</a></span>MQTTVERSION_3_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_3_1&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version to connect with: 3.1 </p>

</div>
</div>
<a id="ac79cc6fdeaa9e3f4ee12c3418898b1ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac79cc6fdeaa9e3f4ee12c3418898b1ef">&#9670;&nbsp;</a></span>MQTTVERSION_3_1_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_3_1_1&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version to connect with: 3.1.1 </p>

</div>
</div>
<a id="af8b176fa4d5b89789767ce972338e1e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8b176fa4d5b89789767ce972338e1e3">&#9670;&nbsp;</a></span>MQTTVERSION_5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTVERSION_5&#160;&#160;&#160;5</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version to connect with: 5 </p>

</div>
</div>
<a id="ade337b363b7f4bc7c1a7b2858e0380bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade337b363b7f4bc7c1a7b2858e0380bd">&#9670;&nbsp;</a></span>MQTT_BAD_SUBSCRIBE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_BAD_SUBSCRIBE&#160;&#160;&#160;0x80</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Bad return code from subscribe, as defined in the 3.1.1 specification </p>

</div>
</div>
<a id="a866e023f70141969d48597930c0ee313"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a866e023f70141969d48597930c0ee313">&#9670;&nbsp;</a></span>MQTTAsync_init_options_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_init_options_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'G'}, 0, 0 }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6a85061dadab532f28e96e5ab3c600e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a85061dadab532f28e96e5ab3c600e9">&#9670;&nbsp;</a></span>MQTTAsync_message_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_message_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'M'}, 1, 0, NULL, 0, 0, 0, 0, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a> }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2e415e68016ae56f6bbbbdc9840a9c6e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2e415e68016ae56f6bbbbdc9840a9c6e">&#9670;&nbsp;</a></span>MQTTAsync_connectData_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_connectData_initializer&#160;&#160;&#160;{{'M', 'Q', 'C', 'D'}, 0, NULL, {0, NULL}}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a53ce2002ae2c2579575bb41c48c51c29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53ce2002ae2c2579575bb41c48c51c29">&#9670;&nbsp;</a></span>MQTTAsync_failureData5_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_failureData5_initializer&#160;&#160;&#160;{{'M', 'Q', 'F', 'D'}, 0, 0, <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a>, 0, NULL, 0}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6182ec90ec4a134465f627b324ac5a41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6182ec90ec4a134465f627b324ac5a41">&#9670;&nbsp;</a></span>MQTTAsync_successData5_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_successData5_initializer&#160;&#160;&#160;{{'M', 'Q', 'S', 'D'}, 0, 0, <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a>, {.sub={0,0}}}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3f8b408243b5c2369bc9758f2edf0878"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3f8b408243b5c2369bc9758f2edf0878">&#9670;&nbsp;</a></span>MQTTAsync_responseOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_responseOptions_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'R'}, 1, NULL, NULL, 0, 0, NULL, NULL, <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a>, <a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aec3b45fd0367106eea344396f87cfda7">MQTTSubscribe_options_initializer</a>, 0, NULL}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a570185766fc8a9da410a6f84915b6df5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a570185766fc8a9da410a6f84915b6df5">&#9670;&nbsp;</a></span>MQTTAsync_callOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_callOptions_initializer&#160;&#160;&#160;<a class="el" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">MQTTAsync_responseOptions_initializer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5fedeafef4753f09b1bcb92773564786"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5fedeafef4753f09b1bcb92773564786">&#9670;&nbsp;</a></span>MQTTAsync_createOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_createOptions_initializer&#160;&#160;&#160;{ {'M', 'Q', 'C', 'O'}, 2, 0, 100, <a class="el" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>, 0, 0, 1, 1}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0008776a46e7268ccbef4774ce3d4579"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0008776a46e7268ccbef4774ce3d4579">&#9670;&nbsp;</a></span>MQTTAsync_createOptions_initializer5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_createOptions_initializer5&#160;&#160;&#160;{ {'M', 'Q', 'C', 'O'}, 2, 0, 100, <a class="el" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>, 0, 0, 1, 1}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6c45768e1b28844f2ac0f6ac68709730"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6c45768e1b28844f2ac0f6ac68709730">&#9670;&nbsp;</a></span>MQTTAsync_willOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_willOptions_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'W'}, 1, NULL, NULL, 0, 0, { 0, NULL } }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2549ea897af26c76198284731db9e721"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2549ea897af26c76198284731db9e721">&#9670;&nbsp;</a></span>MQTT_SSL_VERSION_DEFAULT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_DEFAULT&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7e5da3d6f0d2b53409bbfcf6e56f3d2d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">&#9670;&nbsp;</a></span>MQTT_SSL_VERSION_TLS_1_0</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_TLS_1_0&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abdff87efa3f2ee473a1591e10638b537"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abdff87efa3f2ee473a1591e10638b537">&#9670;&nbsp;</a></span>MQTT_SSL_VERSION_TLS_1_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_TLS_1_1&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3a94dbdeafbb73c73a068e7c2085fbab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3a94dbdeafbb73c73a068e7c2085fbab">&#9670;&nbsp;</a></span>MQTT_SSL_VERSION_TLS_1_2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_SSL_VERSION_TLS_1_2&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aac935e2e9d770a53ee8189f128530511"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac935e2e9d770a53ee8189f128530511">&#9670;&nbsp;</a></span>MQTTAsync_SSLOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_SSLOptions_initializer&#160;&#160;&#160;{ {'M', 'Q', 'T', 'S'}, 5, NULL, NULL, NULL, NULL, NULL, 1, <a class="el" href="_m_q_t_t_async_8h.html#a2549ea897af26c76198284731db9e721">MQTT_SSL_VERSION_DEFAULT</a>, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae18b51f22784a43803eb809d6a0c2492"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae18b51f22784a43803eb809d6a0c2492">&#9670;&nbsp;</a></span>MQTTAsync_connectOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_connectOptions_initializer</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 60, 1, 65535, NULL, NULL, NULL, 30, 0,\</div>
<div class="line">NULL, NULL, NULL, NULL, 0, NULL, <a class="code" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 3.1.1 non-WebSocket connections </p>

</div>
</div>
<a id="abd403ce21f7aa0348ae1d3eefd031a5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd403ce21f7aa0348ae1d3eefd031a5d">&#9670;&nbsp;</a></span>MQTTAsync_connectOptions_initializer5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_connectOptions_initializer5</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 60, 0, 65535, NULL, NULL, NULL, 30, 0,\</div>
<div class="line">NULL, NULL, NULL, NULL, 0, NULL, <a class="code" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 5.0 non-WebSocket connections </p>

</div>
</div>
<a id="a080951d916d7a58c4ceff8c6bacfe313"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a080951d916d7a58c4ceff8c6bacfe313">&#9670;&nbsp;</a></span>MQTTAsync_connectOptions_initializer_ws</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_connectOptions_initializer_ws</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 45, 1, 65535, NULL, NULL, NULL, 30, 0,\</div>
<div class="line">NULL, NULL, NULL, NULL, 0, NULL, <a class="code" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a>, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 3.1.1 WebSockets connections. The keepalive interval is set to 45 seconds to avoid webserver 60 second inactivity timeouts. </p>

</div>
</div>
<a id="a513bfbec7b7d39c827240db75aa4044b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a513bfbec7b7d39c827240db75aa4044b">&#9670;&nbsp;</a></span>MQTTAsync_connectOptions_initializer5_ws</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_connectOptions_initializer5_ws</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">{ {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;C&#39;</span>}, 8, 45, 0, 65535, NULL, NULL, NULL, 30, 0,\</div>
<div class="line">NULL, NULL, NULL, NULL, 0, NULL, <a class="code" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a>, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}</div>
</div><!-- fragment --><p>Initializer for connect options for MQTT 5.0 WebSockets connections. The keepalive interval is set to 45 seconds to avoid webserver 60 second inactivity timeouts. </p>

</div>
</div>
<a id="a2fd5d6df31928ae468f3f2e522b9c707"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2fd5d6df31928ae468f3f2e522b9c707">&#9670;&nbsp;</a></span>MQTTAsync_disconnectOptions_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_disconnectOptions_initializer</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">        { {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;D&#39;</span>}, 0, 0, NULL, NULL, NULL,\</div>
<div class="line">        MQTTProperties_initializer, <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, NULL, NULL }</div>
</div><!-- fragment -->
</div>
</div>
<a id="aaa278001953dc129003eff83c8e7b3db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa278001953dc129003eff83c8e7b3db">&#9670;&nbsp;</a></span>MQTTAsync_disconnectOptions_initializer5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTAsync_disconnectOptions_initializer5</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">        { {<span class="charliteral">&#39;M&#39;</span>, <span class="charliteral">&#39;Q&#39;</span>, <span class="charliteral">&#39;T&#39;</span>, <span class="charliteral">&#39;D&#39;</span>}, 1, 0, NULL, NULL, NULL,\</div>
<div class="line">        MQTTProperties_initializer, <a class="code" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a>, NULL, NULL }</div>
</div><!-- fragment -->
</div>
</div>
<a id="a61e6ee632e63312d382e2fcbe427f01a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a61e6ee632e63312d382e2fcbe427f01a">&#9670;&nbsp;</a></span>MQTTASYNC_TRUE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTASYNC_TRUE&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Tests whether a request corresponding to a token is complete.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">token</td><td>An <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> associated with a request. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>1 if the request has been completed, 0 if not. </dd></dl>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="a0db1d736cdc0c864fe41abb3afd605bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0db1d736cdc0c864fe41abb3afd605bd">&#9670;&nbsp;</a></span>MQTTAsync</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void* <a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A handle representing an MQTT client. A valid client handle is available following a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </p>

</div>
</div>
<a id="a7ca6d2a1813f2bbd0bc3af2771e46ba4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ca6d2a1813f2bbd0bc3af2771e46ba4">&#9670;&nbsp;</a></span>MQTTAsync_token</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A value representing an MQTT message. A token is returned to the client application when a message is published. The token can then be used to check that the message was successfully delivered to its destination (see MQTTAsync_publish(), MQTTAsync_publishMessage(), <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete()</a>, and <a class="el" href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync_getPendingTokens()</a>). </p>

</div>
</div>
<a id="a3918ead59b56816a8d7544def184e48e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3918ead59b56816a8d7544def184e48e">&#9670;&nbsp;</a></span>MQTTAsync_messageArrived</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int MQTTAsync_messageArrived(void *context, char *topicName, int topicLen, <a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> *message)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous receipt of messages. The function is registered with the client library by passing it as an argument to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>. It is called by the client library when a new message that matches a client subscription has been received from the server. This function is executed on a separate thread to the one on which the client application is running.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">topicName</td><td>The topic associated with the received message. </td></tr>
    <tr><td class="paramname">topicLen</td><td>The length of the topic if there are one more NULL characters embedded in <em>topicName</em>, otherwise <em>topicLen</em> is 0. If <em>topicLen</em> is 0, the value returned by <em>strlen(topicName)</em> can be trusted. If <em>topicLen</em> is greater than 0, the full topic name can be retrieved by accessing <em>topicName</em> as a byte array of length <em>topicLen</em>. </td></tr>
    <tr><td class="paramname">message</td><td>The <a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> structure for the received message. This structure contains the message payload and attributes. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>This function must return 0 or 1 indicating whether or not the message has been safely received by the client application. <br  />
 Returning 1 indicates that the message has been successfully handled. To free the message storage, <a class="el" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync_freeMessage</a> must be called. To free the topic name storage, <a class="el" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync_free</a> must be called.<br  />
 Returning 0 indicates that there was a problem. In this case, the client library will reinvoke <a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived()</a> to attempt to deliver the message to the application again. Do not free the message and topic storage when returning 0, otherwise the redelivery will fail. </dd></dl>

</div>
</div>
<a id="ab10296618e266b3c02fd117d6616b15d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab10296618e266b3c02fd117d6616b15d">&#9670;&nbsp;</a></span>MQTTAsync_deliveryComplete</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_deliveryComplete(void *context, <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> token)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous notification of delivery of messages to the server. The function is registered with the client library by passing it as an argument to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>. It is called by the client library after the client application has published a message to the server. It indicates that the necessary handshaking and acknowledgements for the requested quality of service (see <a class="el" href="struct_m_q_t_t_async__message.html#a35738099155a0e4f54050da474bab2e7">MQTTAsync_message.qos</a>) have been completed. This function is executed on a separate thread to the one on which the client application is running.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">token</td><td>The <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> associated with the published message. Applications can check that all messages have been correctly published by matching the tokens returned from calls to <a class="el" href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync_send()</a> and <a class="el" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage()</a> with the tokens passed to this callback. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a3900a98d7b1d58ad6e686bfce298bb6c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3900a98d7b1d58ad6e686bfce298bb6c">&#9670;&nbsp;</a></span>MQTTAsync_connectionLost</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_connectionLost(void *context, char *cause)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous notification of the loss of connection to the server. The function is registered with the client library by passing it as an argument to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>. It is called by the client library if the client loses its connection to the server. The client application must take appropriate action, such as trying to reconnect or reporting the problem. This function is executed on a separate thread to the one on which the client application is running.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">cause</td><td>The reason for the disconnection. Currently, <em>cause</em> is always set to NULL. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a34bb8d321e9d368780b5c832c058f223"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a34bb8d321e9d368780b5c832c058f223">&#9670;&nbsp;</a></span>MQTTAsync_connected</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_connected(void *context, char *cause)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function, which will be called when the client library successfully connects. This is superfluous when the connection is made in response to a MQTTAsync_connect call, because the onSuccess callback can be used. It is intended for use when automatic reconnect is enabled, so that when a reconnection attempt succeeds in the background, the application is notified and can take any required actions.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">cause</td><td>The reason for the disconnection. Currently, <em>cause</em> is always set to NULL. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a52a1d9ab6e5d5064a3de42d0eec88f57"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a52a1d9ab6e5d5064a3de42d0eec88f57">&#9670;&nbsp;</a></span>MQTTAsync_disconnected</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_disconnected(void *context, <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> reasonCode)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function, which will be called when the client library receives a disconnect packet from the server. This applies to MQTT V5 and above only.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">properties</td><td>the properties in the disconnect packet. </td></tr>
    <tr><td class="paramname">properties</td><td>the reason code from the disconnect packet Currently, <em>cause</em> is always set to NULL. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5e44304a2c011a7d61b72c779ad83979"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5e44304a2c011a7d61b72c779ad83979">&#9670;&nbsp;</a></span>MQTTAsync_updateConnectOptions</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int MQTTAsync_updateConnectOptions(void *context, <a class="el" href="struct_m_q_t_t_async__connect_data.html">MQTTAsync_connectData</a> *data)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function which will allow the client application to update the connection data. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">data</td><td>The connection data which can be modified by the application. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return a non-zero value to update the connect data, zero to keep the same data. </dd></dl>

</div>
</div>
<a id="a7b0c18a0e29e2ce73f3ea109bc32617b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7b0c18a0e29e2ce73f3ea109bc32617b">&#9670;&nbsp;</a></span>MQTTAsync_onSuccess</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_onSuccess(void *context, <a class="el" href="struct_m_q_t_t_async__success_data.html">MQTTAsync_successData</a> *response)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous notification of the successful completion of an API call. The function is registered with the client library by passing it as an argument in <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">response</td><td>Any success data associated with the API completion. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a892cf122e6e8d8f6cd38c4c8efe8fb67"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a892cf122e6e8d8f6cd38c4c8efe8fb67">&#9670;&nbsp;</a></span>MQTTAsync_onSuccess5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_onSuccess5(void *context, <a class="el" href="struct_m_q_t_t_async__success_data5.html">MQTTAsync_successData5</a> *response)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function, the MQTT V5 version of <a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess</a>. The client application must provide an implementation of this function to enable asynchronous notification of the successful completion of an API call. The function is registered with the client library by passing it as an argument in <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">response</td><td>Any success data associated with the API completion. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6060c25c2641e878803aef76fefb31ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6060c25c2641e878803aef76fefb31ee">&#9670;&nbsp;</a></span>MQTTAsync_onFailure</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_onFailure(void *context, <a class="el" href="struct_m_q_t_t_async__failure_data.html">MQTTAsync_failureData</a> *response)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function. The client application must provide an implementation of this function to enable asynchronous notification of the unsuccessful completion of an API call. The function is registered with the client library by passing it as an argument in <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">response</td><td>Failure data associated with the API completion. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a8c5023e04d5c3e9805d5dae76df21f4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c5023e04d5c3e9805d5dae76df21f4c">&#9670;&nbsp;</a></span>MQTTAsync_onFailure5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_onFailure5(void *context, <a class="el" href="struct_m_q_t_t_async__failure_data5.html">MQTTAsync_failureData5</a> *response)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function, the MQTT V5 version of <a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync_onFailure</a>. The application must provide an implementation of this function to enable asynchronous notification of the unsuccessful completion of an API call. The function is registered with the client library by passing it as an argument in <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>.</p>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to the <em>context</em> value originally passed to <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a>, which contains any application-specific context. </td></tr>
    <tr><td class="paramname">response</td><td>Failure data associated with the API completion. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae1568d96d6418004cc79466c06f3d791"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae1568d96d6418004cc79466c06f3d791">&#9670;&nbsp;</a></span>MQTTAsync_responseOptions</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Structure to define call options. For MQTT 5.0 there is input data as well as that describing the response method. So there is now also a synonym <a class="el" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">MQTTAsync_callOptions</a> to better reflect the use. This responseOptions name is kept for backward compatibility. </p>

</div>
</div>
<a id="ab6bfa6beae93c259220e1a131ba1cf9c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6bfa6beae93c259220e1a131ba1cf9c">&#9670;&nbsp;</a></span>MQTTAsync_callOptions</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> <a class="el" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">MQTTAsync_callOptions</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A synonym for responseOptions to better reflect its usage since MQTT 5.0 </p>

</div>
</div>
<a id="a65aba1caeae9b5af5d5b6c5598a75b02"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a65aba1caeae9b5af5d5b6c5598a75b02">&#9670;&nbsp;</a></span>MQTTAsync_traceCallback</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void MQTTAsync_traceCallback(enum <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a> level, char *message)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is a callback function prototype which must be implemented if you want to receive trace information. Do not invoke any other Paho API calls in this callback function - unpredictable behavior may result. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">level</td><td>the trace level of the message returned </td></tr>
    <tr><td class="paramname">message</td><td>the trace message. This is a pointer to a static buffer which will be overwritten on each call. You must copy the data if you want to keep it for later. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="a5de816f986b318947709a34e0787eda5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5de816f986b318947709a34e0787eda5">&#9670;&nbsp;</a></span>MQTTASYNC_TRACE_LEVELS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1"></a>MQTTASYNC_TRACE_MAXIMUM&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce"></a>MQTTASYNC_TRACE_MEDIUM&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b"></a>MQTTASYNC_TRACE_MINIMUM&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e"></a>MQTTASYNC_TRACE_PROTOCOL&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8"></a>MQTTASYNC_TRACE_ERROR&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40"></a>MQTTASYNC_TRACE_SEVERE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295"></a>MQTTASYNC_TRACE_FATAL&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a1705e75a48999cb45bf85c15608478f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1705e75a48999cb45bf85c15608478f5">&#9670;&nbsp;</a></span>MQTTAsync_global_init()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTAsync_global_init </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__init__options.html">MQTTAsync_init_options</a> *&#160;</td>
          <td class="paramname"><em>inits</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Global init of mqtt library. Call once on program start to set global behaviour. handle_openssl_init - if mqtt library should handle openssl init (1) or rely on the caller to init it before using mqtt (0) </p>

</div>
</div>
<a id="ada4dd26d23c8849c51e4ab8200339040"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada4dd26d23c8849c51e4ab8200339040">&#9670;&nbsp;</a></span>MQTTAsync_setDisconnected()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setDisconnected </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected</a> *&#160;</td>
          <td class="paramname"><em>co</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the <a class="el" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync_disconnected()</a> callback function for a client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>.</td></tr>
  </table>
  </dd>
</dl>
<p><b>Note:</b> Neither <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a> nor <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a> should be called within this callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to each of the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">co</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected()</a> callback function. NULL removes the callback setting. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="aa078aec3eba83481f63db3c3939a5da9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa078aec3eba83481f63db3c3939a5da9">&#9670;&nbsp;</a></span>MQTTAsync_setUpdateConnectOptions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setUpdateConnectOptions </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions</a> *&#160;</td>
          <td class="paramname"><em>co</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the <a class="el" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions()</a> callback function for a client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to each of the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">co</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync_updateConnectOptions()</a> callback function. NULL removes the callback setting. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a1002b09c62a096578c9b3e0135eb98c1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1002b09c62a096578c9b3e0135eb98c1">&#9670;&nbsp;</a></span>MQTTAsync_setBeforePersistenceWrite()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setBeforePersistenceWrite </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a> *&#160;</td>
          <td class="paramname"><em>co</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the <a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite()</a> callback function for a client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to the callback function to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">co</td><td>A pointer to an <a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite()</a> callback function. NULL removes the callback setting. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ab4d16e3c57502be6a7d1b1d3bcc382f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4d16e3c57502be6a7d1b1d3bcc382f3">&#9670;&nbsp;</a></span>MQTTAsync_setAfterPersistenceRead()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setAfterPersistenceRead </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a> *&#160;</td>
          <td class="paramname"><em>co</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the <a class="el" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead()</a> callback function for a client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to the callback function to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">co</td><td>A pointer to an <a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite()</a> callback function. NULL removes the callback setting. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae9ae8d61023e7029ef5a19f5219c3599"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9ae8d61023e7029ef5a19f5219c3599">&#9670;&nbsp;</a></span>MQTTAsync_setCallbacks()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setCallbacks </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a> *&#160;</td>
          <td class="paramname"><em>cl</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a> *&#160;</td>
          <td class="paramname"><em>ma</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a> *&#160;</td>
          <td class="paramname"><em>dc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the global callback functions for a specific client. If your client application doesn't use a particular callback, set the relevant parameter to NULL. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application. If you do not set a messageArrived callback function, you will not be notified of the receipt of any messages as a result of a subscription.</p>
<p><b>Note:</b> The MQTT client must be disconnected when this function is called. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to each of the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">cl</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost()</a> callback function. You can set this to NULL if your application doesn't handle disconnections. </td></tr>
    <tr><td class="paramname">ma</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived()</a> callback function. If this callback is not set, an error will be returned. You must set this callback because otherwise there would be no way to deliver any incoming messages. </td></tr>
    <tr><td class="paramname">dc</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete()</a> callback function. You can set this to NULL if you do not want to check for successful delivery. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="aee15bbd9224efd9dcce9b4ae491b2e2e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee15bbd9224efd9dcce9b4ae491b2e2e">&#9670;&nbsp;</a></span>MQTTAsync_setConnectionLostCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setConnectionLostCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost</a> *&#160;</td>
          <td class="paramname"><em>cl</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the callback function for a connection lost event for a specific client. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application.</p>
<p><b>Note:</b> The MQTT client must be disconnected when this function is called. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">cl</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync_connectionLost()</a> callback function. You can set this to NULL if your application doesn't handle disconnections. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="a44abc360051b918a39b0596a137775ae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44abc360051b918a39b0596a137775ae">&#9670;&nbsp;</a></span>MQTTAsync_setMessageArrivedCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setMessageArrivedCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived</a> *&#160;</td>
          <td class="paramname"><em>ma</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the callback function for a message arrived event for a specific client. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application. If you do not set a messageArrived callback function, you will not be notified of the receipt of any messages as a result of a subscription.</p>
<p><b>Note:</b> The MQTT client must be disconnected when this function is called. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">ma</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync_messageArrived()</a> callback function. You can set this to NULL if your application doesn't handle receipt of messages. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="a94ec624ee22cc01d2ca58a9e646a2665"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a94ec624ee22cc01d2ca58a9e646a2665">&#9670;&nbsp;</a></span>MQTTAsync_setDeliveryCompleteCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setDeliveryCompleteCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete</a> *&#160;</td>
          <td class="paramname"><em>dc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the callback function for a delivery complete event for a specific client. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application.</p>
<p><b>Note:</b> The MQTT client must be disconnected when this function is called. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">dc</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete()</a> callback function. You can set this to NULL if you do not want to check for successful delivery. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="a18cc19740d9b00c629dc53a4420ecf1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a18cc19740d9b00c629dc53a4420ecf1f">&#9670;&nbsp;</a></span>MQTTAsync_setConnected()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_setConnected </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected</a> *&#160;</td>
          <td class="paramname"><em>co</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the <a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected()</a> callback function for a client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">context</td><td>A pointer to any application-specific context. The the <em>context</em> pointer is passed to each of the callback functions to provide access to the context information in the callback. </td></tr>
    <tr><td class="paramname">co</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync_connected()</a> callback function. NULL removes the callback setting. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="abd3ea01869b89ff23f9522640479c395"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd3ea01869b89ff23f9522640479c395">&#9670;&nbsp;</a></span>MQTTAsync_reconnect()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_reconnect </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Reconnects a client with the previously used connect options. Connect must have previously been called for this to work. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the callbacks were correctly set, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> if an error occurred. </dd></dl>

</div>
</div>
<a id="a5462c4618d0a229116db5fbadacf95d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5462c4618d0a229116db5fbadacf95d2">&#9670;&nbsp;</a></span>MQTTAsync_create()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_create </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> *&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>serverURI</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>clientId</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>persistence_type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>persistence_context</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function creates an MQTT client ready for connection to the specified server and using the specified persistent storage (see MQTTAsync_persistence). See also <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync_destroy()</a>. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> handle. The handle is populated with a valid client reference following a successful return from this function. </td></tr>
    <tr><td class="paramname">serverURI</td><td>A null-terminated string specifying the server to which the client will connect. It takes the form <em>protocol://host:port</em> where <em>protocol</em> must be: <br  />
 <em>tcp://</em> or <em>mqtt://</em> - Insecure TCP <br  />
 <em>ssl://</em> or <em>mqtts://</em> - Encrypted SSL/TLS <br  />
 <em>ws://</em> - Insecure websockets <br  />
 <em>wss://</em> - Secure web sockets <br  />
 The TLS enabled prefixes (ssl, mqtts, wss) are only valid if a TLS version of the library is linked with. For <em>host</em>, you can specify either an IP address or a host name. For instance, to connect to a server running on the local machines with the default MQTT port, specify <em>tcp://localhost:1883</em>. </td></tr>
    <tr><td class="paramname">clientId</td><td>The client identifier passed to the server when the client connects to it. It is a null-terminated UTF-8 encoded string. </td></tr>
    <tr><td class="paramname">persistence_type</td><td>The type of persistence to be used by the client: <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a>: Use in-memory persistence. If the device or system on which the client is running fails or is switched off, the current state of any in-flight messages is lost and some messages may not be delivered even at QoS1 and QoS2. <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a>: Use the default (file system-based) persistence mechanism. Status about in-flight messages is held in persistent storage and provides some protection against message loss in the case of unexpected failure. <br  />
 <a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a>: Use an application-specific persistence implementation. Using this type of persistence gives control of the persistence mechanism to the application. The application has to implement the <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a> interface. </td></tr>
    <tr><td class="paramname">persistence_context</td><td>If the application uses <a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTCLIENT_PERSISTENCE_NONE</a> persistence, this argument is unused and should be set to NULL. For <a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTCLIENT_PERSISTENCE_DEFAULT</a> persistence, it should be set to the location of the persistence directory (if set to NULL, the persistence directory used is the working directory). Applications that use <a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTCLIENT_PERSISTENCE_USER</a> persistence set this argument to point to a valid <a class="el" href="struct_m_q_t_t_client__persistence.html" title="A structure containing the function pointers to a persistence implementation and the context or state...">MQTTClient_persistence</a> structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the client is successfully created, otherwise an error code is returned. </dd></dl>

</div>
</div>
<a id="a78cbe1b851fea48001112f7ba9e4ea62"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78cbe1b851fea48001112f7ba9e4ea62">&#9670;&nbsp;</a></span>MQTTAsync_createWithOptions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_createWithOptions </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> *&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>serverURI</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>clientId</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>persistence_type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>persistence_context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__create_options.html">MQTTAsync_createOptions</a> *&#160;</td>
          <td class="paramname"><em>options</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0388b226a414b09fa733f6d65004ec32"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0388b226a414b09fa733f6d65004ec32">&#9670;&nbsp;</a></span>MQTTAsync_connect()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_connect </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a> *&#160;</td>
          <td class="paramname"><em>options</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to connect a previously-created client (see <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>) to an MQTT server using the specified options. If you want to enable asynchronous message and status notifications, you must call <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync_setCallbacks()</a> prior to <a class="el" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect()</a>. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">options</td><td>A pointer to a valid <a class="el" href="struct_m_q_t_t_async__connect_options.html">MQTTAsync_connectOptions</a> structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the client connect request was accepted. If the client was unable to connect to the server, an error code is returned via the onFailure callback, if set. Error codes greater than 0 are returned by the MQTT protocol:<br  />
<br  />
 <b>1</b>: Connection refused: Unacceptable protocol version<br  />
 <b>2</b>: Connection refused: Identifier rejected<br  />
 <b>3</b>: Connection refused: Server unavailable<br  />
 <b>4</b>: Connection refused: Bad user name or password<br  />
 <b>5</b>: Connection refused: Not authorized<br  />
 <b>6-255</b>: Reserved for future use<br  />
 </dd></dl>

</div>
</div>
<a id="adc69afa4725f8321bdaa5a05aec5cfd5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adc69afa4725f8321bdaa5a05aec5cfd5">&#9670;&nbsp;</a></span>MQTTAsync_disconnect()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_disconnect </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="struct_m_q_t_t_async__disconnect_options.html">MQTTAsync_disconnectOptions</a> *&#160;</td>
          <td class="paramname"><em>options</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to disconnect the client from the MQTT server. In order to allow the client time to complete handling of messages that are in-flight when this function is called, a timeout period is specified. When the timeout period has expired, the client disconnects even if there are still outstanding message acknowledgements. The next time the client connects to the same server, any QoS 1 or 2 messages which have not completed will be retried depending on the cleansession settings for both the previous and the new connection (see <a class="el" href="struct_m_q_t_t_async__connect_options.html#a036c36a2a4d3a3ffae9ab4dd8b3e7f7b">MQTTAsync_connectOptions.cleansession</a> and <a class="el" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync_connect()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">options</td><td>The client delays disconnection for up to this time (in milliseconds) in order to allow in-flight message transfers to complete. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the client successfully disconnects from the server. An error code is returned if the client was unable to disconnect from the server </dd></dl>

</div>
</div>
<a id="a46c332245c379629ae11f457fc179457"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a46c332245c379629ae11f457fc179457">&#9670;&nbsp;</a></span>MQTTAsync_isConnected()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_isConnected </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function allows the client application to test whether or not a client is currently connected to the MQTT server. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Boolean true if the client is connected, otherwise false. </dd></dl>

</div>
</div>
<a id="ae10bd009934b3bb4a9f4abae7424a611"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae10bd009934b3bb4a9f4abae7424a611">&#9670;&nbsp;</a></span>MQTTAsync_subscribe()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_subscribe </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>topic</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>qos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *&#160;</td>
          <td class="paramname"><em>response</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to subscribe a client to a single topic, which may contain wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). This call also specifies the <a class="el" href="qos.html">Quality of service</a> requested for the subscription (see also <a class="el" href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync_subscribeMany()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">topic</td><td>The subscription topic, which may include wildcards. </td></tr>
    <tr><td class="paramname">qos</td><td>The requested quality of service for the subscription. </td></tr>
    <tr><td class="paramname">response</td><td>A pointer to a response options structure. Used to set callback functions. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the subscription request is successful. An error code is returned if there was a problem registering the subscription. </dd></dl>

</div>
</div>
<a id="ac78620b33434a187255bd1a3faec1578"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac78620b33434a187255bd1a3faec1578">&#9670;&nbsp;</a></span>MQTTAsync_subscribeMany()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_subscribeMany </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>count</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *const *&#160;</td>
          <td class="paramname"><em>topic</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const int *&#160;</td>
          <td class="paramname"><em>qos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *&#160;</td>
          <td class="paramname"><em>response</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to subscribe a client to a list of topics, which may contain wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). This call also specifies the <a class="el" href="qos.html">Quality of service</a> requested for each topic (see also <a class="el" href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync_subscribe()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">count</td><td>The number of topics for which the client is requesting subscriptions. </td></tr>
    <tr><td class="paramname">topic</td><td>An array (of length <em>count</em>) of pointers to topics, each of which may include wildcards. </td></tr>
    <tr><td class="paramname">qos</td><td>An array (of length <em>count</em>) of <a class="el" href="qos.html">Quality of service</a> values. qos[n] is the requested QoS for topic[n]. </td></tr>
    <tr><td class="paramname">response</td><td>A pointer to a response options structure. Used to set callback functions. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the subscription request is successful. An error code is returned if there was a problem registering the subscriptions. </dd></dl>

</div>
</div>
<a id="a08d18ece91c1b011011354570d8ac1ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a08d18ece91c1b011011354570d8ac1ab">&#9670;&nbsp;</a></span>MQTTAsync_unsubscribe()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_unsubscribe </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>topic</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *&#160;</td>
          <td class="paramname"><em>response</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to remove an existing subscription made by the specified client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">topic</td><td>The topic for the subscription to be removed, which may include wildcards (see <a class="el" href="wildcard.html">Subscription wildcards</a>). </td></tr>
    <tr><td class="paramname">response</td><td>A pointer to a response options structure. Used to set callback functions. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the subscription is removed. An error code is returned if there was a problem removing the subscription. </dd></dl>

</div>
</div>
<a id="a69fd433ce1b9b6a1b3b453c4793a9311"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69fd433ce1b9b6a1b3b453c4793a9311">&#9670;&nbsp;</a></span>MQTTAsync_unsubscribeMany()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_unsubscribeMany </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>count</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *const *&#160;</td>
          <td class="paramname"><em>topic</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *&#160;</td>
          <td class="paramname"><em>response</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to remove existing subscriptions to a list of topics made by the specified client. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">count</td><td>The number subscriptions to be removed. </td></tr>
    <tr><td class="paramname">topic</td><td>An array (of length <em>count</em>) of pointers to the topics of the subscriptions to be removed, each of which may include wildcards. </td></tr>
    <tr><td class="paramname">response</td><td>A pointer to a response options structure. Used to set callback functions. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the subscriptions are removed. An error code is returned if there was a problem removing the subscriptions. </dd></dl>

</div>
</div>
<a id="a63c66a311ab16239a4175ff671871bf2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a63c66a311ab16239a4175ff671871bf2">&#9670;&nbsp;</a></span>MQTTAsync_send()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_send </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>destinationName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>payloadlen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *&#160;</td>
          <td class="paramname"><em>payload</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>qos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>retained</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *&#160;</td>
          <td class="paramname"><em>response</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to publish a message to a given topic (see also <a class="el" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync_sendMessage()</a>). An <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> is issued when this function returns successfully if the QoS is greater than 0. If the client application needs to test for successful delivery of messages, a callback should be set (see <a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess()</a> and <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">destinationName</td><td>The topic associated with this message. </td></tr>
    <tr><td class="paramname">payloadlen</td><td>The length of the payload in bytes. </td></tr>
    <tr><td class="paramname">payload</td><td>A pointer to the byte array payload of the message. </td></tr>
    <tr><td class="paramname">qos</td><td>The <a class="el" href="qos.html">Quality of service</a> of the message. </td></tr>
    <tr><td class="paramname">retained</td><td>The retained flag for the message. </td></tr>
    <tr><td class="paramname">response</td><td>A pointer to an <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> structure. Used to set callback functions. This is optional and can be set to NULL. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the message is accepted for publication. An error code is returned if there was a problem accepting the message. </dd></dl>

</div>
</div>
<a id="a5687171e67e98f9ea590c9e3b64cde18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5687171e67e98f9ea590c9e3b64cde18">&#9670;&nbsp;</a></span>MQTTAsync_sendMessage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_sendMessage </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>destinationName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> *&#160;</td>
          <td class="paramname"><em>msg</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> *&#160;</td>
          <td class="paramname"><em>response</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function attempts to publish a message to a given topic (see also MQTTAsync_publish()). An <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> is issued when this function returns successfully if the QoS is greater than 0. If the client application needs to test for successful delivery of messages, a callback should be set (see <a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync_onSuccess()</a> and <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync_deliveryComplete()</a>). </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">destinationName</td><td>The topic associated with this message. </td></tr>
    <tr><td class="paramname">msg</td><td>A pointer to a valid <a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> structure containing the payload and attributes of the message to be published. </td></tr>
    <tr><td class="paramname">response</td><td>A pointer to an <a class="el" href="struct_m_q_t_t_async__response_options.html">MQTTAsync_responseOptions</a> structure. Used to set callback functions. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the message is accepted for publication. An error code is returned if there was a problem accepting the message. </dd></dl>

</div>
</div>
<a id="abc92f60743fc471643b473abbc987be0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc92f60743fc471643b473abbc987be0">&#9670;&nbsp;</a></span>MQTTAsync_getPendingTokens()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_getPendingTokens </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> **&#160;</td>
          <td class="paramname"><em>tokens</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets a pointer to an array of tokens for messages that are currently in-flight (pending completion).</p>
<p><b>Important note:</b> The memory used to hold the array of tokens is malloc()'d in this function. The client application is responsible for freeing this memory when it is no longer required. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">tokens</td><td>The address of a pointer to an <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>. When the function returns successfully, the pointer is set to point to an array of tokens representing messages pending completion. The last member of the array is set to -1 to indicate there are no more tokens. If no tokens are pending, the pointer is set to NULL. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the function returns successfully. An error code is returned if there was a problem obtaining the list of pending tokens. </dd></dl>

</div>
</div>
<a id="ab207095cab6f9a48b52cdb593b8456f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab207095cab6f9a48b52cdb593b8456f4">&#9670;&nbsp;</a></span>MQTTAsync_isComplete()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_isComplete </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>&#160;</td>
          <td class="paramname"><em>token</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4fe09cc9c976b1cf424e13765d6cd8c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4fe09cc9c976b1cf424e13765d6cd8c9">&#9670;&nbsp;</a></span>MQTTAsync_waitForCompletion()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTAsync_waitForCompletion </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a>&#160;</td>
          <td class="paramname"><em>handle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a>&#160;</td>
          <td class="paramname"><em>token</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long&#160;</td>
          <td class="paramname"><em>timeout</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Waits for a request corresponding to a token to complete. This only works for messages with QoS greater than 0. A QoS 0 message has no MQTT token. This function will always return <a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> for a QoS 0 message.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A valid client handle from a successful call to <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>. </td></tr>
    <tr><td class="paramname">token</td><td>An <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync_token</a> associated with a request. </td></tr>
    <tr><td class="paramname">timeout</td><td>the maximum time to wait for completion, in milliseconds </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTASYNC_SUCCESS</a> if the request has been completed in the time allocated, <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTASYNC_FAILURE</a> or <a class="el" href="_m_q_t_t_async_8h.html#a66c0f30b329bc770145c2f04b3929df6">MQTTASYNC_DISCONNECTED</a> if not. </dd></dl>

</div>
</div>
<a id="a9b45db63052fe29ab1fad22d2a00c91c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b45db63052fe29ab1fad22d2a00c91c">&#9670;&nbsp;</a></span>MQTTAsync_freeMessage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTAsync_freeMessage </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> **&#160;</td>
          <td class="paramname"><em>msg</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function frees memory allocated to an MQTT message, including the additional memory allocated to the message payload. The client application calls this function when the message has been fully processed. <b>Important note:</b> This function does not free the memory allocated to a message topic string. It is the responsibility of the client application to free this memory using the <a class="el" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync_free()</a> library function. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">msg</td><td>The address of a pointer to the <a class="el" href="struct_m_q_t_t_async__message.html">MQTTAsync_message</a> structure to be freed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a2b836f58612a2c4627e40ae848da190d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2b836f58612a2c4627e40ae848da190d">&#9670;&nbsp;</a></span>MQTTAsync_free()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTAsync_free </td>
          <td>(</td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function frees memory allocated by the MQTT C client library, especially the topic name. This is needed on Windows when the client library and application program have been compiled with different versions of the C compiler. It is thus good policy to always use this function when freeing any MQTT C client- allocated memory. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr</td><td>The pointer to the client library storage to be freed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="af5500ba58592afafaade2fcabdc61e61"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af5500ba58592afafaade2fcabdc61e61">&#9670;&nbsp;</a></span>MQTTAsync_malloc()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* MQTTAsync_malloc </td>
          <td>(</td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>size</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function is used to allocate memory to be used or freed by the MQTT C client library, especially the data in the <a class="el" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTPersistence_afterRead</a> and <a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTPersistence_beforeWrite</a> callbacks. This is needed on Windows when the client library and application program have been compiled with different versions of the C compiler. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">size</td><td>The size of the memory to be allocated. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad5562f9dc71fbd93d25ad20b328cb887"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad5562f9dc71fbd93d25ad20b328cb887">&#9670;&nbsp;</a></span>MQTTAsync_destroy()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTAsync_destroy </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> *&#160;</td>
          <td class="paramname"><em>handle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function frees the memory allocated to an MQTT client (see <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync_create()</a>). It should be called when the client is no longer required. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">handle</td><td>A pointer to the handle referring to the <a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync</a> structure to be freed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac7fbab13a0b2e5dd4ee11efbbb9f6a3a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">&#9670;&nbsp;</a></span>MQTTAsync_setTraceLevel()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTAsync_setTraceLevel </td>
          <td>(</td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTASYNC_TRACE_LEVELS</a>&#160;</td>
          <td class="paramname"><em>level</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the level of trace information which will be returned in the trace callback. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">level</td><td>the trace level required </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a0b350581324a4ff0eaee71e7a6721388"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b350581324a4ff0eaee71e7a6721388">&#9670;&nbsp;</a></span>MQTTAsync_setTraceCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTAsync_setTraceCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync_traceCallback</a> *&#160;</td>
          <td class="paramname"><em>callback</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the trace callback if needed. If set to NULL, no trace information will be returned. The default trace level is MQTTASYNC_TRACE_MINIMUM. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">callback</td><td>a pointer to the function which will handle the trace information </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a7cf29b785a1d4ff1de2e67e2f916b658"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7cf29b785a1d4ff1de2e67e2f916b658">&#9670;&nbsp;</a></span>MQTTAsync_getVersionInfo()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_async__name_value.html">MQTTAsync_nameValue</a>* MQTTAsync_getVersionInfo </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns version information about the library. no trace information will be returned. The default trace level is MQTTASYNC_TRACE_MINIMUM </p><dl class="section return"><dt>Returns</dt><dd>an array of strings describing the library. The last entry is a NULL pointer. </dd></dl>

</div>
</div>
<a id="a875cd089a8b23eb3fd50c0406fc75d9f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a875cd089a8b23eb3fd50c0406fc75d9f">&#9670;&nbsp;</a></span>MQTTAsync_strerror()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* MQTTAsync_strerror </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>code</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a pointer to a string representation of the error code, or NULL. Do not free after use. Returns NULL if the error code is unknown. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">code</td><td>the MQTTASYNC_ return code. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>a static string representation of the error code. </dd></dl>

</div>
</div>
</div><!-- contents -->
<div class="ttc" id="a_m_q_t_t_reason_codes_8h_html_aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969"><div class="ttname"><a href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a></div><div class="ttdeci">@ MQTTREASONCODE_SUCCESS</div><div class="ttdef"><b>Definition:</b> MQTTReasonCodes.h:38</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_af8b176fa4d5b89789767ce972338e1e3"><div class="ttname"><a href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTVERSION_5</a></div><div class="ttdeci">#define MQTTVERSION_5</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:221</div></div>
<div class="ttc" id="a_m_q_t_t_async_8h_html_a75b80b01f98d5a1ffa2a4d42995a8397"><div class="ttname"><a href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTVERSION_DEFAULT</a></div><div class="ttdeci">#define MQTTVERSION_DEFAULT</div><div class="ttdef"><b>Definition:</b> MQTTAsync.h:209</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:45 for Paho Asynchronous MQTT C Client Library by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
