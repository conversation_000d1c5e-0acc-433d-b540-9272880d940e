.TH "MQTTProperties.h" 3 "Thu Sep 29 2022" "Paho Asynchronous MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTProperties.h
.SH SYNOPSIS
.br
.PP
\fC#include 'MQTTExportDeclarations\&.h'\fP
.br

.SS "Data Structures"

.in +1c
.ti -1c
.RI "struct \fBMQTTLenString\fP"
.br
.ti -1c
.RI "struct \fBMQTTProperty\fP"
.br
.ti -1c
.RI "struct \fBMQTTProperties\fP"
.br
.in -1c
.SS "Macros"

.in +1c
.ti -1c
.RI "#define \fBMQTT_INVALID_PROPERTY_ID\fP   \-2"
.br
.ti -1c
.RI "#define \fBMQTTProperties_initializer\fP   {0, 0, 0, NULL}"
.br
.in -1c
.SS "Typedefs"

.in +1c
.ti -1c
.RI "typedef struct \fBMQTTProperties\fP \fBMQTTProperties\fP"
.br
.in -1c
.SS "Enumerations"

.in +1c
.ti -1c
.RI "enum \fBMQTTPropertyCodes\fP { \fBMQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR\fP = 1, \fBMQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL\fP = 2, \fBMQTTPROPERTY_CODE_CONTENT_TYPE\fP = 3, \fBMQTTPROPERTY_CODE_RESPONSE_TOPIC\fP = 8, \fBMQTTPROPERTY_CODE_CORRELATION_DATA\fP = 9, \fBMQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER\fP = 11, \fBMQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL\fP = 17, \fBMQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER\fP = 18, \fBMQTTPROPERTY_CODE_SERVER_KEEP_ALIVE\fP = 19, \fBMQTTPROPERTY_CODE_AUTHENTICATION_METHOD\fP = 21, \fBMQTTPROPERTY_CODE_AUTHENTICATION_DATA\fP = 22, \fBMQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION\fP = 23, \fBMQTTPROPERTY_CODE_WILL_DELAY_INTERVAL\fP = 24, \fBMQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION\fP = 25, \fBMQTTPROPERTY_CODE_RESPONSE_INFORMATION\fP = 26, \fBMQTTPROPERTY_CODE_SERVER_REFERENCE\fP = 28, \fBMQTTPROPERTY_CODE_REASON_STRING\fP = 31, \fBMQTTPROPERTY_CODE_RECEIVE_MAXIMUM\fP = 33, \fBMQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM\fP = 34, \fBMQTTPROPERTY_CODE_TOPIC_ALIAS\fP = 35, \fBMQTTPROPERTY_CODE_MAXIMUM_QOS\fP = 36, \fBMQTTPROPERTY_CODE_RETAIN_AVAILABLE\fP = 37, \fBMQTTPROPERTY_CODE_USER_PROPERTY\fP = 38, \fBMQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE\fP = 39, \fBMQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE\fP = 40, \fBMQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE\fP = 41, \fBMQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE\fP = 42 }"
.br
.ti -1c
.RI "enum \fBMQTTPropertyTypes\fP { \fBMQTTPROPERTY_TYPE_BYTE\fP, \fBMQTTPROPERTY_TYPE_TWO_BYTE_INTEGER\fP, \fBMQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER\fP, \fBMQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER\fP, \fBMQTTPROPERTY_TYPE_BINARY_DATA\fP, \fBMQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING\fP, \fBMQTTPROPERTY_TYPE_UTF_8_STRING_PAIR\fP }"
.br
.in -1c
.SS "Functions"

.in +1c
.ti -1c
.RI "const char * \fBMQTTPropertyName\fP (enum \fBMQTTPropertyCodes\fP value)"
.br
.ti -1c
.RI "int \fBMQTTProperty_getType\fP (enum \fBMQTTPropertyCodes\fP value)"
.br
.ti -1c
.RI "int \fBMQTTProperties_len\fP (\fBMQTTProperties\fP *props)"
.br
.ti -1c
.RI "int \fBMQTTProperties_add\fP (\fBMQTTProperties\fP *props, const \fBMQTTProperty\fP *prop)"
.br
.ti -1c
.RI "int \fBMQTTProperties_write\fP (char **pptr, const \fBMQTTProperties\fP *properties)"
.br
.ti -1c
.RI "int \fBMQTTProperties_read\fP (\fBMQTTProperties\fP *properties, char **pptr, char *enddata)"
.br
.ti -1c
.RI "void \fBMQTTProperties_free\fP (\fBMQTTProperties\fP *properties)"
.br
.ti -1c
.RI "\fBMQTTProperties\fP \fBMQTTProperties_copy\fP (const \fBMQTTProperties\fP *props)"
.br
.ti -1c
.RI "int \fBMQTTProperties_hasProperty\fP (\fBMQTTProperties\fP *props, enum \fBMQTTPropertyCodes\fP propid)"
.br
.ti -1c
.RI "int \fBMQTTProperties_propertyCount\fP (\fBMQTTProperties\fP *props, enum \fBMQTTPropertyCodes\fP propid)"
.br
.ti -1c
.RI "int \fBMQTTProperties_getNumericValue\fP (\fBMQTTProperties\fP *props, enum \fBMQTTPropertyCodes\fP propid)"
.br
.ti -1c
.RI "int \fBMQTTProperties_getNumericValueAt\fP (\fBMQTTProperties\fP *props, enum \fBMQTTPropertyCodes\fP propid, int index)"
.br
.ti -1c
.RI "\fBMQTTProperty\fP * \fBMQTTProperties_getProperty\fP (\fBMQTTProperties\fP *props, enum \fBMQTTPropertyCodes\fP propid)"
.br
.ti -1c
.RI "\fBMQTTProperty\fP * \fBMQTTProperties_getPropertyAt\fP (\fBMQTTProperties\fP *props, enum \fBMQTTPropertyCodes\fP propid, int index)"
.br
.in -1c
.SH "Macro Definition Documentation"
.PP 
.SS "#define MQTT_INVALID_PROPERTY_ID   \-2"

.SS "#define MQTTProperties_initializer   {0, 0, 0, NULL}"

.SH "Typedef Documentation"
.PP 
.SS "typedef struct \fBMQTTProperties\fP \fBMQTTProperties\fP"
MQTT version 5 property list 
.SH "Enumeration Type Documentation"
.PP 
.SS "enum \fBMQTTPropertyCodes\fP"
The one byte MQTT V5 property indicator 
.PP
\fBEnumerator\fP
.in +1c
.TP
\fB\fIMQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR \fP\fP
The value is 1 
.TP
\fB\fIMQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL \fP\fP
The value is 2 
.TP
\fB\fIMQTTPROPERTY_CODE_CONTENT_TYPE \fP\fP
The value is 3 
.TP
\fB\fIMQTTPROPERTY_CODE_RESPONSE_TOPIC \fP\fP
The value is 8 
.TP
\fB\fIMQTTPROPERTY_CODE_CORRELATION_DATA \fP\fP
The value is 9 
.TP
\fB\fIMQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER \fP\fP
The value is 11 
.TP
\fB\fIMQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL \fP\fP
The value is 17 
.TP
\fB\fIMQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER \fP\fP
The value is 18 
.TP
\fB\fIMQTTPROPERTY_CODE_SERVER_KEEP_ALIVE \fP\fP
The value is 19 
.TP
\fB\fIMQTTPROPERTY_CODE_AUTHENTICATION_METHOD \fP\fP
The value is 21 
.TP
\fB\fIMQTTPROPERTY_CODE_AUTHENTICATION_DATA \fP\fP
The value is 22 
.TP
\fB\fIMQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION \fP\fP
The value is 23 
.TP
\fB\fIMQTTPROPERTY_CODE_WILL_DELAY_INTERVAL \fP\fP
The value is 24 
.TP
\fB\fIMQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION \fP\fP
The value is 25 
.TP
\fB\fIMQTTPROPERTY_CODE_RESPONSE_INFORMATION \fP\fP
The value is 26 
.TP
\fB\fIMQTTPROPERTY_CODE_SERVER_REFERENCE \fP\fP
The value is 28 
.TP
\fB\fIMQTTPROPERTY_CODE_REASON_STRING \fP\fP
The value is 31 
.TP
\fB\fIMQTTPROPERTY_CODE_RECEIVE_MAXIMUM \fP\fP
The value is 33 
.TP
\fB\fIMQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM \fP\fP
The value is 34 
.TP
\fB\fIMQTTPROPERTY_CODE_TOPIC_ALIAS \fP\fP
The value is 35 
.TP
\fB\fIMQTTPROPERTY_CODE_MAXIMUM_QOS \fP\fP
The value is 36 
.TP
\fB\fIMQTTPROPERTY_CODE_RETAIN_AVAILABLE \fP\fP
The value is 37 
.TP
\fB\fIMQTTPROPERTY_CODE_USER_PROPERTY \fP\fP
The value is 38 
.TP
\fB\fIMQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE \fP\fP
The value is 39 
.TP
\fB\fIMQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE \fP\fP
The value is 40 
.TP
\fB\fIMQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE \fP\fP
The value is 41 
.TP
\fB\fIMQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE \fP\fP
The value is 241 
.SS "enum \fBMQTTPropertyTypes\fP"
The one byte MQTT V5 property type 
.PP
\fBEnumerator\fP
.in +1c
.TP
\fB\fIMQTTPROPERTY_TYPE_BYTE \fP\fP
.TP
\fB\fIMQTTPROPERTY_TYPE_TWO_BYTE_INTEGER \fP\fP
.TP
\fB\fIMQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER \fP\fP
.TP
\fB\fIMQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER \fP\fP
.TP
\fB\fIMQTTPROPERTY_TYPE_BINARY_DATA \fP\fP
.TP
\fB\fIMQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING \fP\fP
.TP
\fB\fIMQTTPROPERTY_TYPE_UTF_8_STRING_PAIR \fP\fP
.SH "Function Documentation"
.PP 
.SS "const char* MQTTPropertyName (enum \fBMQTTPropertyCodes\fP value)"
Returns a printable string description of an MQTT V5 property code\&. 
.PP
\fBParameters\fP
.RS 4
\fIvalue\fP an MQTT V5 property code\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the printable string description of the input property code\&. NULL if the code was not found\&. 
.RE
.PP

.SS "int MQTTProperty_getType (enum \fBMQTTPropertyCodes\fP value)"
Returns the MQTT V5 type code of an MQTT V5 property\&. 
.PP
\fBParameters\fP
.RS 4
\fIvalue\fP an MQTT V5 property code\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the MQTT V5 type code of the input property\&. -1 if the code was not found\&. 
.RE
.PP

.SS "int MQTTProperties_len (\fBMQTTProperties\fP * props)"
Returns the length of the properties structure when serialized ready for network transmission\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP an MQTT V5 property structure\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the length in bytes of the properties when serialized\&. 
.RE
.PP

.SS "int MQTTProperties_add (\fBMQTTProperties\fP * props, const \fBMQTTProperty\fP * prop)"
Add a property pointer to the property array\&. There is no memory allocation\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP The property list to add the property to\&. 
.br
\fIprop\fP The property to add to the list\&. 
.RE
.PP
\fBReturns\fP
.RS 4
0 on success, -1 on failure\&. 
.RE
.PP

.SS "int MQTTProperties_write (char ** pptr, const \fBMQTTProperties\fP * properties)"
Serialize the given property list to a character buffer, e\&.g\&. for writing to the network\&. 
.PP
\fBParameters\fP
.RS 4
\fIpptr\fP pointer to the buffer - move the pointer as we add data 
.br
\fIproperties\fP pointer to the property list, can be NULL 
.RE
.PP
\fBReturns\fP
.RS 4
whether the write succeeded or not: number of bytes written, or < 0 on failure\&. 
.RE
.PP

.SS "int MQTTProperties_read (\fBMQTTProperties\fP * properties, char ** pptr, char * enddata)"
Reads a property list from a character buffer into an array\&. 
.PP
\fBParameters\fP
.RS 4
\fIproperties\fP pointer to the property list to be filled\&. Should be initalized but empty\&. 
.br
\fIpptr\fP pointer to the character buffer\&. 
.br
\fIenddata\fP pointer to the end of the character buffer so we don't read beyond\&. 
.RE
.PP
\fBReturns\fP
.RS 4
1 if the properties were read successfully\&. 
.RE
.PP

.SS "void MQTTProperties_free (\fBMQTTProperties\fP * properties)"
Free all memory allocated to the property list, including any to individual properties\&. 
.PP
\fBParameters\fP
.RS 4
\fIproperties\fP pointer to the property list\&. 
.RE
.PP

.SS "\fBMQTTProperties\fP MQTTProperties_copy (const \fBMQTTProperties\fP * props)"
Copy the contents of a property list, allocating additional memory if needed\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP pointer to the property list\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the duplicated property list\&. 
.RE
.PP

.SS "int MQTTProperties_hasProperty (\fBMQTTProperties\fP * props, enum \fBMQTTPropertyCodes\fP propid)"
Checks if property list contains a specific property\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP pointer to the property list\&. 
.br
\fIpropid\fP the property id to check for\&. 
.RE
.PP
\fBReturns\fP
.RS 4
1 if found, 0 if not\&. 
.RE
.PP

.SS "int MQTTProperties_propertyCount (\fBMQTTProperties\fP * props, enum \fBMQTTPropertyCodes\fP propid)"
Returns the number of instances of a property id\&. Most properties can exist only once\&. User properties and subscription ids can exist more than once\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP pointer to the property list\&. 
.br
\fIpropid\fP the property id to check for\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the number of times found\&. Can be 0\&. 
.RE
.PP

.SS "int MQTTProperties_getNumericValue (\fBMQTTProperties\fP * props, enum \fBMQTTPropertyCodes\fP propid)"
Returns the integer value of a specific property\&. The property given must be a numeric type\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP pointer to the property list\&. 
.br
\fIpropid\fP the property id to check for\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the integer value of the property\&. -9999999 on failure\&. 
.RE
.PP

.SS "int MQTTProperties_getNumericValueAt (\fBMQTTProperties\fP * props, enum \fBMQTTPropertyCodes\fP propid, int index)"
Returns the integer value of a specific property when it's not the only instance\&. The property given must be a numeric type\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP pointer to the property list\&. 
.br
\fIpropid\fP the property id to check for\&. 
.br
\fIindex\fP the instance number, starting at 0\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the integer value of the property\&. -9999999 on failure\&. 
.RE
.PP

.SS "\fBMQTTProperty\fP* MQTTProperties_getProperty (\fBMQTTProperties\fP * props, enum \fBMQTTPropertyCodes\fP propid)"
Returns a pointer to the property structure for a specific property\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP pointer to the property list\&. 
.br
\fIpropid\fP the property id to check for\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the pointer to the property structure if found\&. NULL if not found\&. 
.RE
.PP

.SS "\fBMQTTProperty\fP* MQTTProperties_getPropertyAt (\fBMQTTProperties\fP * props, enum \fBMQTTPropertyCodes\fP propid, int index)"
Returns a pointer to the property structure for a specific property when it's not the only instance\&. 
.PP
\fBParameters\fP
.RS 4
\fIprops\fP pointer to the property list\&. 
.br
\fIpropid\fP the property id to check for\&. 
.br
\fIindex\fP the instance number, starting at 0\&. 
.RE
.PP
\fBReturns\fP
.RS 4
the pointer to the property structure if found\&. NULL if not found\&. 
.RE
.PP

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho Asynchronous MQTT C Client Library from the source code\&.
