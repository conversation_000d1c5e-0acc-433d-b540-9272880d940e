<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a id="index_m"></a>- m -</h3><ul>
<li>MQTT_BAD_SUBSCRIBE
: <a class="el" href="_m_q_t_t_async_8h.html#ade337b363b7f4bc7c1a7b2858e0380bd">MQTTAsync.h</a>
</li>
<li>MQTT_INVALID_PROPERTY_ID
: <a class="el" href="_m_q_t_t_properties_8h.html#afc56d2e8937a0c8f180d68ad93945945">MQTTProperties.h</a>
</li>
<li>MQTT_SSL_VERSION_DEFAULT
: <a class="el" href="_m_q_t_t_async_8h.html#a2549ea897af26c76198284731db9e721">MQTTAsync.h</a>
</li>
<li>MQTT_SSL_VERSION_TLS_1_0
: <a class="el" href="_m_q_t_t_async_8h.html#a7e5da3d6f0d2b53409bbfcf6e56f3d2d">MQTTAsync.h</a>
</li>
<li>MQTT_SSL_VERSION_TLS_1_1
: <a class="el" href="_m_q_t_t_async_8h.html#abdff87efa3f2ee473a1591e10638b537">MQTTAsync.h</a>
</li>
<li>MQTT_SSL_VERSION_TLS_1_2
: <a class="el" href="_m_q_t_t_async_8h.html#a3a94dbdeafbb73c73a068e7c2085fbab">MQTTAsync.h</a>
</li>
<li>MQTTAsync
: <a class="el" href="_m_q_t_t_async_8h.html#a0db1d736cdc0c864fe41abb3afd605bd">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_0_LEN_WILL_TOPIC
: <a class="el" href="_m_q_t_t_async_8h.html#a47b3aed75983f48a503e1cad6c862004">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_BAD_MQTT_OPTION
: <a class="el" href="_m_q_t_t_async_8h.html#af6f97562573876867ba77460a51ca1d1">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_BAD_PROTOCOL
: <a class="el" href="_m_q_t_t_async_8h.html#a785250cd4a1938ffeeff67b3538abfba">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_BAD_QOS
: <a class="el" href="_m_q_t_t_async_8h.html#a64d111778ce4e0d3a62808f6db11f224">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_BAD_STRUCTURE
: <a class="el" href="_m_q_t_t_async_8h.html#a241fc8db46dca132d591bc2be92247ba">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_BAD_UTF8_STRING
: <a class="el" href="_m_q_t_t_async_8h.html#a80cbe091930c11b67ca719b3e385aa26">MQTTAsync.h</a>
</li>
<li>MQTTAsync_callOptions
: <a class="el" href="_m_q_t_t_async_8h.html#ab6bfa6beae93c259220e1a131ba1cf9c">MQTTAsync.h</a>
</li>
<li>MQTTAsync_callOptions_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a570185766fc8a9da410a6f84915b6df5">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_COMMAND_IGNORED
: <a class="el" href="_m_q_t_t_async_8h.html#a8278cf4b50dd818c31fa12e45f074b5c">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connect()
: <a class="el" href="_m_q_t_t_async_8h.html#a0388b226a414b09fa733f6d65004ec32">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connectData_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a2e415e68016ae56f6bbbbdc9840a9c6e">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connected
: <a class="el" href="_m_q_t_t_async_8h.html#a34bb8d321e9d368780b5c832c058f223">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connectionLost
: <a class="el" href="_m_q_t_t_async_8h.html#a3900a98d7b1d58ad6e686bfce298bb6c">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connectOptions_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#ae18b51f22784a43803eb809d6a0c2492">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connectOptions_initializer5
: <a class="el" href="_m_q_t_t_async_8h.html#abd403ce21f7aa0348ae1d3eefd031a5d">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connectOptions_initializer5_ws
: <a class="el" href="_m_q_t_t_async_8h.html#a513bfbec7b7d39c827240db75aa4044b">MQTTAsync.h</a>
</li>
<li>MQTTAsync_connectOptions_initializer_ws
: <a class="el" href="_m_q_t_t_async_8h.html#a080951d916d7a58c4ceff8c6bacfe313">MQTTAsync.h</a>
</li>
<li>MQTTAsync_create()
: <a class="el" href="_m_q_t_t_async_8h.html#a5462c4618d0a229116db5fbadacf95d2">MQTTAsync.h</a>
</li>
<li>MQTTAsync_createOptions_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a5fedeafef4753f09b1bcb92773564786">MQTTAsync.h</a>
</li>
<li>MQTTAsync_createOptions_initializer5
: <a class="el" href="_m_q_t_t_async_8h.html#a0008776a46e7268ccbef4774ce3d4579">MQTTAsync.h</a>
</li>
<li>MQTTAsync_createWithOptions()
: <a class="el" href="_m_q_t_t_async_8h.html#a78cbe1b851fea48001112f7ba9e4ea62">MQTTAsync.h</a>
</li>
<li>MQTTAsync_deliveryComplete
: <a class="el" href="_m_q_t_t_async_8h.html#ab10296618e266b3c02fd117d6616b15d">MQTTAsync.h</a>
</li>
<li>MQTTAsync_destroy()
: <a class="el" href="_m_q_t_t_async_8h.html#ad5562f9dc71fbd93d25ad20b328cb887">MQTTAsync.h</a>
</li>
<li>MQTTAsync_disconnect()
: <a class="el" href="_m_q_t_t_async_8h.html#adc69afa4725f8321bdaa5a05aec5cfd5">MQTTAsync.h</a>
</li>
<li>MQTTAsync_disconnected
: <a class="el" href="_m_q_t_t_async_8h.html#a52a1d9ab6e5d5064a3de42d0eec88f57">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_DISCONNECTED
: <a class="el" href="_m_q_t_t_async_8h.html#a66c0f30b329bc770145c2f04b3929df6">MQTTAsync.h</a>
</li>
<li>MQTTAsync_disconnectOptions_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a2fd5d6df31928ae468f3f2e522b9c707">MQTTAsync.h</a>
</li>
<li>MQTTAsync_disconnectOptions_initializer5
: <a class="el" href="_m_q_t_t_async_8h.html#aaa278001953dc129003eff83c8e7b3db">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_FAILURE
: <a class="el" href="_m_q_t_t_async_8h.html#a7c8230fef85fc04b8a1035501f3be406">MQTTAsync.h</a>
</li>
<li>MQTTAsync_failureData5_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a53ce2002ae2c2579575bb41c48c51c29">MQTTAsync.h</a>
</li>
<li>MQTTAsync_free()
: <a class="el" href="_m_q_t_t_async_8h.html#a2b836f58612a2c4627e40ae848da190d">MQTTAsync.h</a>
</li>
<li>MQTTAsync_freeMessage()
: <a class="el" href="_m_q_t_t_async_8h.html#a9b45db63052fe29ab1fad22d2a00c91c">MQTTAsync.h</a>
</li>
<li>MQTTAsync_getPendingTokens()
: <a class="el" href="_m_q_t_t_async_8h.html#abc92f60743fc471643b473abbc987be0">MQTTAsync.h</a>
</li>
<li>MQTTAsync_getVersionInfo()
: <a class="el" href="_m_q_t_t_async_8h.html#a7cf29b785a1d4ff1de2e67e2f916b658">MQTTAsync.h</a>
</li>
<li>MQTTAsync_global_init()
: <a class="el" href="_m_q_t_t_async_8h.html#a1705e75a48999cb45bf85c15608478f5">MQTTAsync.h</a>
</li>
<li>MQTTAsync_init_options_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a866e023f70141969d48597930c0ee313">MQTTAsync.h</a>
</li>
<li>MQTTAsync_isComplete()
: <a class="el" href="_m_q_t_t_async_8h.html#ab207095cab6f9a48b52cdb593b8456f4">MQTTAsync.h</a>
</li>
<li>MQTTAsync_isConnected()
: <a class="el" href="_m_q_t_t_async_8h.html#a46c332245c379629ae11f457fc179457">MQTTAsync.h</a>
</li>
<li>MQTTAsync_malloc()
: <a class="el" href="_m_q_t_t_async_8h.html#af5500ba58592afafaade2fcabdc61e61">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_MAX_BUFFERED
: <a class="el" href="_m_q_t_t_async_8h.html#a2efee8e190e2c3690c680bde060f78ab">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_MAX_BUFFERED_MESSAGES
: <a class="el" href="_m_q_t_t_async_8h.html#a4e338072cfd5291b579e4f0c99a6e773">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_MAX_MESSAGES_INFLIGHT
: <a class="el" href="_m_q_t_t_async_8h.html#ad577286d43c72fbc49818aac42f4e24a">MQTTAsync.h</a>
</li>
<li>MQTTAsync_message_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a6a85061dadab532f28e96e5ab3c600e9">MQTTAsync.h</a>
</li>
<li>MQTTAsync_messageArrived
: <a class="el" href="_m_q_t_t_async_8h.html#a3918ead59b56816a8d7544def184e48e">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_NO_MORE_MSGIDS
: <a class="el" href="_m_q_t_t_async_8h.html#ab0f54d0bae2c74849022a8009e5d6ff7">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_NULL_PARAMETER
: <a class="el" href="_m_q_t_t_async_8h.html#ab88e1ebcee991099a72429e52a8253fd">MQTTAsync.h</a>
</li>
<li>MQTTAsync_onFailure
: <a class="el" href="_m_q_t_t_async_8h.html#a6060c25c2641e878803aef76fefb31ee">MQTTAsync.h</a>
</li>
<li>MQTTAsync_onFailure5
: <a class="el" href="_m_q_t_t_async_8h.html#a8c5023e04d5c3e9805d5dae76df21f4c">MQTTAsync.h</a>
</li>
<li>MQTTAsync_onSuccess
: <a class="el" href="_m_q_t_t_async_8h.html#a7b0c18a0e29e2ce73f3ea109bc32617b">MQTTAsync.h</a>
</li>
<li>MQTTAsync_onSuccess5
: <a class="el" href="_m_q_t_t_async_8h.html#a892cf122e6e8d8f6cd38c4c8efe8fb67">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_OPERATION_INCOMPLETE
: <a class="el" href="_m_q_t_t_async_8h.html#aee1b79d0632bec0fe49eb7ea1abd3b2e">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_PERSISTENCE_ERROR
: <a class="el" href="_m_q_t_t_async_8h.html#a4edf1249c75abd4975fec8ddeae2cdc9">MQTTAsync.h</a>
</li>
<li>MQTTAsync_reconnect()
: <a class="el" href="_m_q_t_t_async_8h.html#abd3ea01869b89ff23f9522640479c395">MQTTAsync.h</a>
</li>
<li>MQTTAsync_responseOptions
: <a class="el" href="_m_q_t_t_async_8h.html#ae1568d96d6418004cc79466c06f3d791">MQTTAsync.h</a>
</li>
<li>MQTTAsync_responseOptions_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a3f8b408243b5c2369bc9758f2edf0878">MQTTAsync.h</a>
</li>
<li>MQTTAsync_send()
: <a class="el" href="_m_q_t_t_async_8h.html#a63c66a311ab16239a4175ff671871bf2">MQTTAsync.h</a>
</li>
<li>MQTTAsync_sendMessage()
: <a class="el" href="_m_q_t_t_async_8h.html#a5687171e67e98f9ea590c9e3b64cde18">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setAfterPersistenceRead()
: <a class="el" href="_m_q_t_t_async_8h.html#ab4d16e3c57502be6a7d1b1d3bcc382f3">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setBeforePersistenceWrite()
: <a class="el" href="_m_q_t_t_async_8h.html#a1002b09c62a096578c9b3e0135eb98c1">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setCallbacks()
: <a class="el" href="_m_q_t_t_async_8h.html#ae9ae8d61023e7029ef5a19f5219c3599">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setConnected()
: <a class="el" href="_m_q_t_t_async_8h.html#a18cc19740d9b00c629dc53a4420ecf1f">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setConnectionLostCallback()
: <a class="el" href="_m_q_t_t_async_8h.html#aee15bbd9224efd9dcce9b4ae491b2e2e">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setDeliveryCompleteCallback()
: <a class="el" href="_m_q_t_t_async_8h.html#a94ec624ee22cc01d2ca58a9e646a2665">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setDisconnected()
: <a class="el" href="_m_q_t_t_async_8h.html#ada4dd26d23c8849c51e4ab8200339040">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setMessageArrivedCallback()
: <a class="el" href="_m_q_t_t_async_8h.html#a44abc360051b918a39b0596a137775ae">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setTraceCallback()
: <a class="el" href="_m_q_t_t_async_8h.html#a0b350581324a4ff0eaee71e7a6721388">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setTraceLevel()
: <a class="el" href="_m_q_t_t_async_8h.html#ac7fbab13a0b2e5dd4ee11efbbb9f6a3a">MQTTAsync.h</a>
</li>
<li>MQTTAsync_setUpdateConnectOptions()
: <a class="el" href="_m_q_t_t_async_8h.html#aa078aec3eba83481f63db3c3939a5da9">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_SSL_NOT_SUPPORTED
: <a class="el" href="_m_q_t_t_async_8h.html#a6992c00553db1608aef9e162c161d73c">MQTTAsync.h</a>
</li>
<li>MQTTAsync_SSLOptions_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#aac935e2e9d770a53ee8189f128530511">MQTTAsync.h</a>
</li>
<li>MQTTAsync_strerror()
: <a class="el" href="_m_q_t_t_async_8h.html#a875cd089a8b23eb3fd50c0406fc75d9f">MQTTAsync.h</a>
</li>
<li>MQTTAsync_subscribe()
: <a class="el" href="_m_q_t_t_async_8h.html#ae10bd009934b3bb4a9f4abae7424a611">MQTTAsync.h</a>
</li>
<li>MQTTAsync_subscribeMany()
: <a class="el" href="_m_q_t_t_async_8h.html#ac78620b33434a187255bd1a3faec1578">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_SUCCESS
: <a class="el" href="_m_q_t_t_async_8h.html#afe0cffcce8efe25186f79c51ac44e16f">MQTTAsync.h</a>
</li>
<li>MQTTAsync_successData5_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a6182ec90ec4a134465f627b324ac5a41">MQTTAsync.h</a>
</li>
<li>MQTTAsync_token
: <a class="el" href="_m_q_t_t_async_8h.html#a7ca6d2a1813f2bbd0bc3af2771e46ba4">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TOPICNAME_TRUNCATED
: <a class="el" href="_m_q_t_t_async_8h.html#a77a7106d97ff60be3fe70f90b1867800">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_ERROR
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5ac428f74ca453dacb7b8271ca741266e8">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_FATAL
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a0b91d2213ebb6655e41a7f6ce1a42295">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_LEVELS
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_MAXIMUM
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5af684f42971cced68693ce993703548c1">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_MEDIUM
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a133c380b84d75477ff31a2ad732133ce">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_MINIMUM
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a7a45c26816b1cac1fde02d79a9f4337b">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_PROTOCOL
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a6a719b2b7fc4dfc41494370ff96fec3e">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRACE_SEVERE
: <a class="el" href="_m_q_t_t_async_8h.html#a5de816f986b318947709a34e0787eda5a3084770185f384398cefe4aaba533d40">MQTTAsync.h</a>
</li>
<li>MQTTAsync_traceCallback
: <a class="el" href="_m_q_t_t_async_8h.html#a65aba1caeae9b5af5d5b6c5598a75b02">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_TRUE
: <a class="el" href="_m_q_t_t_async_8h.html#a61e6ee632e63312d382e2fcbe427f01a">MQTTAsync.h</a>
</li>
<li>MQTTAsync_unsubscribe()
: <a class="el" href="_m_q_t_t_async_8h.html#a08d18ece91c1b011011354570d8ac1ab">MQTTAsync.h</a>
</li>
<li>MQTTAsync_unsubscribeMany()
: <a class="el" href="_m_q_t_t_async_8h.html#a69fd433ce1b9b6a1b3b453c4793a9311">MQTTAsync.h</a>
</li>
<li>MQTTAsync_updateConnectOptions
: <a class="el" href="_m_q_t_t_async_8h.html#a5e44304a2c011a7d61b72c779ad83979">MQTTAsync.h</a>
</li>
<li>MQTTAsync_waitForCompletion()
: <a class="el" href="_m_q_t_t_async_8h.html#a4fe09cc9c976b1cf424e13765d6cd8c9">MQTTAsync.h</a>
</li>
<li>MQTTAsync_willOptions_initializer
: <a class="el" href="_m_q_t_t_async_8h.html#a6c45768e1b28844f2ac0f6ac68709730">MQTTAsync.h</a>
</li>
<li>MQTTASYNC_WRONG_MQTT_VERSION
: <a class="el" href="_m_q_t_t_async_8h.html#af5df806e9767e1e3182fe089a8ee551b">MQTTAsync.h</a>
</li>
<li>MQTTCLIENT_PERSISTENCE_DEFAULT
: <a class="el" href="_m_q_t_t_client_persistence_8h.html#aaa948291718a9c06369b854b0f64bc32">MQTTClientPersistence.h</a>
</li>
<li>MQTTCLIENT_PERSISTENCE_ERROR
: <a class="el" href="_m_q_t_t_client_persistence_8h.html#ab716e21e53c84a5ad62aa962a2a8f7db">MQTTClientPersistence.h</a>
</li>
<li>MQTTCLIENT_PERSISTENCE_NONE
: <a class="el" href="_m_q_t_t_client_persistence_8h.html#ae01e089313a65ac4661ed216b6ac00fa">MQTTClientPersistence.h</a>
</li>
<li>MQTTCLIENT_PERSISTENCE_USER
: <a class="el" href="_m_q_t_t_client_persistence_8h.html#a5dc68b8616e4041e037bad94ce07681b">MQTTClientPersistence.h</a>
</li>
<li>MQTTPersistence_afterRead
: <a class="el" href="_m_q_t_t_client_persistence_8h.html#af5a966a574c6ad7a35f1ebb7edd5c1c4">MQTTClientPersistence.h</a>
</li>
<li>MQTTPersistence_beforeWrite
: <a class="el" href="_m_q_t_t_client_persistence_8h.html#ab865640a1cc53b68622004c5a2d29fae">MQTTClientPersistence.h</a>
</li>
<li>MQTTProperties
: <a class="el" href="_m_q_t_t_properties_8h.html#a7758f1a5eceb6f46c8540630e39e2fb4">MQTTProperties.h</a>
</li>
<li>MQTTProperties_add()
: <a class="el" href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">MQTTProperties.h</a>
</li>
<li>MQTTProperties_copy()
: <a class="el" href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">MQTTProperties.h</a>
</li>
<li>MQTTProperties_free()
: <a class="el" href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">MQTTProperties.h</a>
</li>
<li>MQTTProperties_getNumericValue()
: <a class="el" href="_m_q_t_t_properties_8h.html#afa777eb796532a3c0c59ddc40aca3792">MQTTProperties.h</a>
</li>
<li>MQTTProperties_getNumericValueAt()
: <a class="el" href="_m_q_t_t_properties_8h.html#abc6bc80d9bb644c9b4a66b6808829a53">MQTTProperties.h</a>
</li>
<li>MQTTProperties_getProperty()
: <a class="el" href="_m_q_t_t_properties_8h.html#a788b033cbed8a7906799fccbb1463f20">MQTTProperties.h</a>
</li>
<li>MQTTProperties_getPropertyAt()
: <a class="el" href="_m_q_t_t_properties_8h.html#ac6942d8fafb5794591fc558b4bb06beb">MQTTProperties.h</a>
</li>
<li>MQTTProperties_hasProperty()
: <a class="el" href="_m_q_t_t_properties_8h.html#a4ccdf6a712c3d803a16a4d3f00f9be2f">MQTTProperties.h</a>
</li>
<li>MQTTProperties_initializer
: <a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties.h</a>
</li>
<li>MQTTProperties_len()
: <a class="el" href="_m_q_t_t_properties_8h.html#ab7ad6f14e2dc7576d35827a28b0dc81d">MQTTProperties.h</a>
</li>
<li>MQTTProperties_propertyCount()
: <a class="el" href="_m_q_t_t_properties_8h.html#a6f0d8b230c2e7008c7639cfce1c04429">MQTTProperties.h</a>
</li>
<li>MQTTProperties_read()
: <a class="el" href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">MQTTProperties.h</a>
</li>
<li>MQTTProperties_write()
: <a class="el" href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_AUTHENTICATION_DATA
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_AUTHENTICATION_METHOD
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_CONTENT_TYPE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_CORRELATION_DATA
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_MAXIMUM_QOS
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_REASON_STRING
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_RECEIVE_MAXIMUM
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_RESPONSE_INFORMATION
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_RESPONSE_TOPIC
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_RETAIN_AVAILABLE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_SERVER_REFERENCE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_TOPIC_ALIAS
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_USER_PROPERTY
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef">MQTTProperties.h</a>
</li>
<li>MQTTProperty_getType()
: <a class="el" href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_TYPE_BINARY_DATA
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_TYPE_BYTE
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214">MQTTProperties.h</a>
</li>
<li>MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7">MQTTProperties.h</a>
</li>
<li>MQTTPropertyCodes
: <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTProperties.h</a>
</li>
<li>MQTTPropertyName()
: <a class="el" href="_m_q_t_t_properties_8h.html#afa0a7306d7b6174c5bc417ca49d99851">MQTTProperties.h</a>
</li>
<li>MQTTPropertyTypes
: <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958">MQTTProperties.h</a>
</li>
<li>MQTTREASONCODE_ADMINISTRATIVE_ACTION
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_BAD_AUTHENTICATION_METHOD
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_BANNED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_CONNECTION_RATE_EXCEEDED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_CONTINUE_AUTHENTICATION
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_GRANTED_QOS_0
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_GRANTED_QOS_1
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_GRANTED_QOS_2
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_KEEP_ALIVE_TIMEOUT
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_MALFORMED_PACKET
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_MAXIMUM_CONNECT_TIME
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_NO_SUBSCRIPTION_FOUND
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_NORMAL_DISCONNECTION
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_NOT_AUTHORIZED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_PACKET_TOO_LARGE
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_PAYLOAD_FORMAT_INVALID
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_PROTOCOL_ERROR
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_QOS_NOT_SUPPORTED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_QUOTA_EXCEEDED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_RE_AUTHENTICATE
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_RETAIN_NOT_SUPPORTED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SERVER_BUSY
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SERVER_MOVED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SERVER_SHUTTING_DOWN
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SERVER_UNAVAILABLE
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SESSION_TAKEN_OVER
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_SUCCESS
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_TOPIC_ALIAS_INVALID
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_TOPIC_FILTER_INVALID
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_TOPIC_NAME_INVALID
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac">MQTTReasonCodes.h</a>
</li>
<li>MQTTReasonCode_toString()
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#a91922a5e3c1e5ec7670b6e296854f1b7">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_UNSPECIFIED_ERROR
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_USE_ANOTHER_SERVER
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c">MQTTReasonCodes.h</a>
</li>
<li>MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f">MQTTReasonCodes.h</a>
</li>
<li>MQTTReasonCodes
: <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes.h</a>
</li>
<li>MQTTSubscribe_options
: <a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aa68db3eaed272ae1aaea294401079d8a">MQTTSubscribeOpts.h</a>
</li>
<li>MQTTSubscribe_options_initializer
: <a class="el" href="_m_q_t_t_subscribe_opts_8h.html#aec3b45fd0367106eea344396f87cfda7">MQTTSubscribeOpts.h</a>
</li>
<li>MQTTVERSION_3_1
: <a class="el" href="_m_q_t_t_async_8h.html#a4603b988e76872e1f23f135d225ce2fb">MQTTAsync.h</a>
</li>
<li>MQTTVERSION_3_1_1
: <a class="el" href="_m_q_t_t_async_8h.html#ac79cc6fdeaa9e3f4ee12c3418898b1ef">MQTTAsync.h</a>
</li>
<li>MQTTVERSION_5
: <a class="el" href="_m_q_t_t_async_8h.html#af8b176fa4d5b89789767ce972338e1e3">MQTTAsync.h</a>
</li>
<li>MQTTVERSION_DEFAULT
: <a class="el" href="_m_q_t_t_async_8h.html#a75b80b01f98d5a1ffa2a4d42995a8397">MQTTAsync.h</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:45 for Paho Asynchronous MQTT C Client Library by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
