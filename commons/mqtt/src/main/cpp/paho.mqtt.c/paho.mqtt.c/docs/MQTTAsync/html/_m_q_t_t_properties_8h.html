<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTProperties.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">MQTTProperties.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;MQTTExportDeclarations.h&quot;</code><br />
</div>
<p><a href="_m_q_t_t_properties_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_len_string.html">MQTTLenString</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:afc56d2e8937a0c8f180d68ad93945945"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#afc56d2e8937a0c8f180d68ad93945945">MQTT_INVALID_PROPERTY_ID</a>&#160;&#160;&#160;-2</td></tr>
<tr class="separator:afc56d2e8937a0c8f180d68ad93945945"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a80e158486a414ccdfcdd7f75f23988"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a5a80e158486a414ccdfcdd7f75f23988">MQTTProperties_initializer</a>&#160;&#160;&#160;{0, 0, 0, NULL}</td></tr>
<tr class="separator:a5a80e158486a414ccdfcdd7f75f23988"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a7758f1a5eceb6f46c8540630e39e2fb4"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a7758f1a5eceb6f46c8540630e39e2fb4">MQTTProperties</a></td></tr>
<tr class="separator:a7758f1a5eceb6f46c8540630e39e2fb4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:af623c1b670dfe3fda633c068e054d8b4"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> { <br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d">MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR</a> = 1, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a">MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL</a> = 2, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198">MQTTPROPERTY_CODE_CONTENT_TYPE</a> = 3, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5">MQTTPROPERTY_CODE_RESPONSE_TOPIC</a> = 8, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5">MQTTPROPERTY_CODE_CORRELATION_DATA</a> = 9, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96">MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER</a> = 11, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766">MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL</a> = 17, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672">MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER</a> = 18, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7">MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE</a> = 19, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100">MQTTPROPERTY_CODE_AUTHENTICATION_METHOD</a> = 21, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e">MQTTPROPERTY_CODE_AUTHENTICATION_DATA</a> = 22, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1">MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION</a> = 23, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef">MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL</a> = 24, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf">MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION</a> = 25, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00">MQTTPROPERTY_CODE_RESPONSE_INFORMATION</a> = 26, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4">MQTTPROPERTY_CODE_SERVER_REFERENCE</a> = 28, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1">MQTTPROPERTY_CODE_REASON_STRING</a> = 31, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f">MQTTPROPERTY_CODE_RECEIVE_MAXIMUM</a> = 33, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23">MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM</a> = 34, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee">MQTTPROPERTY_CODE_TOPIC_ALIAS</a> = 35, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59">MQTTPROPERTY_CODE_MAXIMUM_QOS</a> = 36, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c">MQTTPROPERTY_CODE_RETAIN_AVAILABLE</a> = 37, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8">MQTTPROPERTY_CODE_USER_PROPERTY</a> = 38, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5">MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE</a> = 39, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9">MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE</a> = 40, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505">MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE</a> = 41, 
<a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636">MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE</a> = 42
<br />
 }</td></tr>
<tr class="separator:af623c1b670dfe3fda633c068e054d8b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a942f52ef7c232829f6df5c86e07cc958"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958">MQTTPropertyTypes</a> { <br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a">MQTTPROPERTY_TYPE_BYTE</a>, 
<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6">MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER</a>, 
<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15">MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER</a>, 
<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7">MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER</a>, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4">MQTTPROPERTY_TYPE_BINARY_DATA</a>, 
<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1">MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING</a>, 
<a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214">MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR</a>
<br />
 }</td></tr>
<tr class="separator:a942f52ef7c232829f6df5c86e07cc958"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:afa0a7306d7b6174c5bc417ca49d99851"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#afa0a7306d7b6174c5bc417ca49d99851">MQTTPropertyName</a> (enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> value)</td></tr>
<tr class="separator:afa0a7306d7b6174c5bc417ca49d99851"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d30ad0520bc9b9366e700d4b493b173"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a7d30ad0520bc9b9366e700d4b493b173">MQTTProperty_getType</a> (enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> value)</td></tr>
<tr class="separator:a7d30ad0520bc9b9366e700d4b493b173"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7ad6f14e2dc7576d35827a28b0dc81d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#ab7ad6f14e2dc7576d35827a28b0dc81d">MQTTProperties_len</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props)</td></tr>
<tr class="separator:ab7ad6f14e2dc7576d35827a28b0dc81d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88f1d21556c2d23330d71357cd226a15"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a88f1d21556c2d23330d71357cd226a15">MQTTProperties_add</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, const <a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a> *prop)</td></tr>
<tr class="separator:a88f1d21556c2d23330d71357cd226a15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade0027a4e571bd288fe40271ff7aa497"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#ade0027a4e571bd288fe40271ff7aa497">MQTTProperties_write</a> (char **pptr, const <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties)</td></tr>
<tr class="separator:ade0027a4e571bd288fe40271ff7aa497"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afcb874dfcc9f0eaa0b063e2fad740871"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#afcb874dfcc9f0eaa0b063e2fad740871">MQTTProperties_read</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties, char **pptr, char *enddata)</td></tr>
<tr class="separator:afcb874dfcc9f0eaa0b063e2fad740871"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab68247ed365ee51170a9309c828b1823"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#ab68247ed365ee51170a9309c828b1823">MQTTProperties_free</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *properties)</td></tr>
<tr class="separator:ab68247ed365ee51170a9309c828b1823"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69b3e474ee2f828e5b827d615fe0fe72"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a69b3e474ee2f828e5b827d615fe0fe72">MQTTProperties_copy</a> (const <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props)</td></tr>
<tr class="separator:a69b3e474ee2f828e5b827d615fe0fe72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ccdf6a712c3d803a16a4d3f00f9be2f"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a4ccdf6a712c3d803a16a4d3f00f9be2f">MQTTProperties_hasProperty</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid)</td></tr>
<tr class="separator:a4ccdf6a712c3d803a16a4d3f00f9be2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f0d8b230c2e7008c7639cfce1c04429"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a6f0d8b230c2e7008c7639cfce1c04429">MQTTProperties_propertyCount</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid)</td></tr>
<tr class="separator:a6f0d8b230c2e7008c7639cfce1c04429"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa777eb796532a3c0c59ddc40aca3792"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#afa777eb796532a3c0c59ddc40aca3792">MQTTProperties_getNumericValue</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid)</td></tr>
<tr class="separator:afa777eb796532a3c0c59ddc40aca3792"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc6bc80d9bb644c9b4a66b6808829a53"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#abc6bc80d9bb644c9b4a66b6808829a53">MQTTProperties_getNumericValueAt</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid, int index)</td></tr>
<tr class="separator:abc6bc80d9bb644c9b4a66b6808829a53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a788b033cbed8a7906799fccbb1463f20"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#a788b033cbed8a7906799fccbb1463f20">MQTTProperties_getProperty</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid)</td></tr>
<tr class="separator:a788b033cbed8a7906799fccbb1463f20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6942d8fafb5794591fc558b4bb06beb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_properties_8h.html#ac6942d8fafb5794591fc558b4bb06beb">MQTTProperties_getPropertyAt</a> (<a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *props, enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a> propid, int index)</td></tr>
<tr class="separator:ac6942d8fafb5794591fc558b4bb06beb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="afc56d2e8937a0c8f180d68ad93945945"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afc56d2e8937a0c8f180d68ad93945945">&#9670;&nbsp;</a></span>MQTT_INVALID_PROPERTY_ID</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTT_INVALID_PROPERTY_ID&#160;&#160;&#160;-2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5a80e158486a414ccdfcdd7f75f23988"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5a80e158486a414ccdfcdd7f75f23988">&#9670;&nbsp;</a></span>MQTTProperties_initializer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MQTTProperties_initializer&#160;&#160;&#160;{0, 0, 0, NULL}</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="a7758f1a5eceb6f46c8540630e39e2fb4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7758f1a5eceb6f46c8540630e39e2fb4">&#9670;&nbsp;</a></span>MQTTProperties</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>MQTT version 5 property list </p>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="af623c1b670dfe3fda633c068e054d8b4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af623c1b670dfe3fda633c068e054d8b4">&#9670;&nbsp;</a></span>MQTTPropertyCodes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The one byte MQTT V5 property indicator </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4ae5d077520427d03b44096f631411575d"></a>MQTTPROPERTY_CODE_PAYLOAD_FORMAT_INDICATOR&#160;</td><td class="fielddoc"><p>The value is 1 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a284c0e62d47ee8d358b16a8075632b4a"></a>MQTTPROPERTY_CODE_MESSAGE_EXPIRY_INTERVAL&#160;</td><td class="fielddoc"><p>The value is 2 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a4027d9e0fb53a62ae35963e700b56198"></a>MQTTPROPERTY_CODE_CONTENT_TYPE&#160;</td><td class="fielddoc"><p>The value is 3 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a7fa9996eef721d318504fbb0a8d4bac5"></a>MQTTPROPERTY_CODE_RESPONSE_TOPIC&#160;</td><td class="fielddoc"><p>The value is 8 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a887d3dd3f0ce31255324f5a1ba8b72c5"></a>MQTTPROPERTY_CODE_CORRELATION_DATA&#160;</td><td class="fielddoc"><p>The value is 9 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a70ead9c93f06396a4d9469b65bff0c96"></a>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIER&#160;</td><td class="fielddoc"><p>The value is 11 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a22e4caa63f63ca3f9b1c1330711ee766"></a>MQTTPROPERTY_CODE_SESSION_EXPIRY_INTERVAL&#160;</td><td class="fielddoc"><p>The value is 17 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a768d84858fd18d5d5a7dee394929c672"></a>MQTTPROPERTY_CODE_ASSIGNED_CLIENT_IDENTIFER&#160;</td><td class="fielddoc"><p>The value is 18 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4ab106f320e7537b79644f25d3efcd68c7"></a>MQTTPROPERTY_CODE_SERVER_KEEP_ALIVE&#160;</td><td class="fielddoc"><p>The value is 19 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a7c53f1e414b577d787b5d51af3204100"></a>MQTTPROPERTY_CODE_AUTHENTICATION_METHOD&#160;</td><td class="fielddoc"><p>The value is 21 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4abdf9feec165aceefbe7aa46764f6ab6e"></a>MQTTPROPERTY_CODE_AUTHENTICATION_DATA&#160;</td><td class="fielddoc"><p>The value is 22 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a3954daf1d5772b5d56eefa1ab6a28aa1"></a>MQTTPROPERTY_CODE_REQUEST_PROBLEM_INFORMATION&#160;</td><td class="fielddoc"><p>The value is 23 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a53fd81bc554f152a2772d282be7ce5ef"></a>MQTTPROPERTY_CODE_WILL_DELAY_INTERVAL&#160;</td><td class="fielddoc"><p>The value is 24 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a420b882a337dc1fd5f336ac6cd0529bf"></a>MQTTPROPERTY_CODE_REQUEST_RESPONSE_INFORMATION&#160;</td><td class="fielddoc"><p>The value is 25 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a2584b050f016af496c7f0b46692dbc00"></a>MQTTPROPERTY_CODE_RESPONSE_INFORMATION&#160;</td><td class="fielddoc"><p>The value is 26 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a0168e8a59f7994c02b7a7fd2fc3735c4"></a>MQTTPROPERTY_CODE_SERVER_REFERENCE&#160;</td><td class="fielddoc"><p>The value is 28 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a3dce8f679474e901ce4aec076e9e59e1"></a>MQTTPROPERTY_CODE_REASON_STRING&#160;</td><td class="fielddoc"><p>The value is 31 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4ab2688fe8d7d263c27c00d41776cb8f9f"></a>MQTTPROPERTY_CODE_RECEIVE_MAXIMUM&#160;</td><td class="fielddoc"><p>The value is 33 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a0a0b0b0715ecc9ccf471c75aa4c21c23"></a>MQTTPROPERTY_CODE_TOPIC_ALIAS_MAXIMUM&#160;</td><td class="fielddoc"><p>The value is 34 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4ad4dfb37d341ea190afc144668e5e3bee"></a>MQTTPROPERTY_CODE_TOPIC_ALIAS&#160;</td><td class="fielddoc"><p>The value is 35 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a506faeb89c407cf78853c777d750fa59"></a>MQTTPROPERTY_CODE_MAXIMUM_QOS&#160;</td><td class="fielddoc"><p>The value is 36 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a448b3a40afaa5f7195701e7dc8bed30c"></a>MQTTPROPERTY_CODE_RETAIN_AVAILABLE&#160;</td><td class="fielddoc"><p>The value is 37 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a596ff540370235d3eca693ce30dd4af8"></a>MQTTPROPERTY_CODE_USER_PROPERTY&#160;</td><td class="fielddoc"><p>The value is 38 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a6834ea9878f028d5fbdeccaaeae492e5"></a>MQTTPROPERTY_CODE_MAXIMUM_PACKET_SIZE&#160;</td><td class="fielddoc"><p>The value is 39 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4ad05993f90baaee0ba7094ccef4d378b9"></a>MQTTPROPERTY_CODE_WILDCARD_SUBSCRIPTION_AVAILABLE&#160;</td><td class="fielddoc"><p>The value is 40 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4a8b366cfd8bd3f388bafb67f3ebf83505"></a>MQTTPROPERTY_CODE_SUBSCRIPTION_IDENTIFIERS_AVAILABLE&#160;</td><td class="fielddoc"><p>The value is 41 </p>
</td></tr>
<tr><td class="fieldname"><a id="af623c1b670dfe3fda633c068e054d8b4ae04a7356f9e11654f15a3b21f2aae636"></a>MQTTPROPERTY_CODE_SHARED_SUBSCRIPTION_AVAILABLE&#160;</td><td class="fielddoc"><p>The value is 241 </p>
</td></tr>
</table>

</div>
</div>
<a id="a942f52ef7c232829f6df5c86e07cc958"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a942f52ef7c232829f6df5c86e07cc958">&#9670;&nbsp;</a></span>MQTTPropertyTypes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_properties_8h.html#a942f52ef7c232829f6df5c86e07cc958">MQTTPropertyTypes</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The one byte MQTT V5 property type </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a942f52ef7c232829f6df5c86e07cc958ac36f96ce58c98a8ebbe0783df030726a"></a>MQTTPROPERTY_TYPE_BYTE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a942f52ef7c232829f6df5c86e07cc958ae301a9e68326cc2d8bfefeca401e78e6"></a>MQTTPROPERTY_TYPE_TWO_BYTE_INTEGER&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a942f52ef7c232829f6df5c86e07cc958aa49c558733bd735ae872fd87ad0d7e15"></a>MQTTPROPERTY_TYPE_FOUR_BYTE_INTEGER&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a942f52ef7c232829f6df5c86e07cc958a27bbcb5bc4f584f96612c0cec329c6a7"></a>MQTTPROPERTY_TYPE_VARIABLE_BYTE_INTEGER&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a942f52ef7c232829f6df5c86e07cc958a6643aed682b9b07f98159856776fe7b4"></a>MQTTPROPERTY_TYPE_BINARY_DATA&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a942f52ef7c232829f6df5c86e07cc958ad45c866a5bef6c5048a7af21405734d1"></a>MQTTPROPERTY_TYPE_UTF_8_ENCODED_STRING&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a942f52ef7c232829f6df5c86e07cc958a28ab5fe5b159f3b3a8884b0f61527214"></a>MQTTPROPERTY_TYPE_UTF_8_STRING_PAIR&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="afa0a7306d7b6174c5bc417ca49d99851"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa0a7306d7b6174c5bc417ca49d99851">&#9670;&nbsp;</a></span>MQTTPropertyName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* MQTTPropertyName </td>
          <td>(</td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a printable string description of an MQTT V5 property code. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td>an MQTT V5 property code. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the printable string description of the input property code. NULL if the code was not found. </dd></dl>

</div>
</div>
<a id="a7d30ad0520bc9b9366e700d4b493b173"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7d30ad0520bc9b9366e700d4b493b173">&#9670;&nbsp;</a></span>MQTTProperty_getType()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperty_getType </td>
          <td>(</td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the MQTT V5 type code of an MQTT V5 property. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td>an MQTT V5 property code. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the MQTT V5 type code of the input property. -1 if the code was not found. </dd></dl>

</div>
</div>
<a id="ab7ad6f14e2dc7576d35827a28b0dc81d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7ad6f14e2dc7576d35827a28b0dc81d">&#9670;&nbsp;</a></span>MQTTProperties_len()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_len </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the length of the properties structure when serialized ready for network transmission. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>an MQTT V5 property structure. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the length in bytes of the properties when serialized. </dd></dl>

</div>
</div>
<a id="a88f1d21556c2d23330d71357cd226a15"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a88f1d21556c2d23330d71357cd226a15">&#9670;&nbsp;</a></span>MQTTProperties_add()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_add </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a> *&#160;</td>
          <td class="paramname"><em>prop</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add a property pointer to the property array. There is no memory allocation. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>The property list to add the property to. </td></tr>
    <tr><td class="paramname">prop</td><td>The property to add to the list. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>0 on success, -1 on failure. </dd></dl>

</div>
</div>
<a id="ade0027a4e571bd288fe40271ff7aa497"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade0027a4e571bd288fe40271ff7aa497">&#9670;&nbsp;</a></span>MQTTProperties_write()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_write </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>properties</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Serialize the given property list to a character buffer, e.g. for writing to the network. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the buffer - move the pointer as we add data </td></tr>
    <tr><td class="paramname">properties</td><td>pointer to the property list, can be NULL </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>whether the write succeeded or not: number of bytes written, or &lt; 0 on failure. </dd></dl>

</div>
</div>
<a id="afcb874dfcc9f0eaa0b063e2fad740871"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afcb874dfcc9f0eaa0b063e2fad740871">&#9670;&nbsp;</a></span>MQTTProperties_read()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_read </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>properties</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>enddata</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Reads a property list from a character buffer into an array. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">properties</td><td>pointer to the property list to be filled. Should be initalized but empty. </td></tr>
    <tr><td class="paramname">pptr</td><td>pointer to the character buffer. </td></tr>
    <tr><td class="paramname">enddata</td><td>pointer to the end of the character buffer so we don't read beyond. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>1 if the properties were read successfully. </dd></dl>

</div>
</div>
<a id="ab68247ed365ee51170a9309c828b1823"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab68247ed365ee51170a9309c828b1823">&#9670;&nbsp;</a></span>MQTTProperties_free()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTProperties_free </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>properties</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Free all memory allocated to the property list, including any to individual properties. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">properties</td><td>pointer to the property list. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a69b3e474ee2f828e5b827d615fe0fe72"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69b3e474ee2f828e5b827d615fe0fe72">&#9670;&nbsp;</a></span>MQTTProperties_copy()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> MQTTProperties_copy </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Copy the contents of a property list, allocating additional memory if needed. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>pointer to the property list. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the duplicated property list. </dd></dl>

</div>
</div>
<a id="a4ccdf6a712c3d803a16a4d3f00f9be2f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4ccdf6a712c3d803a16a4d3f00f9be2f">&#9670;&nbsp;</a></span>MQTTProperties_hasProperty()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_hasProperty </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>propid</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Checks if property list contains a specific property. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>pointer to the property list. </td></tr>
    <tr><td class="paramname">propid</td><td>the property id to check for. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>1 if found, 0 if not. </dd></dl>

</div>
</div>
<a id="a6f0d8b230c2e7008c7639cfce1c04429"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6f0d8b230c2e7008c7639cfce1c04429">&#9670;&nbsp;</a></span>MQTTProperties_propertyCount()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_propertyCount </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>propid</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the number of instances of a property id. Most properties can exist only once. User properties and subscription ids can exist more than once. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>pointer to the property list. </td></tr>
    <tr><td class="paramname">propid</td><td>the property id to check for. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the number of times found. Can be 0. </dd></dl>

</div>
</div>
<a id="afa777eb796532a3c0c59ddc40aca3792"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa777eb796532a3c0c59ddc40aca3792">&#9670;&nbsp;</a></span>MQTTProperties_getNumericValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_getNumericValue </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>propid</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the integer value of a specific property. The property given must be a numeric type. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>pointer to the property list. </td></tr>
    <tr><td class="paramname">propid</td><td>the property id to check for. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the integer value of the property. -9999999 on failure. </dd></dl>

</div>
</div>
<a id="abc6bc80d9bb644c9b4a66b6808829a53"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc6bc80d9bb644c9b4a66b6808829a53">&#9670;&nbsp;</a></span>MQTTProperties_getNumericValueAt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTProperties_getNumericValueAt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>propid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>index</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the integer value of a specific property when it's not the only instance. The property given must be a numeric type. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>pointer to the property list. </td></tr>
    <tr><td class="paramname">propid</td><td>the property id to check for. </td></tr>
    <tr><td class="paramname">index</td><td>the instance number, starting at 0. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the integer value of the property. -9999999 on failure. </dd></dl>

</div>
</div>
<a id="a788b033cbed8a7906799fccbb1463f20"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a788b033cbed8a7906799fccbb1463f20">&#9670;&nbsp;</a></span>MQTTProperties_getProperty()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a>* MQTTProperties_getProperty </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>propid</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a pointer to the property structure for a specific property. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>pointer to the property list. </td></tr>
    <tr><td class="paramname">propid</td><td>the property id to check for. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the pointer to the property structure if found. NULL if not found. </dd></dl>

</div>
</div>
<a id="ac6942d8fafb5794591fc558b4bb06beb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac6942d8fafb5794591fc558b4bb06beb">&#9670;&nbsp;</a></span>MQTTProperties_getPropertyAt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="struct_m_q_t_t_property.html">MQTTProperty</a>* MQTTProperties_getPropertyAt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="struct_m_q_t_t_properties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_properties_8h.html#af623c1b670dfe3fda633c068e054d8b4">MQTTPropertyCodes</a>&#160;</td>
          <td class="paramname"><em>propid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>index</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a pointer to the property structure for a specific property when it's not the only instance. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">props</td><td>pointer to the property list. </td></tr>
    <tr><td class="paramname">propid</td><td>the property id to check for. </td></tr>
    <tr><td class="paramname">index</td><td>the instance number, starting at 0. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the pointer to the property structure if found. NULL if not found. </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:45 for Paho Asynchronous MQTT C Client Library by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
