.TH "MQTTAsync.h" 3 "Thu Sep 29 2022" "Paho Asynchronous MQTT C Client Library" \" -*- nroff -*-
.ad l
.nh
.SH NAME
MQTTAsync.h
.SH SYNOPSIS
.br
.PP
\fC#include 'MQTTExportDeclarations\&.h'\fP
.br
\fC#include 'MQTTProperties\&.h'\fP
.br
\fC#include 'MQTTReasonCodes\&.h'\fP
.br
\fC#include 'MQTTSubscribeOpts\&.h'\fP
.br
\fC#include 'MQTTClientPersistence\&.h'\fP
.br

.SS "Data Structures"

.in +1c
.ti -1c
.RI "struct \fBMQTTAsync_init_options\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_message\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_connectData\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_failureData\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_failureData5\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_successData\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_successData5\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_responseOptions\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_createOptions\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_willOptions\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_SSLOptions\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_nameValue\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_connectOptions\fP"
.br
.ti -1c
.RI "struct \fBMQTTAsync_disconnectOptions\fP"
.br
.in -1c
.SS "Macros"

.in +1c
.ti -1c
.RI "#define \fBMQTTASYNC_SUCCESS\fP   0"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_FAILURE\fP   \-1"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_PERSISTENCE_ERROR\fP   \-2"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_DISCONNECTED\fP   \-3"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_MAX_MESSAGES_INFLIGHT\fP   \-4"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_BAD_UTF8_STRING\fP   \-5"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_NULL_PARAMETER\fP   \-6"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_TOPICNAME_TRUNCATED\fP   \-7"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_BAD_STRUCTURE\fP   \-8"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_BAD_QOS\fP   \-9"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_NO_MORE_MSGIDS\fP   \-10"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_OPERATION_INCOMPLETE\fP   \-11"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_MAX_BUFFERED_MESSAGES\fP   \-12"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_SSL_NOT_SUPPORTED\fP   \-13"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_BAD_PROTOCOL\fP   \-14"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_BAD_MQTT_OPTION\fP   \-15"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_WRONG_MQTT_VERSION\fP   \-16"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_0_LEN_WILL_TOPIC\fP   \-17"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_COMMAND_IGNORED\fP   \-18"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_MAX_BUFFERED\fP   \-19"
.br
.ti -1c
.RI "#define \fBMQTTVERSION_DEFAULT\fP   0"
.br
.ti -1c
.RI "#define \fBMQTTVERSION_3_1\fP   3"
.br
.ti -1c
.RI "#define \fBMQTTVERSION_3_1_1\fP   4"
.br
.ti -1c
.RI "#define \fBMQTTVERSION_5\fP   5"
.br
.ti -1c
.RI "#define \fBMQTT_BAD_SUBSCRIBE\fP   0x80"
.br
.ti -1c
.RI "#define \fBMQTTAsync_init_options_initializer\fP   { {'M', 'Q', 'T', 'G'}, 0, 0 }"
.br
.ti -1c
.RI "#define \fBMQTTAsync_message_initializer\fP   { {'M', 'Q', 'T', 'M'}, 1, 0, NULL, 0, 0, 0, 0, \fBMQTTProperties_initializer\fP }"
.br
.ti -1c
.RI "#define \fBMQTTAsync_connectData_initializer\fP   {{'M', 'Q', 'C', 'D'}, 0, NULL, {0, NULL}}"
.br
.ti -1c
.RI "#define \fBMQTTAsync_failureData5_initializer\fP   {{'M', 'Q', 'F', 'D'}, 0, 0, \fBMQTTREASONCODE_SUCCESS\fP, \fBMQTTProperties_initializer\fP, 0, NULL, 0}"
.br
.ti -1c
.RI "#define \fBMQTTAsync_successData5_initializer\fP   {{'M', 'Q', 'S', 'D'}, 0, 0, \fBMQTTREASONCODE_SUCCESS\fP, \fBMQTTProperties_initializer\fP, {\&.sub={0,0}}}"
.br
.ti -1c
.RI "#define \fBMQTTAsync_responseOptions_initializer\fP   { {'M', 'Q', 'T', 'R'}, 1, NULL, NULL, 0, 0, NULL, NULL, \fBMQTTProperties_initializer\fP, \fBMQTTSubscribe_options_initializer\fP, 0, NULL}"
.br
.ti -1c
.RI "#define \fBMQTTAsync_callOptions_initializer\fP   \fBMQTTAsync_responseOptions_initializer\fP"
.br
.ti -1c
.RI "#define \fBMQTTAsync_createOptions_initializer\fP   { {'M', 'Q', 'C', 'O'}, 2, 0, 100, \fBMQTTVERSION_DEFAULT\fP, 0, 0, 1, 1}"
.br
.ti -1c
.RI "#define \fBMQTTAsync_createOptions_initializer5\fP   { {'M', 'Q', 'C', 'O'}, 2, 0, 100, \fBMQTTVERSION_5\fP, 0, 0, 1, 1}"
.br
.ti -1c
.RI "#define \fBMQTTAsync_willOptions_initializer\fP   { {'M', 'Q', 'T', 'W'}, 1, NULL, NULL, 0, 0, { 0, NULL } }"
.br
.ti -1c
.RI "#define \fBMQTT_SSL_VERSION_DEFAULT\fP   0"
.br
.ti -1c
.RI "#define \fBMQTT_SSL_VERSION_TLS_1_0\fP   1"
.br
.ti -1c
.RI "#define \fBMQTT_SSL_VERSION_TLS_1_1\fP   2"
.br
.ti -1c
.RI "#define \fBMQTT_SSL_VERSION_TLS_1_2\fP   3"
.br
.ti -1c
.RI "#define \fBMQTTAsync_SSLOptions_initializer\fP   { {'M', 'Q', 'T', 'S'}, 5, NULL, NULL, NULL, NULL, NULL, 1, \fBMQTT_SSL_VERSION_DEFAULT\fP, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }"
.br
.ti -1c
.RI "#define \fBMQTTAsync_connectOptions_initializer\fP"
.br
.ti -1c
.RI "#define \fBMQTTAsync_connectOptions_initializer5\fP"
.br
.ti -1c
.RI "#define \fBMQTTAsync_connectOptions_initializer_ws\fP"
.br
.ti -1c
.RI "#define \fBMQTTAsync_connectOptions_initializer5_ws\fP"
.br
.ti -1c
.RI "#define \fBMQTTAsync_disconnectOptions_initializer\fP"
.br
.ti -1c
.RI "#define \fBMQTTAsync_disconnectOptions_initializer5\fP"
.br
.ti -1c
.RI "#define \fBMQTTASYNC_TRUE\fP   1"
.br
.in -1c
.SS "Typedefs"

.in +1c
.ti -1c
.RI "typedef void * \fBMQTTAsync\fP"
.br
.ti -1c
.RI "typedef int \fBMQTTAsync_token\fP"
.br
.ti -1c
.RI "typedef int \fBMQTTAsync_messageArrived\fP(void *context, char *topicName, int topicLen, \fBMQTTAsync_message\fP *message)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_deliveryComplete\fP(void *context, \fBMQTTAsync_token\fP token)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_connectionLost\fP(void *context, char *cause)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_connected\fP(void *context, char *cause)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_disconnected\fP(void *context, \fBMQTTProperties\fP *properties, enum \fBMQTTReasonCodes\fP reasonCode)"
.br
.ti -1c
.RI "typedef int \fBMQTTAsync_updateConnectOptions\fP(void *context, \fBMQTTAsync_connectData\fP *data)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_onSuccess\fP(void *context, \fBMQTTAsync_successData\fP *response)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_onSuccess5\fP(void *context, \fBMQTTAsync_successData5\fP *response)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_onFailure\fP(void *context, \fBMQTTAsync_failureData\fP *response)"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_onFailure5\fP(void *context, \fBMQTTAsync_failureData5\fP *response)"
.br
.ti -1c
.RI "typedef struct \fBMQTTAsync_responseOptions\fP \fBMQTTAsync_responseOptions\fP"
.br
.ti -1c
.RI "typedef struct \fBMQTTAsync_responseOptions\fP \fBMQTTAsync_callOptions\fP"
.br
.ti -1c
.RI "typedef void \fBMQTTAsync_traceCallback\fP(enum \fBMQTTASYNC_TRACE_LEVELS\fP level, char *message)"
.br
.in -1c
.SS "Enumerations"

.in +1c
.ti -1c
.RI "enum \fBMQTTASYNC_TRACE_LEVELS\fP { \fBMQTTASYNC_TRACE_MAXIMUM\fP = 1, \fBMQTTASYNC_TRACE_MEDIUM\fP, \fBMQTTASYNC_TRACE_MINIMUM\fP, \fBMQTTASYNC_TRACE_PROTOCOL\fP, \fBMQTTASYNC_TRACE_ERROR\fP, \fBMQTTASYNC_TRACE_SEVERE\fP, \fBMQTTASYNC_TRACE_FATAL\fP }"
.br
.in -1c
.SS "Functions"

.in +1c
.ti -1c
.RI "void \fBMQTTAsync_global_init\fP (\fBMQTTAsync_init_options\fP *inits)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setDisconnected\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTAsync_disconnected\fP *co)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setUpdateConnectOptions\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTAsync_updateConnectOptions\fP *co)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setBeforePersistenceWrite\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTPersistence_beforeWrite\fP *co)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setAfterPersistenceRead\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTPersistence_afterRead\fP *co)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setCallbacks\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTAsync_connectionLost\fP *cl, \fBMQTTAsync_messageArrived\fP *ma, \fBMQTTAsync_deliveryComplete\fP *dc)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setConnectionLostCallback\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTAsync_connectionLost\fP *cl)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setMessageArrivedCallback\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTAsync_messageArrived\fP *ma)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setDeliveryCompleteCallback\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTAsync_deliveryComplete\fP *dc)"
.br
.ti -1c
.RI "int \fBMQTTAsync_setConnected\fP (\fBMQTTAsync\fP handle, void *context, \fBMQTTAsync_connected\fP *co)"
.br
.ti -1c
.RI "int \fBMQTTAsync_reconnect\fP (\fBMQTTAsync\fP handle)"
.br
.ti -1c
.RI "int \fBMQTTAsync_create\fP (\fBMQTTAsync\fP *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context)"
.br
.ti -1c
.RI "int \fBMQTTAsync_createWithOptions\fP (\fBMQTTAsync\fP *handle, const char *serverURI, const char *clientId, int persistence_type, void *persistence_context, \fBMQTTAsync_createOptions\fP *options)"
.br
.ti -1c
.RI "int \fBMQTTAsync_connect\fP (\fBMQTTAsync\fP handle, const \fBMQTTAsync_connectOptions\fP *options)"
.br
.ti -1c
.RI "int \fBMQTTAsync_disconnect\fP (\fBMQTTAsync\fP handle, const \fBMQTTAsync_disconnectOptions\fP *options)"
.br
.ti -1c
.RI "int \fBMQTTAsync_isConnected\fP (\fBMQTTAsync\fP handle)"
.br
.ti -1c
.RI "int \fBMQTTAsync_subscribe\fP (\fBMQTTAsync\fP handle, const char *topic, int qos, \fBMQTTAsync_responseOptions\fP *response)"
.br
.ti -1c
.RI "int \fBMQTTAsync_subscribeMany\fP (\fBMQTTAsync\fP handle, int count, char *const *topic, const int *qos, \fBMQTTAsync_responseOptions\fP *response)"
.br
.ti -1c
.RI "int \fBMQTTAsync_unsubscribe\fP (\fBMQTTAsync\fP handle, const char *topic, \fBMQTTAsync_responseOptions\fP *response)"
.br
.ti -1c
.RI "int \fBMQTTAsync_unsubscribeMany\fP (\fBMQTTAsync\fP handle, int count, char *const *topic, \fBMQTTAsync_responseOptions\fP *response)"
.br
.ti -1c
.RI "int \fBMQTTAsync_send\fP (\fBMQTTAsync\fP handle, const char *destinationName, int payloadlen, const void *payload, int qos, int retained, \fBMQTTAsync_responseOptions\fP *response)"
.br
.ti -1c
.RI "int \fBMQTTAsync_sendMessage\fP (\fBMQTTAsync\fP handle, const char *destinationName, const \fBMQTTAsync_message\fP *msg, \fBMQTTAsync_responseOptions\fP *response)"
.br
.ti -1c
.RI "int \fBMQTTAsync_getPendingTokens\fP (\fBMQTTAsync\fP handle, \fBMQTTAsync_token\fP **tokens)"
.br
.ti -1c
.RI "int \fBMQTTAsync_isComplete\fP (\fBMQTTAsync\fP handle, \fBMQTTAsync_token\fP token)"
.br
.ti -1c
.RI "int \fBMQTTAsync_waitForCompletion\fP (\fBMQTTAsync\fP handle, \fBMQTTAsync_token\fP token, unsigned long timeout)"
.br
.ti -1c
.RI "void \fBMQTTAsync_freeMessage\fP (\fBMQTTAsync_message\fP **msg)"
.br
.ti -1c
.RI "void \fBMQTTAsync_free\fP (void *ptr)"
.br
.ti -1c
.RI "void * \fBMQTTAsync_malloc\fP (size_t size)"
.br
.ti -1c
.RI "void \fBMQTTAsync_destroy\fP (\fBMQTTAsync\fP *handle)"
.br
.ti -1c
.RI "void \fBMQTTAsync_setTraceLevel\fP (enum \fBMQTTASYNC_TRACE_LEVELS\fP level)"
.br
.ti -1c
.RI "void \fBMQTTAsync_setTraceCallback\fP (\fBMQTTAsync_traceCallback\fP *callback)"
.br
.ti -1c
.RI "\fBMQTTAsync_nameValue\fP * \fBMQTTAsync_getVersionInfo\fP (void)"
.br
.ti -1c
.RI "const char * \fBMQTTAsync_strerror\fP (int code)"
.br
.in -1c
.SH "Macro Definition Documentation"
.PP 
.SS "#define MQTTASYNC_SUCCESS   0"
Return code: No error\&. Indicates successful completion of an MQTT client operation\&. 
.SS "#define MQTTASYNC_FAILURE   \-1"
Return code: A generic error code indicating the failure of an MQTT client operation\&. 
.SS "#define MQTTASYNC_PERSISTENCE_ERROR   \-2"

.SS "#define MQTTASYNC_DISCONNECTED   \-3"
Return code: The client is disconnected\&. 
.SS "#define MQTTASYNC_MAX_MESSAGES_INFLIGHT   \-4"
Return code: The maximum number of messages allowed to be simultaneously in-flight has been reached\&. 
.SS "#define MQTTASYNC_BAD_UTF8_STRING   \-5"
Return code: An invalid UTF-8 string has been detected\&. 
.SS "#define MQTTASYNC_NULL_PARAMETER   \-6"
Return code: A NULL parameter has been supplied when this is invalid\&. 
.SS "#define MQTTASYNC_TOPICNAME_TRUNCATED   \-7"
Return code: The topic has been truncated (the topic string includes embedded NULL characters)\&. String functions will not access the full topic\&. Use the topic length value to access the full topic\&. 
.SS "#define MQTTASYNC_BAD_STRUCTURE   \-8"
Return code: A structure parameter does not have the correct eyecatcher and version number\&. 
.SS "#define MQTTASYNC_BAD_QOS   \-9"
Return code: A qos parameter is not 0, 1 or 2 
.SS "#define MQTTASYNC_NO_MORE_MSGIDS   \-10"
Return code: All 65535 MQTT msgids are being used 
.SS "#define MQTTASYNC_OPERATION_INCOMPLETE   \-11"
Return code: the request is being discarded when not complete 
.SS "#define MQTTASYNC_MAX_BUFFERED_MESSAGES   \-12"
Return code: no more messages can be buffered 
.SS "#define MQTTASYNC_SSL_NOT_SUPPORTED   \-13"
Return code: Attempting SSL connection using non-SSL version of library 
.SS "#define MQTTASYNC_BAD_PROTOCOL   \-14"
Return code: protocol prefix in serverURI should be: 
.PD 0

.IP "\(bu" 2
\fItcp://\fP or \fImqtt://\fP - Insecure TCP 
.IP "\(bu" 2
\fIssl://\fP or \fImqtts://\fP - Encrypted SSL/TLS 
.IP "\(bu" 2
\fIws://\fP - Insecure websockets 
.IP "\(bu" 2
\fIwss://\fP - Secure web sockets
.PP
The TLS enabled prefixes (ssl, mqtts, wss) are only valid if the TLS version of the library is linked with\&. 
.SS "#define MQTTASYNC_BAD_MQTT_OPTION   \-15"
Return code: don't use options for another version of MQTT 
.SS "#define MQTTASYNC_WRONG_MQTT_VERSION   \-16"
Return code: call not applicable to the client's version of MQTT 
.SS "#define MQTTASYNC_0_LEN_WILL_TOPIC   \-17"
Return code: 0 length will topic 
.SS "#define MQTTASYNC_COMMAND_IGNORED   \-18"

.SS "#define MQTTASYNC_MAX_BUFFERED   \-19"

.SS "#define MQTTVERSION_DEFAULT   0"
Default MQTT version to connect with\&. Use 3\&.1\&.1 then fall back to 3\&.1 
.SS "#define MQTTVERSION_3_1   3"
MQTT version to connect with: 3\&.1 
.SS "#define MQTTVERSION_3_1_1   4"
MQTT version to connect with: 3\&.1\&.1 
.SS "#define MQTTVERSION_5   5"
MQTT version to connect with: 5 
.SS "#define MQTT_BAD_SUBSCRIBE   0x80"
Bad return code from subscribe, as defined in the 3\&.1\&.1 specification 
.SS "#define MQTTAsync_init_options_initializer   { {'M', 'Q', 'T', 'G'}, 0, 0 }"

.SS "#define MQTTAsync_message_initializer   { {'M', 'Q', 'T', 'M'}, 1, 0, NULL, 0, 0, 0, 0, \fBMQTTProperties_initializer\fP }"

.SS "#define MQTTAsync_connectData_initializer   {{'M', 'Q', 'C', 'D'}, 0, NULL, {0, NULL}}"

.SS "#define MQTTAsync_failureData5_initializer   {{'M', 'Q', 'F', 'D'}, 0, 0, \fBMQTTREASONCODE_SUCCESS\fP, \fBMQTTProperties_initializer\fP, 0, NULL, 0}"

.SS "#define MQTTAsync_successData5_initializer   {{'M', 'Q', 'S', 'D'}, 0, 0, \fBMQTTREASONCODE_SUCCESS\fP, \fBMQTTProperties_initializer\fP, {\&.sub={0,0}}}"

.SS "#define MQTTAsync_responseOptions_initializer   { {'M', 'Q', 'T', 'R'}, 1, NULL, NULL, 0, 0, NULL, NULL, \fBMQTTProperties_initializer\fP, \fBMQTTSubscribe_options_initializer\fP, 0, NULL}"

.SS "#define MQTTAsync_callOptions_initializer   \fBMQTTAsync_responseOptions_initializer\fP"

.SS "#define MQTTAsync_createOptions_initializer   { {'M', 'Q', 'C', 'O'}, 2, 0, 100, \fBMQTTVERSION_DEFAULT\fP, 0, 0, 1, 1}"

.SS "#define MQTTAsync_createOptions_initializer5   { {'M', 'Q', 'C', 'O'}, 2, 0, 100, \fBMQTTVERSION_5\fP, 0, 0, 1, 1}"

.SS "#define MQTTAsync_willOptions_initializer   { {'M', 'Q', 'T', 'W'}, 1, NULL, NULL, 0, 0, { 0, NULL } }"

.SS "#define MQTT_SSL_VERSION_DEFAULT   0"

.SS "#define MQTT_SSL_VERSION_TLS_1_0   1"

.SS "#define MQTT_SSL_VERSION_TLS_1_1   2"

.SS "#define MQTT_SSL_VERSION_TLS_1_2   3"

.SS "#define MQTTAsync_SSLOptions_initializer   { {'M', 'Q', 'T', 'S'}, 5, NULL, NULL, NULL, NULL, NULL, 1, \fBMQTT_SSL_VERSION_DEFAULT\fP, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0 }"

.SS "#define MQTTAsync_connectOptions_initializer"
\fBValue:\fP
.PP
.nf
{ {'M', 'Q', 'T', 'C'}, 8, 60, 1, 65535, NULL, NULL, NULL, 30, 0,\
NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_DEFAULT, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}
.fi
Initializer for connect options for MQTT 3\&.1\&.1 non-WebSocket connections 
.SS "#define MQTTAsync_connectOptions_initializer5"
\fBValue:\fP
.PP
.nf
{ {'M', 'Q', 'T', 'C'}, 8, 60, 0, 65535, NULL, NULL, NULL, 30, 0,\
NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_5, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}
.fi
Initializer for connect options for MQTT 5\&.0 non-WebSocket connections 
.SS "#define MQTTAsync_connectOptions_initializer_ws"
\fBValue:\fP
.PP
.nf
{ {'M', 'Q', 'T', 'C'}, 8, 45, 1, 65535, NULL, NULL, NULL, 30, 0,\
NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_DEFAULT, 0, 1, 60, {0, NULL}, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL}
.fi
Initializer for connect options for MQTT 3\&.1\&.1 WebSockets connections\&. The keepalive interval is set to 45 seconds to avoid webserver 60 second inactivity timeouts\&. 
.SS "#define MQTTAsync_connectOptions_initializer5_ws"
\fBValue:\fP
.PP
.nf
{ {'M', 'Q', 'T', 'C'}, 8, 45, 0, 65535, NULL, NULL, NULL, 30, 0,\
NULL, NULL, NULL, NULL, 0, NULL, MQTTVERSION_5, 0, 1, 60, {0, NULL}, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL}
.fi
Initializer for connect options for MQTT 5\&.0 WebSockets connections\&. The keepalive interval is set to 45 seconds to avoid webserver 60 second inactivity timeouts\&. 
.SS "#define MQTTAsync_disconnectOptions_initializer"
\fBValue:\fP
.PP
.nf
 { {'M', 'Q', 'T', 'D'}, 0, 0, NULL, NULL, NULL,\
        MQTTProperties_initializer, MQTTREASONCODE_SUCCESS, NULL, NULL }
.fi
.SS "#define MQTTAsync_disconnectOptions_initializer5"
\fBValue:\fP
.PP
.nf
        { {'M', 'Q', 'T', 'D'}, 1, 0, NULL, NULL, NULL,\
        MQTTProperties_initializer, MQTTREASONCODE_SUCCESS, NULL, NULL }
.fi
.SS "#define MQTTASYNC_TRUE   1"
Tests whether a request corresponding to a token is complete\&.
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fItoken\fP An \fBMQTTAsync_token\fP associated with a request\&. 
.RE
.PP
\fBReturns\fP
.RS 4
1 if the request has been completed, 0 if not\&. 
.RE
.PP

.SH "Typedef Documentation"
.PP 
.SS "typedef void* \fBMQTTAsync\fP"
A handle representing an MQTT client\&. A valid client handle is available following a successful call to \fBMQTTAsync_create()\fP\&. 
.SS "typedef int \fBMQTTAsync_token\fP"
A value representing an MQTT message\&. A token is returned to the client application when a message is published\&. The token can then be used to check that the message was successfully delivered to its destination (see MQTTAsync_publish(), MQTTAsync_publishMessage(), \fBMQTTAsync_deliveryComplete()\fP, and \fBMQTTAsync_getPendingTokens()\fP)\&. 
.SS "typedef int MQTTAsync_messageArrived(void *context, char *topicName, int topicLen, \fBMQTTAsync_message\fP *message)"
This is a callback function\&. The client application must provide an implementation of this function to enable asynchronous receipt of messages\&. The function is registered with the client library by passing it as an argument to \fBMQTTAsync_setCallbacks()\fP\&. It is called by the client library when a new message that matches a client subscription has been received from the server\&. This function is executed on a separate thread to the one on which the client application is running\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_setCallbacks()\fP, which contains any application-specific context\&. 
.br
\fItopicName\fP The topic associated with the received message\&. 
.br
\fItopicLen\fP The length of the topic if there are one more NULL characters embedded in \fItopicName\fP, otherwise \fItopicLen\fP is 0\&. If \fItopicLen\fP is 0, the value returned by \fIstrlen(topicName)\fP can be trusted\&. If \fItopicLen\fP is greater than 0, the full topic name can be retrieved by accessing \fItopicName\fP as a byte array of length \fItopicLen\fP\&. 
.br
\fImessage\fP The \fBMQTTAsync_message\fP structure for the received message\&. This structure contains the message payload and attributes\&. 
.RE
.PP
\fBReturns\fP
.RS 4
This function must return 0 or 1 indicating whether or not the message has been safely received by the client application\&. 
.br
 Returning 1 indicates that the message has been successfully handled\&. To free the message storage, \fBMQTTAsync_freeMessage\fP must be called\&. To free the topic name storage, \fBMQTTAsync_free\fP must be called\&.
.br
 Returning 0 indicates that there was a problem\&. In this case, the client library will reinvoke \fBMQTTAsync_messageArrived()\fP to attempt to deliver the message to the application again\&. Do not free the message and topic storage when returning 0, otherwise the redelivery will fail\&. 
.RE
.PP

.SS "typedef void MQTTAsync_deliveryComplete(void *context, \fBMQTTAsync_token\fP token)"
This is a callback function\&. The client application must provide an implementation of this function to enable asynchronous notification of delivery of messages to the server\&. The function is registered with the client library by passing it as an argument to \fBMQTTAsync_setCallbacks()\fP\&. It is called by the client library after the client application has published a message to the server\&. It indicates that the necessary handshaking and acknowledgements for the requested quality of service (see \fBMQTTAsync_message\&.qos\fP) have been completed\&. This function is executed on a separate thread to the one on which the client application is running\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_setCallbacks()\fP, which contains any application-specific context\&. 
.br
\fItoken\fP The \fBMQTTAsync_token\fP associated with the published message\&. Applications can check that all messages have been correctly published by matching the tokens returned from calls to \fBMQTTAsync_send()\fP and \fBMQTTAsync_sendMessage()\fP with the tokens passed to this callback\&. 
.RE
.PP

.SS "typedef void MQTTAsync_connectionLost(void *context, char *cause)"
This is a callback function\&. The client application must provide an implementation of this function to enable asynchronous notification of the loss of connection to the server\&. The function is registered with the client library by passing it as an argument to \fBMQTTAsync_setCallbacks()\fP\&. It is called by the client library if the client loses its connection to the server\&. The client application must take appropriate action, such as trying to reconnect or reporting the problem\&. This function is executed on a separate thread to the one on which the client application is running\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_setCallbacks()\fP, which contains any application-specific context\&. 
.br
\fIcause\fP The reason for the disconnection\&. Currently, \fIcause\fP is always set to NULL\&. 
.RE
.PP

.SS "typedef void MQTTAsync_connected(void *context, char *cause)"
This is a callback function, which will be called when the client library successfully connects\&. This is superfluous when the connection is made in response to a MQTTAsync_connect call, because the onSuccess callback can be used\&. It is intended for use when automatic reconnect is enabled, so that when a reconnection attempt succeeds in the background, the application is notified and can take any required actions\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_setCallbacks()\fP, which contains any application-specific context\&. 
.br
\fIcause\fP The reason for the disconnection\&. Currently, \fIcause\fP is always set to NULL\&. 
.RE
.PP

.SS "typedef void MQTTAsync_disconnected(void *context, \fBMQTTProperties\fP *properties, enum \fBMQTTReasonCodes\fP reasonCode)"
This is a callback function, which will be called when the client library receives a disconnect packet from the server\&. This applies to MQTT V5 and above only\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_setCallbacks()\fP, which contains any application-specific context\&. 
.br
\fIproperties\fP the properties in the disconnect packet\&. 
.br
\fIproperties\fP the reason code from the disconnect packet Currently, \fIcause\fP is always set to NULL\&. 
.RE
.PP

.SS "typedef int MQTTAsync_updateConnectOptions(void *context, \fBMQTTAsync_connectData\fP *data)"
This is a callback function which will allow the client application to update the connection data\&. 
.PP
\fBParameters\fP
.RS 4
\fIdata\fP The connection data which can be modified by the application\&. 
.RE
.PP
\fBReturns\fP
.RS 4
Return a non-zero value to update the connect data, zero to keep the same data\&. 
.RE
.PP

.SS "typedef void MQTTAsync_onSuccess(void *context, \fBMQTTAsync_successData\fP *response)"
This is a callback function\&. The client application must provide an implementation of this function to enable asynchronous notification of the successful completion of an API call\&. The function is registered with the client library by passing it as an argument in \fBMQTTAsync_responseOptions\fP\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_responseOptions\fP, which contains any application-specific context\&. 
.br
\fIresponse\fP Any success data associated with the API completion\&. 
.RE
.PP

.SS "typedef void MQTTAsync_onSuccess5(void *context, \fBMQTTAsync_successData5\fP *response)"
This is a callback function, the MQTT V5 version of \fBMQTTAsync_onSuccess\fP\&. The client application must provide an implementation of this function to enable asynchronous notification of the successful completion of an API call\&. The function is registered with the client library by passing it as an argument in \fBMQTTAsync_responseOptions\fP\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_responseOptions\fP, which contains any application-specific context\&. 
.br
\fIresponse\fP Any success data associated with the API completion\&. 
.RE
.PP

.SS "typedef void MQTTAsync_onFailure(void *context, \fBMQTTAsync_failureData\fP *response)"
This is a callback function\&. The client application must provide an implementation of this function to enable asynchronous notification of the unsuccessful completion of an API call\&. The function is registered with the client library by passing it as an argument in \fBMQTTAsync_responseOptions\fP\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_responseOptions\fP, which contains any application-specific context\&. 
.br
\fIresponse\fP Failure data associated with the API completion\&. 
.RE
.PP

.SS "typedef void MQTTAsync_onFailure5(void *context, \fBMQTTAsync_failureData5\fP *response)"
This is a callback function, the MQTT V5 version of \fBMQTTAsync_onFailure\fP\&. The application must provide an implementation of this function to enable asynchronous notification of the unsuccessful completion of an API call\&. The function is registered with the client library by passing it as an argument in \fBMQTTAsync_responseOptions\fP\&.
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to the \fIcontext\fP value originally passed to \fBMQTTAsync_responseOptions\fP, which contains any application-specific context\&. 
.br
\fIresponse\fP Failure data associated with the API completion\&. 
.RE
.PP

.SS "typedef struct \fBMQTTAsync_responseOptions\fP \fBMQTTAsync_responseOptions\fP"
Structure to define call options\&. For MQTT 5\&.0 there is input data as well as that describing the response method\&. So there is now also a synonym \fBMQTTAsync_callOptions\fP to better reflect the use\&. This responseOptions name is kept for backward compatibility\&. 
.SS "typedef struct \fBMQTTAsync_responseOptions\fP \fBMQTTAsync_callOptions\fP"
A synonym for responseOptions to better reflect its usage since MQTT 5\&.0 
.SS "typedef void MQTTAsync_traceCallback(enum \fBMQTTASYNC_TRACE_LEVELS\fP level, char *message)"
This is a callback function prototype which must be implemented if you want to receive trace information\&. Do not invoke any other Paho API calls in this callback function - unpredictable behavior may result\&. 
.PP
\fBParameters\fP
.RS 4
\fIlevel\fP the trace level of the message returned 
.br
\fImessage\fP the trace message\&. This is a pointer to a static buffer which will be overwritten on each call\&. You must copy the data if you want to keep it for later\&. 
.RE
.PP

.SH "Enumeration Type Documentation"
.PP 
.SS "enum \fBMQTTASYNC_TRACE_LEVELS\fP"

.PP
\fBEnumerator\fP
.in +1c
.TP
\fB\fIMQTTASYNC_TRACE_MAXIMUM \fP\fP
.TP
\fB\fIMQTTASYNC_TRACE_MEDIUM \fP\fP
.TP
\fB\fIMQTTASYNC_TRACE_MINIMUM \fP\fP
.TP
\fB\fIMQTTASYNC_TRACE_PROTOCOL \fP\fP
.TP
\fB\fIMQTTASYNC_TRACE_ERROR \fP\fP
.TP
\fB\fIMQTTASYNC_TRACE_SEVERE \fP\fP
.TP
\fB\fIMQTTASYNC_TRACE_FATAL \fP\fP
.SH "Function Documentation"
.PP 
.SS "void MQTTAsync_global_init (\fBMQTTAsync_init_options\fP * inits)"
Global init of mqtt library\&. Call once on program start to set global behaviour\&. handle_openssl_init - if mqtt library should handle openssl init (1) or rely on the caller to init it before using mqtt (0) 
.SS "int MQTTAsync_setDisconnected (\fBMQTTAsync\fP handle, void * context, \fBMQTTAsync_disconnected\fP * co)"
Sets the \fBMQTTAsync_disconnected()\fP callback function for a client\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&.
.RE
.PP
\fBNote:\fP Neither \fBMQTTAsync_create()\fP nor \fBMQTTAsync_destroy()\fP should be called within this callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to each of the callback functions to provide access to the context information in the callback\&. 
.br
\fIco\fP A pointer to an \fBMQTTAsync_connected()\fP callback function\&. NULL removes the callback setting\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the callbacks were correctly set, \fBMQTTASYNC_FAILURE\fP if an error occurred\&. 
.RE
.PP

.SS "int MQTTAsync_setUpdateConnectOptions (\fBMQTTAsync\fP handle, void * context, \fBMQTTAsync_updateConnectOptions\fP * co)"
Sets the \fBMQTTAsync_updateConnectOptions()\fP callback function for a client\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to each of the callback functions to provide access to the context information in the callback\&. 
.br
\fIco\fP A pointer to an \fBMQTTAsync_updateConnectOptions()\fP callback function\&. NULL removes the callback setting\&. 
.RE
.PP

.SS "int MQTTAsync_setBeforePersistenceWrite (\fBMQTTAsync\fP handle, void * context, \fBMQTTPersistence_beforeWrite\fP * co)"
Sets the \fBMQTTPersistence_beforeWrite()\fP callback function for a client\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to the callback function to provide access to the context information in the callback\&. 
.br
\fIco\fP A pointer to an \fBMQTTPersistence_beforeWrite()\fP callback function\&. NULL removes the callback setting\&. 
.RE
.PP

.SS "int MQTTAsync_setAfterPersistenceRead (\fBMQTTAsync\fP handle, void * context, \fBMQTTPersistence_afterRead\fP * co)"
Sets the \fBMQTTPersistence_afterRead()\fP callback function for a client\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to the callback function to provide access to the context information in the callback\&. 
.br
\fIco\fP A pointer to an \fBMQTTPersistence_beforeWrite()\fP callback function\&. NULL removes the callback setting\&. 
.RE
.PP

.SS "int MQTTAsync_setCallbacks (\fBMQTTAsync\fP handle, void * context, \fBMQTTAsync_connectionLost\fP * cl, \fBMQTTAsync_messageArrived\fP * ma, \fBMQTTAsync_deliveryComplete\fP * dc)"
This function sets the global callback functions for a specific client\&. If your client application doesn't use a particular callback, set the relevant parameter to NULL\&. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application\&. If you do not set a messageArrived callback function, you will not be notified of the receipt of any messages as a result of a subscription\&.
.PP
\fBNote:\fP The MQTT client must be disconnected when this function is called\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to each of the callback functions to provide access to the context information in the callback\&. 
.br
\fIcl\fP A pointer to an \fBMQTTAsync_connectionLost()\fP callback function\&. You can set this to NULL if your application doesn't handle disconnections\&. 
.br
\fIma\fP A pointer to an \fBMQTTAsync_messageArrived()\fP callback function\&. If this callback is not set, an error will be returned\&. You must set this callback because otherwise there would be no way to deliver any incoming messages\&. 
.br
\fIdc\fP A pointer to an \fBMQTTAsync_deliveryComplete()\fP callback function\&. You can set this to NULL if you do not want to check for successful delivery\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the callbacks were correctly set, \fBMQTTASYNC_FAILURE\fP if an error occurred\&. 
.RE
.PP

.SS "int MQTTAsync_setConnectionLostCallback (\fBMQTTAsync\fP handle, void * context, \fBMQTTAsync_connectionLost\fP * cl)"
This function sets the callback function for a connection lost event for a specific client\&. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application\&.
.PP
\fBNote:\fP The MQTT client must be disconnected when this function is called\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed the callback functions to provide access to the context information in the callback\&. 
.br
\fIcl\fP A pointer to an \fBMQTTAsync_connectionLost()\fP callback function\&. You can set this to NULL if your application doesn't handle disconnections\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the callbacks were correctly set, \fBMQTTASYNC_FAILURE\fP if an error occurred\&. 
.RE
.PP

.SS "int MQTTAsync_setMessageArrivedCallback (\fBMQTTAsync\fP handle, void * context, \fBMQTTAsync_messageArrived\fP * ma)"
This function sets the callback function for a message arrived event for a specific client\&. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application\&. If you do not set a messageArrived callback function, you will not be notified of the receipt of any messages as a result of a subscription\&.
.PP
\fBNote:\fP The MQTT client must be disconnected when this function is called\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to the callback functions to provide access to the context information in the callback\&. 
.br
\fIma\fP A pointer to an \fBMQTTAsync_messageArrived()\fP callback function\&. You can set this to NULL if your application doesn't handle receipt of messages\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the callbacks were correctly set, \fBMQTTASYNC_FAILURE\fP if an error occurred\&. 
.RE
.PP

.SS "int MQTTAsync_setDeliveryCompleteCallback (\fBMQTTAsync\fP handle, void * context, \fBMQTTAsync_deliveryComplete\fP * dc)"
This function sets the callback function for a delivery complete event for a specific client\&. Any necessary message acknowledgements and status communications are handled in the background without any intervention from the client application\&.
.PP
\fBNote:\fP The MQTT client must be disconnected when this function is called\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to the callback functions to provide access to the context information in the callback\&. 
.br
\fIdc\fP A pointer to an \fBMQTTAsync_deliveryComplete()\fP callback function\&. You can set this to NULL if you do not want to check for successful delivery\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the callbacks were correctly set, \fBMQTTASYNC_FAILURE\fP if an error occurred\&. 
.RE
.PP

.SS "int MQTTAsync_setConnected (\fBMQTTAsync\fP handle, void * context, \fBMQTTAsync_connected\fP * co)"
Sets the \fBMQTTAsync_connected()\fP callback function for a client\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcontext\fP A pointer to any application-specific context\&. The the \fIcontext\fP pointer is passed to each of the callback functions to provide access to the context information in the callback\&. 
.br
\fIco\fP A pointer to an \fBMQTTAsync_connected()\fP callback function\&. NULL removes the callback setting\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the callbacks were correctly set, \fBMQTTASYNC_FAILURE\fP if an error occurred\&. 
.RE
.PP

.SS "int MQTTAsync_reconnect (\fBMQTTAsync\fP handle)"
Reconnects a client with the previously used connect options\&. Connect must have previously been called for this to work\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the callbacks were correctly set, \fBMQTTASYNC_FAILURE\fP if an error occurred\&. 
.RE
.PP

.SS "int MQTTAsync_create (\fBMQTTAsync\fP * handle, const char * serverURI, const char * clientId, int persistence_type, void * persistence_context)"
This function creates an MQTT client ready for connection to the specified server and using the specified persistent storage (see MQTTAsync_persistence)\&. See also \fBMQTTAsync_destroy()\fP\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A pointer to an \fBMQTTAsync\fP handle\&. The handle is populated with a valid client reference following a successful return from this function\&. 
.br
\fIserverURI\fP A null-terminated string specifying the server to which the client will connect\&. It takes the form \fIprotocol://host:port\fP where \fIprotocol\fP must be: 
.br
 \fItcp://\fP or \fImqtt://\fP - Insecure TCP 
.br
 \fIssl://\fP or \fImqtts://\fP - Encrypted SSL/TLS 
.br
 \fIws://\fP - Insecure websockets 
.br
 \fIwss://\fP - Secure web sockets 
.br
 The TLS enabled prefixes (ssl, mqtts, wss) are only valid if a TLS version of the library is linked with\&. For \fIhost\fP, you can specify either an IP address or a host name\&. For instance, to connect to a server running on the local machines with the default MQTT port, specify \fItcp://localhost:1883\fP\&. 
.br
\fIclientId\fP The client identifier passed to the server when the client connects to it\&. It is a null-terminated UTF-8 encoded string\&. 
.br
\fIpersistence_type\fP The type of persistence to be used by the client: 
.br
 \fBMQTTCLIENT_PERSISTENCE_NONE\fP: Use in-memory persistence\&. If the device or system on which the client is running fails or is switched off, the current state of any in-flight messages is lost and some messages may not be delivered even at QoS1 and QoS2\&. 
.br
 \fBMQTTCLIENT_PERSISTENCE_DEFAULT\fP: Use the default (file system-based) persistence mechanism\&. Status about in-flight messages is held in persistent storage and provides some protection against message loss in the case of unexpected failure\&. 
.br
 \fBMQTTCLIENT_PERSISTENCE_USER\fP: Use an application-specific persistence implementation\&. Using this type of persistence gives control of the persistence mechanism to the application\&. The application has to implement the \fBMQTTClient_persistence\fP interface\&. 
.br
\fIpersistence_context\fP If the application uses \fBMQTTCLIENT_PERSISTENCE_NONE\fP persistence, this argument is unused and should be set to NULL\&. For \fBMQTTCLIENT_PERSISTENCE_DEFAULT\fP persistence, it should be set to the location of the persistence directory (if set to NULL, the persistence directory used is the working directory)\&. Applications that use \fBMQTTCLIENT_PERSISTENCE_USER\fP persistence set this argument to point to a valid \fBMQTTClient_persistence\fP structure\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the client is successfully created, otherwise an error code is returned\&. 
.RE
.PP

.SS "int MQTTAsync_createWithOptions (\fBMQTTAsync\fP * handle, const char * serverURI, const char * clientId, int persistence_type, void * persistence_context, \fBMQTTAsync_createOptions\fP * options)"

.SS "int MQTTAsync_connect (\fBMQTTAsync\fP handle, const \fBMQTTAsync_connectOptions\fP * options)"
This function attempts to connect a previously-created client (see \fBMQTTAsync_create()\fP) to an MQTT server using the specified options\&. If you want to enable asynchronous message and status notifications, you must call \fBMQTTAsync_setCallbacks()\fP prior to \fBMQTTAsync_connect()\fP\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIoptions\fP A pointer to a valid \fBMQTTAsync_connectOptions\fP structure\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the client connect request was accepted\&. If the client was unable to connect to the server, an error code is returned via the onFailure callback, if set\&. Error codes greater than 0 are returned by the MQTT protocol:
.br

.br
 \fB1\fP: Connection refused: Unacceptable protocol version
.br
 \fB2\fP: Connection refused: Identifier rejected
.br
 \fB3\fP: Connection refused: Server unavailable
.br
 \fB4\fP: Connection refused: Bad user name or password
.br
 \fB5\fP: Connection refused: Not authorized
.br
 \fB6-255\fP: Reserved for future use
.br
 
.RE
.PP

.SS "int MQTTAsync_disconnect (\fBMQTTAsync\fP handle, const \fBMQTTAsync_disconnectOptions\fP * options)"
This function attempts to disconnect the client from the MQTT server\&. In order to allow the client time to complete handling of messages that are in-flight when this function is called, a timeout period is specified\&. When the timeout period has expired, the client disconnects even if there are still outstanding message acknowledgements\&. The next time the client connects to the same server, any QoS 1 or 2 messages which have not completed will be retried depending on the cleansession settings for both the previous and the new connection (see \fBMQTTAsync_connectOptions\&.cleansession\fP and \fBMQTTAsync_connect()\fP)\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIoptions\fP The client delays disconnection for up to this time (in milliseconds) in order to allow in-flight message transfers to complete\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the client successfully disconnects from the server\&. An error code is returned if the client was unable to disconnect from the server 
.RE
.PP

.SS "int MQTTAsync_isConnected (\fBMQTTAsync\fP handle)"
This function allows the client application to test whether or not a client is currently connected to the MQTT server\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.RE
.PP
\fBReturns\fP
.RS 4
Boolean true if the client is connected, otherwise false\&. 
.RE
.PP

.SS "int MQTTAsync_subscribe (\fBMQTTAsync\fP handle, const char * topic, int qos, \fBMQTTAsync_responseOptions\fP * response)"
This function attempts to subscribe a client to a single topic, which may contain wildcards (see \fBSubscription wildcards\fP)\&. This call also specifies the \fBQuality of service\fP requested for the subscription (see also \fBMQTTAsync_subscribeMany()\fP)\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fItopic\fP The subscription topic, which may include wildcards\&. 
.br
\fIqos\fP The requested quality of service for the subscription\&. 
.br
\fIresponse\fP A pointer to a response options structure\&. Used to set callback functions\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the subscription request is successful\&. An error code is returned if there was a problem registering the subscription\&. 
.RE
.PP

.SS "int MQTTAsync_subscribeMany (\fBMQTTAsync\fP handle, int count, char *const * topic, const int * qos, \fBMQTTAsync_responseOptions\fP * response)"
This function attempts to subscribe a client to a list of topics, which may contain wildcards (see \fBSubscription wildcards\fP)\&. This call also specifies the \fBQuality of service\fP requested for each topic (see also \fBMQTTAsync_subscribe()\fP)\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcount\fP The number of topics for which the client is requesting subscriptions\&. 
.br
\fItopic\fP An array (of length \fIcount\fP) of pointers to topics, each of which may include wildcards\&. 
.br
\fIqos\fP An array (of length \fIcount\fP) of \fBQuality of service\fP values\&. qos[n] is the requested QoS for topic[n]\&. 
.br
\fIresponse\fP A pointer to a response options structure\&. Used to set callback functions\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the subscription request is successful\&. An error code is returned if there was a problem registering the subscriptions\&. 
.RE
.PP

.SS "int MQTTAsync_unsubscribe (\fBMQTTAsync\fP handle, const char * topic, \fBMQTTAsync_responseOptions\fP * response)"
This function attempts to remove an existing subscription made by the specified client\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fItopic\fP The topic for the subscription to be removed, which may include wildcards (see \fBSubscription wildcards\fP)\&. 
.br
\fIresponse\fP A pointer to a response options structure\&. Used to set callback functions\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the subscription is removed\&. An error code is returned if there was a problem removing the subscription\&. 
.RE
.PP

.SS "int MQTTAsync_unsubscribeMany (\fBMQTTAsync\fP handle, int count, char *const * topic, \fBMQTTAsync_responseOptions\fP * response)"
This function attempts to remove existing subscriptions to a list of topics made by the specified client\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIcount\fP The number subscriptions to be removed\&. 
.br
\fItopic\fP An array (of length \fIcount\fP) of pointers to the topics of the subscriptions to be removed, each of which may include wildcards\&. 
.br
\fIresponse\fP A pointer to a response options structure\&. Used to set callback functions\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the subscriptions are removed\&. An error code is returned if there was a problem removing the subscriptions\&. 
.RE
.PP

.SS "int MQTTAsync_send (\fBMQTTAsync\fP handle, const char * destinationName, int payloadlen, const void * payload, int qos, int retained, \fBMQTTAsync_responseOptions\fP * response)"
This function attempts to publish a message to a given topic (see also \fBMQTTAsync_sendMessage()\fP)\&. An \fBMQTTAsync_token\fP is issued when this function returns successfully if the QoS is greater than 0\&. If the client application needs to test for successful delivery of messages, a callback should be set (see \fBMQTTAsync_onSuccess()\fP and \fBMQTTAsync_deliveryComplete()\fP)\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIdestinationName\fP The topic associated with this message\&. 
.br
\fIpayloadlen\fP The length of the payload in bytes\&. 
.br
\fIpayload\fP A pointer to the byte array payload of the message\&. 
.br
\fIqos\fP The \fBQuality of service\fP of the message\&. 
.br
\fIretained\fP The retained flag for the message\&. 
.br
\fIresponse\fP A pointer to an \fBMQTTAsync_responseOptions\fP structure\&. Used to set callback functions\&. This is optional and can be set to NULL\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the message is accepted for publication\&. An error code is returned if there was a problem accepting the message\&. 
.RE
.PP

.SS "int MQTTAsync_sendMessage (\fBMQTTAsync\fP handle, const char * destinationName, const \fBMQTTAsync_message\fP * msg, \fBMQTTAsync_responseOptions\fP * response)"
This function attempts to publish a message to a given topic (see also MQTTAsync_publish())\&. An \fBMQTTAsync_token\fP is issued when this function returns successfully if the QoS is greater than 0\&. If the client application needs to test for successful delivery of messages, a callback should be set (see \fBMQTTAsync_onSuccess()\fP and \fBMQTTAsync_deliveryComplete()\fP)\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fIdestinationName\fP The topic associated with this message\&. 
.br
\fImsg\fP A pointer to a valid \fBMQTTAsync_message\fP structure containing the payload and attributes of the message to be published\&. 
.br
\fIresponse\fP A pointer to an \fBMQTTAsync_responseOptions\fP structure\&. Used to set callback functions\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the message is accepted for publication\&. An error code is returned if there was a problem accepting the message\&. 
.RE
.PP

.SS "int MQTTAsync_getPendingTokens (\fBMQTTAsync\fP handle, \fBMQTTAsync_token\fP ** tokens)"
This function sets a pointer to an array of tokens for messages that are currently in-flight (pending completion)\&.
.PP
\fBImportant note:\fP The memory used to hold the array of tokens is malloc()'d in this function\&. The client application is responsible for freeing this memory when it is no longer required\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fItokens\fP The address of a pointer to an \fBMQTTAsync_token\fP\&. When the function returns successfully, the pointer is set to point to an array of tokens representing messages pending completion\&. The last member of the array is set to -1 to indicate there are no more tokens\&. If no tokens are pending, the pointer is set to NULL\&. 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the function returns successfully\&. An error code is returned if there was a problem obtaining the list of pending tokens\&. 
.RE
.PP

.SS "int MQTTAsync_isComplete (\fBMQTTAsync\fP handle, \fBMQTTAsync_token\fP token)"

.SS "int MQTTAsync_waitForCompletion (\fBMQTTAsync\fP handle, \fBMQTTAsync_token\fP token, unsigned long timeout)"
Waits for a request corresponding to a token to complete\&. This only works for messages with QoS greater than 0\&. A QoS 0 message has no MQTT token\&. This function will always return \fBMQTTASYNC_SUCCESS\fP for a QoS 0 message\&.
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A valid client handle from a successful call to \fBMQTTAsync_create()\fP\&. 
.br
\fItoken\fP An \fBMQTTAsync_token\fP associated with a request\&. 
.br
\fItimeout\fP the maximum time to wait for completion, in milliseconds 
.RE
.PP
\fBReturns\fP
.RS 4
\fBMQTTASYNC_SUCCESS\fP if the request has been completed in the time allocated, \fBMQTTASYNC_FAILURE\fP or \fBMQTTASYNC_DISCONNECTED\fP if not\&. 
.RE
.PP

.SS "void MQTTAsync_freeMessage (\fBMQTTAsync_message\fP ** msg)"
This function frees memory allocated to an MQTT message, including the additional memory allocated to the message payload\&. The client application calls this function when the message has been fully processed\&. \fBImportant note:\fP This function does not free the memory allocated to a message topic string\&. It is the responsibility of the client application to free this memory using the \fBMQTTAsync_free()\fP library function\&. 
.PP
\fBParameters\fP
.RS 4
\fImsg\fP The address of a pointer to the \fBMQTTAsync_message\fP structure to be freed\&. 
.RE
.PP

.SS "void MQTTAsync_free (void * ptr)"
This function frees memory allocated by the MQTT C client library, especially the topic name\&. This is needed on Windows when the client library and application program have been compiled with different versions of the C compiler\&. It is thus good policy to always use this function when freeing any MQTT C client- allocated memory\&. 
.PP
\fBParameters\fP
.RS 4
\fIptr\fP The pointer to the client library storage to be freed\&. 
.RE
.PP

.SS "void* MQTTAsync_malloc (size_t size)"
This function is used to allocate memory to be used or freed by the MQTT C client library, especially the data in the \fBMQTTPersistence_afterRead\fP and \fBMQTTPersistence_beforeWrite\fP callbacks\&. This is needed on Windows when the client library and application program have been compiled with different versions of the C compiler\&. 
.PP
\fBParameters\fP
.RS 4
\fIsize\fP The size of the memory to be allocated\&. 
.RE
.PP

.SS "void MQTTAsync_destroy (\fBMQTTAsync\fP * handle)"
This function frees the memory allocated to an MQTT client (see \fBMQTTAsync_create()\fP)\&. It should be called when the client is no longer required\&. 
.PP
\fBParameters\fP
.RS 4
\fIhandle\fP A pointer to the handle referring to the \fBMQTTAsync\fP structure to be freed\&. 
.RE
.PP

.SS "void MQTTAsync_setTraceLevel (enum \fBMQTTASYNC_TRACE_LEVELS\fP level)"
This function sets the level of trace information which will be returned in the trace callback\&. 
.PP
\fBParameters\fP
.RS 4
\fIlevel\fP the trace level required 
.RE
.PP

.SS "void MQTTAsync_setTraceCallback (\fBMQTTAsync_traceCallback\fP * callback)"
This function sets the trace callback if needed\&. If set to NULL, no trace information will be returned\&. The default trace level is MQTTASYNC_TRACE_MINIMUM\&. 
.PP
\fBParameters\fP
.RS 4
\fIcallback\fP a pointer to the function which will handle the trace information 
.RE
.PP

.SS "\fBMQTTAsync_nameValue\fP* MQTTAsync_getVersionInfo (void)"
This function returns version information about the library\&. no trace information will be returned\&. The default trace level is MQTTASYNC_TRACE_MINIMUM 
.PP
\fBReturns\fP
.RS 4
an array of strings describing the library\&. The last entry is a NULL pointer\&. 
.RE
.PP

.SS "const char* MQTTAsync_strerror (int code)"
Returns a pointer to a string representation of the error code, or NULL\&. Do not free after use\&. Returns NULL if the error code is unknown\&. 
.PP
\fBParameters\fP
.RS 4
\fIcode\fP the MQTTASYNC_ return code\&. 
.RE
.PP
\fBReturns\fP
.RS 4
a static string representation of the error code\&. 
.RE
.PP

.SH "Author"
.PP 
Generated automatically by Doxygen for Paho Asynchronous MQTT C Client Library from the source code\&.
