{"types": "", "keywords": ["HarmonyOS", "OpenHarmony", "Sql", "Dbflow"], "author": "<PERSON><PERSON><PERSON>", "description": "dataORM is a relational mapping database with one line of code to manipulate a database or chain call, backup, upgrade, cache, and more", "ohos": {"org": "opensource"}, "main": "Index.ets", "repository": "https://gitee.com/openharmony-sig/dataORM", "type": "module", "version": "2.2.2-rc.0", "dependencies": {"reflect-metadata": "^0.1.13"}, "tags": ["Tools"], "license": "Apache License 2.0", "devDependencies": {}, "name": "@ohos/dataorm"}