/*
 * Copyright (c) 2022 Huawei Device Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// @ts-nocheck
import { TableStatements } from './internal/TableStatements';
import { AbstractDao } from './AbstractDao';

/** For internal use by dataORM only. */
export class InternalQueryDaoAccess <T> {
    private dao: AbstractDao<T, any>;

    constructor(abstractDao: AbstractDao<T, any>) {
        this.dao = abstractDao;
    }

    public loadCurrent(cursor: any, offset: number, lock: boolean): T {
        return this.dao.loadCurrent(cursor, offset, lock);
    }

    public loadAllAndCloseCursor(cursor: any): Array<T> {
        return this.dao.loadAllAndCloseCursor(cursor);
    }

    public loadUniqueAndCloseCursor(cursor: any): T {
        return this.dao.loadUniqueAndCloseCursor(cursor);
    }

    public getStatements(): TableStatements {
        return this.dao.getStatements();
    }

    public static getStatements<T2>(dao: AbstractDao<T2, any>): TableStatements {
        return dao.getStatements();
    }
}