/*
  * Copyright (c) 2022 Huawei Device Co., Ltd.
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
  * you may not use this file except in compliance with the License.
  * You may obtain a copy of the License at
    *
  * http://www.apache.org/licenses/LICENSE-2.0
    *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  */
import { StringBuilder } from '../StringBuilder';

export class Queue<T> {
  private elements: Array<T>;
  private _size: number | undefined;

  public constructor(capacity?: number) {
    this.elements = new Array<T>();
    this._size = capacity;
  }

  public add(o: T) {
    if (o == null) {
      return false;
    }
    // 如果传递了size参数就设置了队列的大小
    if (this._size != undefined && !isNaN(this._size)) {
      if (this.elements.length == this._size) {
        this.pop();
      }
    }
    this.elements.unshift(o);
    return true;
  }

  public pop(): T {
    return this.elements.pop();
  }

  public removeFirst() {
    return this.elements.shift();
  }

  public size(): number {
    return this.elements.length;
  }

  public empty(): boolean {
    return this.size() == 0;
  }

  public clear() {
    delete this.elements;
    this.elements = new Array<T>();
  }

  public getElements(): T[] {
    return this.elements;
  }

  public clone() {
    let c = new Queue();
    c.elements = JSON.parse(JSON.stringify(this.elements));
    c._size = this._size;
    return c;
  }

  public toArrayString(): string {
    let result = "";
    if (this.elements) {
      this.elements.forEach((v, i) => {
        result += v;
      })
    }
    return result;
  }
}