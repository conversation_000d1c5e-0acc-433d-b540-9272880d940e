/*
  * Copyright (c) 2022 Huawei Device Co., Ltd.
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
  * you may not use this file except in compliance with the License.
  * You may obtain a copy of the License at
    *
  * http://www.apache.org/licenses/LICENSE-2.0
    *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  */
export var TableAction;
(function (TableAction) {
    /**
     * The model called {@link Model#save()}
     */
    TableAction[TableAction["SAVE"] = 0] = "SAVE";
    /**
     * The model called {@link Model#insert()}
     */
    TableAction[TableAction["INSERT"] = 1] = "INSERT";
    /**
     * The model called {@link Model#update()}
     */
    TableAction[TableAction["UPDATE"] = 2] = "UPDATE";
    /**
     * The model called {@link Model#delete()}
     */
    TableAction[TableAction["DELETE"] = 3] = "DELETE";
    TableAction[TableAction["CHANGE"] = 4] = "CHANGE";
    TableAction[TableAction["QUERY"] = 5] = "QUERY";
})(TableAction || (TableAction = {}));
//# sourceMappingURL=TableAction.js.map