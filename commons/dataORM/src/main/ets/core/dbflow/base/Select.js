/*
  * Copyright (c) 2022 Huawei Device Co., Ltd.
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
  * you may not use this file except in compliance with the License.
  * You may obtain a copy of the License at
    *
  * http://www.apache.org/licenses/LICENSE-2.0
    *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  */
//@ts-nocheck
import dataRdb from '@ohos.data.relationalStore';
import { AbstractDao } from '../../AbstractDao';
export class Select {
    constructor() {
        this.eqMapKey = new Map();
        this.daoSession = globalThis.daoSession;
    }
    from(model) {
        let tableName = AbstractDao.TABLENAME(model);
        this.predicates = new dataRdb.RdbPredicates(tableName);
        return this;
    }
    eq(columnName, value) {
        this.eqMapKey.set(columnName, value);
        this.predicates.equalTo(columnName, value);
        return this;
    }
    notEq(columnName, value) {
        this.predicates.notEqualTo(columnName, value);
        return this;
    }
    beginWrap() {
        this.predicates.beginWrap();
        return this;
    }
    endWrap() {
        this.predicates.endWrap();
        return this;
    }
    or() {
        this.predicates.or();
        return this;
    }
    and() {
        this.predicates.and();
        return this;
    }
    contains(columnName, value) {
        this.predicates.contains(columnName, value);
        return this;
    }
    beginsWith(columnName, value) {
        this.predicates.beginsWith(columnName, value);
        return this;
    }
    endsWith(columnName, value) {
        this.predicates.endsWith(columnName, value);
        return this;
    }
    isNull(value) {
        this.predicates.isNull(value);
        return this;
    }
    isNotNull(value) {
        this.predicates.isNotNull(value);
        return this;
    }
    like(columnName, value) {
        this.predicates.like(columnName, value);
        return this;
    }
    glob(columnName, value) {
        this.predicates.glob(columnName, value);
        return this;
    }
    between(value, low, high) {
        this.predicates.between(value, low, high);
        return this;
    }
    notBetween(value, low, high) {
        this.predicates.notBetween(value, low, high);
        return this;
    }
    greaterThan(columnName, value) {
        this.predicates.greaterThan(columnName, value);
        return this;
    }
    lessThan(columnName, value) {
        this.predicates.lessThan(columnName, value);
        return this;
    }
    greaterThanOrEq(columnName, value) {
        this.predicates.greaterThanOrEqualTo(columnName, value);
        return this;
    }
    lessThanOrEq(columnName, value) {
        this.predicates.lessThanOrEqualTo(columnName, value);
        return this;
    }
    orderByAsc(columnName) {
        this.predicates.orderByAsc(columnName);
        return this;
    }
    orderByDesc(columnName) {
        this.predicates.orderByDesc(columnName);
        return this;
    }
    distinct() {
        this.predicates.distinct();
        return this;
    }
    limit(value) {
        this.predicates.limitAs(value);
        return this;
    }
    offset(rowOffset) {
        this.predicates.offsetAs(rowOffset);
        return this;
    }
    groupBy(value) {
        this.predicates.groupBy(value);
        return this;
    }
    indexedBy(columnName) {
        this.predicates.indexedBy(columnName);
        return this;
    }
    inData(columnName, value) {
        this.predicates.in(columnName, value);
        return this;
    }
    notIn(columnName, value) {
        this.predicates.notIn(columnName, value);
        return this;
    }
    build() {
        return this.predicates;
    }
    getKeyMaps() {
        return this.eqMapKey;
    }
    querySingle(entityCls) {
        return this.daoSession.getBaseDao(entityCls).query(this.limit(1));
    }
    query(entityCls) {
        return this.daoSession.getBaseDao(entityCls).query(this);
    }
}
//# sourceMappingURL=Select.js.map