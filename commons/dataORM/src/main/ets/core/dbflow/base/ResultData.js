/*
  * Copyright (c) 2022 Huawei Device Co., Ltd.
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
  * you may not use this file except in compliance with the License.
  * You may obtain a copy of the License at
    *
  * http://www.apache.org/licenses/LICENSE-2.0
    *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  */
export class ResultData {
    constructor(resultSet) {
        this.resultSet = resultSet;
    }
    columnNames() {
        return this.resultSet.columnNames;
    }
    columnCount() {
        return this.resultSet.columnCount;
    }
    getColumnIndex(columnName) {
        return this.resultSet.getColumnIndex(columnName);
    }
    getColumnName(columnIndex) {
        return this.resultSet.getColumnName(columnIndex);
    }
    goTo(offset) {
        return this.resultSet.goTo(offset);
    }
    goToRow(position) {
        return this.resultSet.goToRow(position);
    }
    goToFirstRow() {
        return this.resultSet.goToFirstRow();
    }
    goToLastRow() {
        return this.resultSet.goToLastRow();
    }
    goToNextRow() {
        return this.resultSet.goToNextRow();
    }
    goToPreviousRow() {
        return this.resultSet.goToPreviousRow();
    }
    getBlob(columnIndex) {
        return this.resultSet.getBlob(columnIndex);
    }
    getString(columnIndex) {
        return this.resultSet.getString(columnIndex);
    }
    getLong(columnIndex) {
        return this.resultSet.getLong(columnIndex);
    }
    getDouble(columnIndex) {
        return this.resultSet.getDouble(columnIndex);
    }
    isColumnNull(columnIndex) {
        return this.resultSet.isColumnNull(columnIndex);
    }
    close() {
        return this.resultSet.close();
        this.resultSet = null;
    }
}
//# sourceMappingURL=ResultData.js.map