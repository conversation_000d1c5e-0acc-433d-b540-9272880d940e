export class Address {
  public static API_PATH_SECURE: string;
  public static SERVER_HOST: string;
  public readonly type: number;
  public readonly name: string;
  public apiAddr: string;
  public apiIPV6Addr: string;
  public webAddr: string;
  public mqttAddr: string;
  public readonly mqttPort: number;
  public mqttHttpAddr: string;
  public readonly mqttHttpPort: number;

  constructor(
    type: number,
    name: string,
    apiAddr: string,
    apiIPV6Addr: string,
    webAddr: string,
    mqttAddr: string,
    mqttPort: number,
    mqttHttpAddr: string,
    mqttHttpPort: number
  ) {
    this.type = type;
    this.name = name;
    this.apiAddr = apiAddr;
    this.apiIPV6Addr = apiIPV6Addr;
    this.webAddr = webAddr;
    this.mqttAddr = mqttAddr;
    this.mqttPort = mqttPort;
    this.mqttHttpAddr = mqttHttpAddr;
    this.mqttHttpPort = mqttHttpPort;
  }
}
