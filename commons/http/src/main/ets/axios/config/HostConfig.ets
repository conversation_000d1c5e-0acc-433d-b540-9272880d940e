import { Address } from './Address';

export class HostConfig {
  public static MQTT_PORT: number = 2345;
  public static MQTT_SSL_PORT: number = 443;
  public static MQTT_HTTP_PORT: number = 80;
  //线上环境
  static ONLINE = new Address(1, "线上环境", "api5.zhipin.com",
    "api-and.zhipin.com",
    "https://m.zhipin.com/",
    "chat.zhipin.com", HostConfig.MQTT_PORT,
    "hchat.zhipin.com",
    HostConfig.MQTT_HTTP_PORT);
  //
  static OFFLINE = new Address(2, "线下环境", "boss-api.weizhipin.com",
    "https://boss-m.weizhipin.com/",
    '',
    "**************", HostConfig.MQTT_PORT,
    "**************",
    HostConfig.MQTT_HTTP_PORT)
  //QA环境
  static QA = new Address(3, "QA环境", "boss-api-qa.weizhipin.com",
    '',
    "https://boss-m-qa.weizhipin.com/",
    "mqtt-qa.weizhipin.com", HostConfig.MQTT_PORT,
    '',
    HostConfig.MQTT_HTTP_PORT);
  //
  static QA2 = new Address(6, "QA2环境", "boss-api-qa2.weizhipin.com",
    '',
    "https://boss-m-qa2.weizhipin.com/",
    "*************", HostConfig.MQTT_PORT,
    '',
    HostConfig.MQTT_HTTP_PORT)
  //
  static QA3 = new Address(7, "QA3环境", "boss-api-qa3.weizhipin.com",
    '',
    "https://boss-m-qa3.weizhipin.com/",
    "*************", HostConfig.MQTT_PORT,
    '',
    HostConfig.MQTT_HTTP_PORT)
  //
  static QA4 = new Address(8, "QA4环境", "boss-api-qa4.weizhipin.com",
    '',
    "https://boss-m-qa4.weizhipin.com/",
    "*************", HostConfig.MQTT_PORT,
    '',
    HostConfig.MQTT_HTTP_PORT)
  //外网环境
  static PUBLIC = new Address(4, "外网环境", "boss-api2.weizhipin.com",
    '',
    "https://boss-m-qa2.weizhipin.com/",
    "boss-chat.weizhipin.com", HostConfig.MQTT_PORT,
    '',
    HostConfig.MQTT_HTTP_PORT)
  //预发环境
  static PRE = new Address(5, "预发环境", "pre-api.bosszhipin.com",
    '',
    "https://pre-www.zhipin.com/",
    "chat-pre.zhipin.com", HostConfig.MQTT_PORT,
    "*************",
    HostConfig.MQTT_HTTP_PORT)
  /**
   * 所有地址
   */
  static ALL_ADDRESS: Address[] = [HostConfig.ONLINE, HostConfig.PRE, HostConfig.QA, HostConfig.QA2, HostConfig.QA3, HostConfig.QA4, HostConfig.OFFLINE, HostConfig.PUBLIC]
  private static CONFIG: Address;
  private static API_PATH_SECURE: string;
  private static SERVER_HOST: string;

  /**
   *  TODO 使用时先初始化
   */
  public static init(type: number) {
    HostConfig.CONFIG = HostConfig.getAddressByType(type);
  }

  public static getConfig() {
     // TODO 需要调整线上环境HostConfig
    // return HostConfig.CONFIG ?? (HostConfig.CONFIG = HostConfig.getAddressByType(HostConfig.ONLINE.type));
    return HostConfig.CONFIG ?? (HostConfig.CONFIG = HostConfig.getAddressByType(HostConfig.QA.type));
  }

  public static getAddressByType(type: number): Address {
    return HostConfig.ALL_ADDRESS.find(item => item.type == type) ?? HostConfig.ONLINE
  }

  public static getHost(): string {
    let url: string = HostConfig.API_PATH_SECURE;
    if (url == null) {
      HostConfig.SERVER_HOST = HostConfig.getConfig().apiAddr;
      url = HostConfig.API_PATH_SECURE = "https://" + HostConfig.SERVER_HOST + "/api/";
    }
    return url;
  }

  public static getTempHost(address: Address): string {
    return "https://" + address.apiAddr + "/api/";
  }

  public static getHostWithoutApi(): string {
    const host = HostConfig.getConfig().apiAddr;
    return "https://" + host + "";
  }

  public static getWebHost(): string {
    return HostConfig.getConfig().webAddr;
  }

  public static getUrlPath(url: string): string {
    if (!url) return ""
    const API = "/api/";
    let apiLocation = url.indexOf(API);
    if (apiLocation < 0) {
      return HostConfig.getUrlPath2(url);
    } else {
      return url.substring(apiLocation);
    }
  }

  public static isStartWithHost(url: string) {
    return url?.startsWith(HostConfig.getHost())
  }

  /**
   * 视频语音功能配置文件地址
   */
  public static getLiveVideoUrl():
    string {
    if (HostConfig.CONFIG != null) {
      if (HostConfig.CONFIG == HostConfig.ONLINE) {
        return "";
      }
      if (HostConfig.CONFIG == HostConfig.OFFLINE) {
        return "http://*************:80/getEdge";
      }
      if (HostConfig.CONFIG == HostConfig.QA) {
        return "http://qa-nebula.weizhipin.com:8888/getEdge";
      }
      if (HostConfig.CONFIG == HostConfig.PRE) {
        return "http://pre-nebula.weizhipin.com/getEdge";
      }
      return "http://************:80/getEdge";
    }
    return "";
  }

  public static getNebulaIMUrl(): string {
    if (HostConfig.CONFIG != null) {
      if (HostConfig.CONFIG == HostConfig.ONLINE) {
        return "https://nebula.zhipin.com";
      }

      if (HostConfig.CONFIG == HostConfig.OFFLINE || HostConfig.CONFIG == HostConfig.QA) {
        return "https://qa-nebula.weizhipin.com";
      }
      if (HostConfig.CONFIG == HostConfig.PRE) {
        return "https://pre-nebula.zhipin.com";
      }
      return "https://nebula.zhipin.com";
    }

    return "";
  }

  public static getNebulaUrl(): string {
    if (HostConfig.CONFIG != null) {
      if (HostConfig.CONFIG == HostConfig.ONLINE) {
        return "https://nebula.zhipin.com";
      }
      if (HostConfig.CONFIG == HostConfig.OFFLINE || HostConfig.CONFIG == HostConfig.QA) {
        return "https://qa-nebula.weizhipin.com";
      }
      if (HostConfig.CONFIG == HostConfig.PRE) {
        return "https://pre-nebula.zhipin.com";
      }
      return "https://nebula.zhipin.com";
    }

    return "";
  }

  /**
   * 处理 非api接口
   */
  private static getUrlPath2(url: string): string {
    if (!url) return ""
    const API = "/napi/";
    let apiLocation = url.indexOf(API);
    if (apiLocation < 0) {
      return url;
    } else {
      return url.substring(apiLocation);
    }
  }
}

