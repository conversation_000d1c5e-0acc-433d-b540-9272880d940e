import URL from '@ohos.url';
import { HostConfig } from './HostConfig';


export function buildUrl(api: string): string {
  return HostConfig.getHost() + api;
}

export function buildUrlWithoutApi(api: string): string {
  return HostConfig.getHostWithoutApi() + api;
}

export function isNeedLoginUrl(url: string): boolean {
  let path = HostConfig.getUrlPath(url)
  return !URLConfig.NONE_TOKEN_URL_LIST.includes(path);
}

export function transformUrlHost(url: string): string {
  if (HostConfig.getConfig() != HostConfig.ONLINE) {
    return url;
  }
  const path = HostConfig.getUrlPath(url);
  if (URLConfig.LOG_HOST_URL_LIST.includes(url)) {
    return "https://logapi-and.zhipin.com" + path;
  }
  if (URLConfig.DIAN_ZHANG_HOST_URL_LIST.includes(url)) {
    return "https://www.dianzhangzhipin.com" + path;
  }
  //TODO DNSCommon.isSupportIpV6()
  // if (DNSCommon.isSupportIpV6() && url.startsWith(getApiHost()) &&
  // !TextUtils.isEmpty(HostConfig.CONFIG.getApiIPV6Addr())) {
  //   return "https://" + HostConfig.CONFIG.getApiIPV6Addr() + path;
  // }
  return url;
}

export function getBatchMethodName(url: string) {
  let method = ""

  try {
    url = URL.URL.parseURL(url).pathname
  } catch (e) {
  } finally {
    if (url.startsWith("api/")) {
      url = url.substring("api/".length)
    } else if (url.startsWith("/api/")) {
      url = url.substring("/api/".length)
    }
    if (url.charAt(0) === "/") {
      url = url.substring("/".length)
    }
    method = url.split("?")[0]
  }

  /**
   * /\//g 表示一个正则表达式模式，其中：
   * / 是表示正则表达式的开始和结束
   * \/ 匹配斜杠字符
   * g 表示全局搜索，即在整个字符串中进行搜索并替换所有匹配项
   */
  // return method.replace(/\//g, "\.")
  return method.replace(new RegExp('\/', 'g'), ".");
}

export class URLConfig {
  public static readonly API_PATH_PREFIX: string = "/api/"
  public static readonly BOSSZP_GOVERMENT_SERVICE: string = "bosszp://bosszhipin.app/openwith?type=chaoyangHumanPhone&title=%E6%9C%9D%E9%98%B3%E5%8C%BA%E4%BA%BA%E7%A4%BE%E5%B1%80%E7%9B%91%E7%9D%A3%E7%94%B5%E8%AF%9D&phoneNumbers=(010)57596212%2C(010)65099938"
  /**
   * 获取注册协议 WEB URL
   */
  //    public static final String WEB_URL_REGISTER_PROTOCOL = HostConfig.getWebHost() + "H5/html/aboutUs/viewer.html?name=userAgreement.pdf";
  public static readonly WEB_URL_REGISTER_PROTOCOL: string = HostConfig.getWebHost() + "mpa/html/mix/agreement-detail?agreementId=registerprotocol"
  /**
   * 获取隐私协议 WEB URL
   */
  //    public static final String WEB_URL_PRIVACY_PROTOCOL = HostConfig.getWebHost() + "H5/html/aboutUs/viewer.html?name=protect-personal-information.pdf";
  public static readonly WEB_URL_PRIVACY_PROTOCOL: string = HostConfig.getWebHost() + "mpa/html/mix/agreement-detail?agreementId=personalinfopro"
  public static readonly WEB_URL_ACCOUNT_AGREEMENT_PROTOCOL: string = HostConfig.getWebHost() + "H5/html/aboutUs/viewer.html?name=accountAgreement.pdf"
  public static readonly WEB_URL_KANZHUN_CO_PLATFORM_PRIVACY_PROTOCOL: string = HostConfig.getWebHost() + "mpa/html/mix/agreement-detail?agreementId=numberoneprotocol"
  public static readonly WEB_URL_OPEN_PDF_PREFIX: string = HostConfig.getWebHost() + "H5/html/aboutUs/viewer.html?name="
  /**
   * 发布职位发布规则
   */
  public static readonly WEB_URL_PUBLISH_POSITION_PROTOCOL = HostConfig.getWebHost() + "mpa/html/mix/agreement-detail?agreementId=postrules";
  /**
   * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=*********
   * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=*********
   */
  public static readonly WEB_CHANGE_MOBILE: string = HostConfig.getWebHost() + "mpa/html/certification/change-phone-number/input-number"
  /**
   * 【附件简历】查询预览页按钮列表
   * <p>
   * https://api.weizhipin.com/project/30/interface/api/264042
   */
  public static readonly URL_ZPGEEK_APP_GEEK_RESUME_PREVIEW_BTN_QUERY: string = buildUrl("zpgeek/cvapp/geek/resume/preview/btn/query")
  /**
   * https://api.weizhipin.com/project/30/interface/api/286758
   */
  public static readonly URL_ZPBOSS_APP_RESUME_SHARE_TO_MINI: string = buildUrl("zpboss/app/resume/share/toMini")
  /**
   * https://api.weizhipin.com/project/30/interface/api/286776
   */
  public static readonly URL_ZPBOSS_APP_RESUME_SHARE_TO_H_5: string = buildUrl("zpboss/app/resume/share/toH5")
  /**
   * BOSS直聘增值服务协议
   */
  public static readonly WEB_URL_INCREMENTAL_SERVICE_PROTOCOL: string = HostConfig.getWebHost() + "mpa/html/mix/agreement-detail?agreementId=appreciationserviceprotocol"
  /**
   * 开发票
   */
  public static readonly WEB_URL_GET_RECIPES: string = HostConfig.getWebHost() + "mpa/html/fapiao/record-list"
  //    public static final String WEB_URL_GET_RECIPES = HostConfig.getWebHost() + "mpa/html/mix/agreement-detail?agreementId=invoicemanagement";

  /**
   * 3.7 推荐牛人
   */
  public static readonly URL_RECOMMEND_GEEK_LIST: string = buildUrl("zpjob/recommend/geek/list")

  public static readonly  URL_ZP_JOB_JOB_F1_JOB_CITYS = buildUrl("zpjob/job/f1/job/citys");

  public static readonly  URL_BOSS_F1_RCD_FILTERS = buildUrl("zpblock/rcd/filters");

  /**
   * 负反馈提交接口
   */
  public static readonly URL_GEEK_RECOMMEND_FEED_BACK: string = buildUrl("zpboss/app/recommend/negative/feedback")
  /**
   * B看C，Boss举报牛人
   */
  public static readonly WEB_URL_BOSS_REPORT_GEEK: string = HostConfig.getWebHost() + "mpa/html/mix/geek-report-reasons"
  /**
   * 【附件简历】查询附件简历引导
   * https://api.weizhipin.com/project/30/interface/api/260181
   */
  public static readonly URL_ZPGEEK_APP_GEEK_RESUME_TIP_QUERY: string = buildUrl("zpgeek/cvapp/geek/resume/tip/query")
  /**
   * 【附件简历】关闭附件简历引导
   * https://api.weizhipin.com/project/30/interface/api/310014
   */
  public static readonly URL_ZPGEEK_APP_GEEK_RESUME_TIP_CLOSE: string = buildUrl("zpgeek/cvapp/geek/resume/tip/close")
  /**
   * 发布职位发布规则
   */
  //    public static final String URL_PUBLISH_POSITION_PROTOCOL = HostConfig.getWebHost() + "H5/html/aboutUs/viewer.html?name=job.pdf";
  public static readonly URL_PUBLISH_POSITION_PROTOCOL: string = HostConfig.getWebHost() + "mpa/html/mix/agreement-detail?agreementId=postrules"
  /**
   * 【专业技能】保存专业技能
   * https://api.weizhipin.com/project/30/interface/api/604365
   */
  public static readonly URL_ZPGEEK_CVAPP_RESUME_PROFESSIONAL_SKILL_SAVE: string = buildUrl("zpgeek/cvapp/resume/professional/skill/save")
  /**
   * 编辑职务，高管，去填写
   */
  public static readonly URL_EDIT_POSITION_INPUT: string = HostConfig.getWebHost() + "mpa/html/certification/job_title?source=appf"
  /**
   * 公司照片看看别人怎么拍
   */
  public static readonly URL_COMPANY_PHOTO_LOOK: string = HostConfig.getWebHost() + "mpa/html/mix/company-show"
  /**
   * https://api.weizhipin.com/project/30/interface/api/285417
   */
  public static readonly URL_ZPGEEK_APP_SECURITY_SETTINGS_QUERY: string = buildUrl("zpgeek/app/security/settings/query")
  /**
   * https://api.weizhipin.com/project/30/interface/api/360691
   */
  public static readonly URL_ZPGEEK_APP_SECURITY_SETTINGS_STATUS_QUERY: string = buildUrl("zpgeek/app/security/settings/status/query")
  /**
   * <a href="https://api.weizhipin.com/project/30/interface/api/648730">...</a>
   */
  public static readonly URL_ZPGEEK_APP_SECURITY_SETTINGS_TEXT_QUERY: string = buildUrl("zpgeek/app/security/settings/text/query")
  /**
   * 获取腾讯云上传临时凭证
   * http://api.kanzhun-inc.com/project/30/interface/api/150964
   */
  public static readonly URL_ZP_COMMON_TENCENT_COS_GET_CREDENTIAL: string = buildUrl("zpCommon/tencent/cos/getCredential")
  /**
   * https://api.weizhipin.com/project/30/interface/api/312912
   */
  public static readonly URL_ZP_COMMON_VIDEO_GETUPLOAD_INFO: string = buildUrl("zpCommon/video/opteration/getUploadInfo")
  /**
   * 实名认证找回页面
   */

  public static readonly URL_PASSWORD_INDEX: string = HostConfig.getWebHost() + "userPswRetrieve/index"
  /**
   * 帮助页面
   */
  public static readonly URL_PASSWORD_HELP: string = HostConfig.getWebHost() + "mpa/html/mix/reset-password/input-phone"
  /**
   * f4牛人VIP卡片信息
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/29029
   */
  public static readonly URL_ZPITEM_GEEK_VIP_INFO: string = buildUrl("zpitemGeek/geek/vip/info")
  /**
   * 切换身份接口 参数： identityType 身份类型，0:牛人 1:boss 以后可能还要加2猎头
   */
  public static readonly URL_CHECK_IDENTITY: string = buildUrl("zpuser/identity/switch")
  /**
   * 推荐职位接口
   */
  public static readonly URL_RECOMMEND_BOSS: string = buildUrl("recommendedBoss/get")
  /**
   * 获取城市职类信息
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/144489
   */
  public static readonly URL_ZP_COMMON_CONFIG_GET_CITY_POSITION: string = buildUrl("zpCommon/config/getCityPosition")
  /**
   * 获取城市显示职类信息
   * <p>
   * http://api.weizhipin.com/project/30/interface/api/208383
   */
  public static readonly URL_ZP_COMMON_CONFIG_GET_CITY_SHOW_POSITION_WIKI: string = buildUrl("zpjob/config/getCityShowPositionWithWiki")
  public static readonly URL_ZP_COMMON_CONFIG_GET_CITY_SHOW_POSITION_WITH_FILTER: string = buildUrl("zpjob/config/getCityShowPosition/filter/recruitmentType")
  /**
   * 推荐职位接口
   */
  public static readonly URL_RECOMMEND_JOB_LIST: string = buildUrl("zpgeek/app/geek/recommend/joblist")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/104808
   */
  public static readonly URL_ZPGEEK_APP_F_1_NEWGEEK_JOBCARD: string = buildUrl("zpgeek/app/f1/newgeek/jobcard")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/104752
   */
  public static readonly URL_ZPBOSS_APP_SMURFS_CHECK: string = buildUrl("zpboss/app/smurfs/check")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/105235
   */
  public static readonly URL_ZPBOSS_APP_SMURFS_OPEN: string = buildUrl("zpboss/app/smurfs/open")
  /**
   * 直聘简历牛人vip
   * http://api.kanzhun-inc.com/project/30/interface/api/144252
   */
  public static readonly URL_ZPITEM_GEEK_VIP_GET_RESUME_DETAIL: string = buildUrl("zpitem/geek/vip/getResumeDetail")
  /**
   * 【808】feed列表引导文案判断接口
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/105935
   */
  public static readonly URL_MOMENT_GET_GET_TOPIC_TIPS: string = buildUrl("moment/get/getTopicTips")
  /**
   * 【身份切换】C身份F4切换身份入口
   * <p>
   * https://api.weizhipin.com/project/30/interface/api/375491
   */
  public static readonly URL_ZPGEEK_APP_IDENTITY_SWITCH_ENTRANCE: string = buildUrl("zpgeek/cvapp/identity/switch/entrance")
  /**
   * 批量接口（一次请求调用多个原子接口） 非登录接口
   */

  public static readonly URL_BATCH_RUN: string = buildUrl("batch/batchRunV2")
  /**
   * 跳转小程序信息接口
   * http://api.weizhipin.com/project/30/interface/api/23244
   */
  public static readonly URL_ZPGEEK_APP_MINIAPP_SCAN_UPLOADRESUME: string = buildUrl("zpgeek/app/miniapp/scan/uploadresume")
  /**
   * 微信授权
   */
  public static readonly URL_ZPPASSPORT_WX_AUTHWX: string = buildUrl("zppassport/wx/authWx")
  /**
   * 当面支付接口
   * http://api.weizhipin.com/project/30/interface/api/217376
   */
  public static readonly URL_ZPP_APP_AGENT_BZB_ORDER_DETAIL: string = buildUrl("zpp/app/agentBzbOrder/detail")
  /**
   * 生成二维码
   * http://api.kanzhun-inc.com/project/30/interface/api/149662
   */
  public static readonly URL_GENERATE_QR_CODE_IMAGE: string = buildUrl("zpweixin/qrcode/getqrcode")
  /**
   * 改变共享职位数据开关
   * http://api.kanzhun-inc.com/project/30/interface/api/148575
   */
  public static readonly URL_JOB_SHARE_RECRUIT_SWITCH_CHANGE: string = buildUrl("zpblock/job/shareRecruitSwitch/change")
  /**
   * 901 【资格证书】JD页弹窗提交数据
   */
  public static readonly URL_JOB_LABEL_SAVE: string = buildUrl("zpgeek/app/geek/job/label/guide/save")
  /**
   * 根据ip获取城市信息
   */
  public static readonly URL_ZP_COMMON_USER_GET_ATTRIBUTION_BY_IP: string = buildUrl("zpCommon/user/getAttributionByIP")
  /**
   * 1124.603 牛人获取推荐城市接口
   * https://api.weizhipin.com/project/30/interface/api/644342
   */
  public static readonly URL_GET_GEEK_EXPECT_RECOMMEND_LOCATION: string = buildUrl("zpgeek/cvapp/geek/expect/recommendlocation/query")
  /**
   * 获取推荐城市 1006
   */
  public static readonly URL_APP_RECOMMEND_CITY: string = buildUrl("zpgeek/app/recommend/city")
  /**
   * http://api.weizhipin.com/project/30/interface/api/206146
   * 【common】城市列表
   */
  public static readonly URL_ZPGEEK_APP_CITYLIST: string = buildUrl("zpgeek/app/citylist")
  public static readonly URL_ZPGEEK_APP_BUSINESSDISTRICT: string = buildUrl("zpgeek/app/businessDistrict")
  public static readonly URL_ZPGEEK_APP_SWITCH_CITY: string = buildUrl("zpgeek/app/f1/switch/city")
  public static readonly URL_ZPGEEK_JD_POPUPGUIDES_CLOSE: string = buildUrl("zpgeek/jobapp/jobdetail/popupguides/close")
  public static readonly URL_ZPGEEK_JD_OVERLAY_CLOSE: string = buildUrl("zpgeek/jobapp/jobdetail/overlay/close")
  public static readonly URL_ZPGEEK_JD_BUBBLE_CLOSE: string = buildUrl("zpgeek/jobapp/jobdetail/bubbles/close")
  /**
   * 定时上传经纬度
   */
  public static readonly URL_ZP_COMMON_REPORT_LOCATION: string = buildUrl("zpCommon/reportLocation")
  /**
   * 【common】职类列表
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/100076
   */
  public static readonly URL_ZPGEEK_APP_POSITIONLIST: string = buildUrl("zpgeek/app/positionlist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/630911
   * <p>
   * 【驻外岗位偏好】获取国家/地区基础数据配置
   */
  public static readonly URL_ZPGEEK_CVAPP_OVERSEASTRAITOPTIONS_COUNTRY_CONFIG_QUERY: string = buildUrl("zpgeek/cvapp/overseastraitoptions/country/config/query")
  /**
   * https://api.weizhipin.com/project/20/interface/api/631061
   * <p>
   * 【APP】驻外-获取地区数据
   */
  public static readonly URL_CERTIFICATION_COUNTRY_ALL_COUNTRIES: string = buildUrl("certification/country/allCountries")
  /**
   * 牛人添加好友
   */
  public static readonly URL_GEEK_ADD_FRIEND: string = buildUrl("zpgeek/app/friend/add")
  /**
   * Boss添加好友
   */
  public static readonly URL_BOSS_ADD_FRIEND: string = buildUrl("zpjob/chat/add/friend")
  public static readonly URL_ZPGEEK_APP_GEEK_JOB_SAMECITY_QUERYPOI: string = buildUrl("zpgeek/app/geek/job/samecity/querypoi")
  public static readonly URL_ZPGEEK_APP_GEEK_JOB_SAMECITY_QUERYLIST: string = buildUrl("zpgeek/app/geek/job/samecity/querylist")
  // Get: http://api.kanzhun-inc.com/project/30/interface/api/cat_4023
  public static readonly URL_MOMENT_GET_COLLECT: string = buildUrl("moment/get/collect")
  public static readonly URL_MOMENT_GET_SCENE_CARD: string = buildUrl("moment/get/scene/card")
  /**
   * 【视频简历】修改牛人视频简历权限
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/95351
   */
  public static readonly URL_ZPGEEK_APP_GEEK_VIDEORESUME_UPDATEAUTHORITY: string = buildUrl("zpgeek/cvapp/geek/videoresume/updateauthority")
  /**
   * 【视频简历】删除牛人视频简历
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/95337
   */
  public static readonly URL_ZPGEEK_APP_GEEK_VIDEORESUME_DELETE: string = buildUrl("zpgeek/cvapp/geek/videoresume/delete")
  /**
   * 删除好友关系 接口名： friendrelation.delete 参数： friendId 好友id
   */
  public static readonly URL_FRIEND_RELATION_DELETE: string = buildUrl("zprelation/friend/remove")
  /**
   * 获取BOSS推荐牛人的数量
   */
  public static readonly URL_FRIEND_RECOMMEND_GEEK_COUNT: string = buildUrl("zpboss/app/mate/share/count")
  /**
   * 原始数据配置数据返回 接口名： config.get 参数：无 不需要登录
   */
  public static readonly URL_CONFIG_GET: string = buildUrl("zpCommon/config/get")
  /**
   * 获取配置数据的数据 不需要登录 非登录接口
   */
  public static readonly URL_DATA_GET: string = buildUrl("zpCommon/data/get")
  /**
   * 获取技能词数据 不需要登录 非登录接口
   */
  public static readonly URL_GET_SKILL_WORD: string = buildUrl("zpCommon/skillwordsConfig/get")
  /**
   * 获取商圈的基础数据
   */
  public static readonly URL_CONFIG_BUSINESS_DISTRICT: string = buildUrl("zpCommon/config/businessDistrict")
  /**
   * 获取地铁的基础数据
   */
  public static readonly URL_CONFIG_SUBWAY: string = buildUrl("zpCommon/config/subway")
  /**
   * 1004 - 新增获取行业信息
   */
  public static readonly URL_CONFIG_INDUSTRY: string = buildUrl("zpCommon/config/industry")
  /**
   * 1004 - 获取城市数据
   */
  public static readonly URL_CONFIG_CITY: string = buildUrl("zpCommon/config/city")
  /**
   * 1004 - 获取薪资数据
   */
  public static readonly URL_CONFIG_SALARY: string = buildUrl("zpCommon/config/salary")
  /**
   * 1004 - 获取学历学位
   */
  public static readonly URL_CONFIG_DEGREE: string = buildUrl("zpCommon/config/degree")
  /**
   * 1004 - 获取实习职位
   */
  public static readonly URL_CONFIG_INTERN: string = buildUrl("zpCommon/config/internPosition")
  /**
   * 1004 - 获取学历学位
   */
  public static readonly URL_CONFIG_EXPERIENCE: string = buildUrl("zpCommon/config/experience")
  /**
   * 1004 - 获取公司规模
   */
  public static readonly URL_CONFIG_SCALE: string = buildUrl("zpCommon/config/scale")
  /**
   * 1004 - 获取公司融资阶段
   */
  public static readonly URL_CONFIG_STAGE: string = buildUrl("zpCommon/config/stage")
  /**
   * 1004 - 获取院校筛选
   */
  public static readonly URL_CONFIG_SCHOOL_SEARCH: string = buildUrl("zpCommon/config/schoolSearch")
  /**
   * 1004 - 获取兼职结算类型
   */
  public static readonly URL_CONFIG_PARTIME_PAYTYPE: string = buildUrl("zpCommon/config/partTimeJobPayType")
  /**
   * 1004 - 获取距离筛选
   */
  public static readonly URL_CONFIG_DISTANCE_FILTER: string = buildUrl("zpCommon/config/distanceFilter")
  /**
   * 1004 - 获取Boss筛选
   */
  public static readonly URL_CONFIG_BOSS_FILTER: string = buildUrl("zpCommon/config/bossFilterConfig")
  /**
   * 1004 - 获取Boss筛选
   */
  public static readonly URL_CONFIG_GEEK_FILTER: string = buildUrl("zpCommon/config/geekFilterConfig")
  /**
   * 1121 - 【驻外岗位偏好】获取驻外时长基础数据配置
   * https://api.weizhipin.com/project/30/interface/api/630929
   */
  public static readonly URL_OVERSEAS_DURATION: string = buildUrl("zpCommon/config/getDurationConfig")
  /**
   * 1121 - 【驻外岗位偏好】获取语言基础数据配置
   * https://api.weizhipin.com/project/30/interface/api/630920
   */
  public static readonly URL_OVERSEAS_LANGUAGE: string = buildUrl("zpCommon/config/getLanguageConfig")
  /**
   * 获取附近距离基础数据
   */
  public static readonly URL_CONFIG_DISTANCE: string = buildUrl("config/distanceFilter")
  /**
   * 获取牛人筛选基础数据
   */
  public static readonly URL_APP_FILTER_DATA: string = buildUrl("zpgeek/app/filter/data")
  /**
   * 不需要登录信息
   */
  public static readonly URL_PHOTO_UPLOAD_NO_LOGIN: string = buildUrl("zpupload/naUploadImage")
  /**
   * 上传聊天信息 参数： file ：文件 multipartType：多媒体类型 0：图片 1：语音
   */
  public static readonly URL_PHOTO_UPLOAD: string = buildUrl("zpupload/uploadSingle")
  /**
   * 获取用户Boss身份的信息
   */
  public static readonly URL_GET_BOSS_DETAIL: string = buildUrl("zpboss/app/boss/get/detail")

  /**
   * 编辑添加职位
   */
  public static readonly URL_JOB_UPDATE: string = buildUrl("zpjob/job/update")
  /**
   * https://api.weizhipin.com/project/30/interface/api/553843
   * 【快速优化】查询快速优化结果
   */
  public static readonly URL_RESUME_GPT_REQUST: string = buildUrl("zpgeek/cvapp/resume/content/gpt/optimize")
  public static readonly URL_BOSS_JOB_DESC_GPT_GENERATE: string = buildUrl("zpjob/job/desc/gpt/generate")
  public static readonly URL_GREET_JOB_GET_GPT_GREETING: string = buildUrl("zpchat/greeting/job/getSuggestGreeting")
  /**
   * 发布/更新前置信息
   * 720获取是否需要展示代招公司字段
   */
  public static readonly URL_JOB_UPDATE_PRE_INFO: string = buildUrl("zpjob/job/update/pre/info")
  /**
   * 【工作经历】工作经历职类列表
   * <p>
   * https://api.weizhipin.com/project/30/interface/api/519590
   */
  public static readonly URL_ZPGEEK_CVAPP_WORKEXP_POSITION_CONFIG: string = buildUrl("zpgeek/cvapp/workexp/position/config")
  public static readonly URL_JOB_PREFERRED_PRE_INFO: string = buildUrl("zpjob/preferred/pre/info")
  /**
   * 判断中介职位是否可匿名
   * 806 判断中介选择的该代招品牌是否可以匿名
   * http://api.kanzhun-inc.com/project/30/interface/api/94826
   */
  public static readonly URL_JOB_ANONYMOUS_CHECK: string = buildUrl("zpjob/job/anonymous/check")
  /**
   * 获取新增客户公司URL
   * http://api.kanzhun-inc.com/project/20/interface/api/112575
   */
  public static readonly URL_BOSS_RECRUITCOMPANY_GETADDCOMURL: string = buildUrl("zpboss/app/recruitCompany/getAddComUrl")
  /**
   * 删除职位
   */
  public static readonly URL_JOB_DELETE: string = buildUrl("zpjob/job/delete")
  /**
   * 上报空地址
   */
  public static readonly URL_JOB_ADDRESS_MISS_REPORT: string = buildUrl("zpjob/job/address/miss/report")
  /**
   * 人机验证使用哪种方式
   */
  public static readonly URL_MACHINE_VALIDATION: string = buildUrl("zppassport/man/machine")
  /**
   * 一键登录SDK  获取移动sdk
   * https://api.weizhipin.com/project/30/interface/api/436075
   */
  public static readonly URL_PHONE_MOBILE_SDK_INFO: string = buildUrl("zppassport/phone/mobileSdk")
  /**
   * https://api.weizhipin.com/project/30/interface/api/10903
   */
  public static readonly URL_PHONE_MOBILE_VERIFY: string = buildUrl("zppassport/phone/mobileVerify")
  /**
   * DZ 登录
   * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=*********
   */
  public static readonly URL_DZ_MACHINE_VALIDATION: string = buildUrl("zppassport/dzz/user/manMachine")
  public static readonly URL_DZ_REGSEND_CODE: string = buildUrl("zppassport/dzz/user/sendPhoneCode")
  public static readonly URL_DZ_LOGIN_CODE: string = buildUrl("zppassport/dzz/user/codeRegistered")
  /**
   * 游客-获取手机号
   * http://api.kanzhun-inc.com/project/30/interface/api/192309
   */
  public static readonly URL_ZPPASSPORT_USER_GET_PHONE: string = buildUrl("zppassport/user/getPhone")
  /**
   * 游客-激活手机号（本机号码登录）
   * http://api.kanzhun-inc.com/project/30/interface/api/192260
   */
  public static readonly URL_ZPPASSPORT_USER_ACTIVE_PHONE: string = buildUrl("zppassport/user/activePhone")
  /**
   * 获取验证码
   */
  public static readonly URL_REGSEND_CODE: string = buildUrl("zppassport/phone/smsCode")
  /**
   * 获取图片验证码
   */
  public static readonly URL_GET_IMAGE_CODE: string = buildUrl("zppassport/phone/imgCode")
  /**
   * 使用验证码登录
   */
  public static readonly URL_LOGIN_CODE: string = buildUrl("zppassport/user/codeLogin")
  /**
   * 更换设备自动登录
   */
  public static readonly URL_LOGIN_AUTO: string = buildUrl("zppassport/user/autoLogin")
  public static readonly URL_CHAT_TIP: string = buildUrl("zpblock/notopen/job/chat/tip")
  /**
   * 登录接口
   */
  public static readonly URL_LOGIN: string = buildUrl("zppassport/user/login")
  /**
   * 退出登录
   */
  public static readonly URL_LOGOUT: string = buildUrl("zppassport/user/logout")
  /**
   * 重置密码接口
   */
  public static readonly URL_RESET_PASSWORD: string = buildUrl("zppassport/user/resetPassword")
  /**
   * 检查密码是否重复（6.09）
   */
  public static readonly URL_VERIFY_PWD: string = buildUrl("zppassport/user/verifyPassword")
  /**
   * 修改密码接口
   */
  public static readonly URL_MODIFY_PASSWORD: string = buildUrl("zppassport/user/modifyPassword")
  /**
   * 用户未用密码登录时，设置密码
   */
  public static readonly URL_SETUP_PASSWORD: string = buildUrl("zppassport/user/setPassword")
  /**
   * 升级类型： 1:app升级 2:检测配置（职位、城市。。json串）升级
   */
  public static readonly URL_UPGRADE_CHECK: string = buildUrl("zpCommon/upgrade/check")
  /**
   * 添加黑名单
   */
  public static readonly URL_ADD_BLACKLIST: string = buildUrl("zprelation/userBlack/add")
  /**
   * 删除黑名单
   */
  public static readonly URL_DELETE_BLACKLIST: string = buildUrl("zprelation/userBlack/delete")
  /**
   * 添加默认头像
   */
  public static readonly URL_EDIT_HEADIMG: string = buildUrl("zpuser/avatar/editDefault")
  /**
   * 代招用户是否真实头像
   */
  public static readonly URL_GET_RECRUIT_AVATAR: string = buildUrl("zpuser/getRecruitAvatar")
  /**
   * Boss获取通知数据的接口
   */
  public static readonly URL_BOSS_GET_NOTIFY_COUNT: string = buildUrl("zprelation/interaction/bossGetInfo")
  /**
   * 牛人获取通知数据的接口
   */
  public static readonly URL_GEEK_GET_NOTIFY_COUNT: string = buildUrl("zprelation/interaction/geekGetInfo")
  /**
   * 统计埋点
   */
  public static readonly URL_COMMON_BATCH_STATISTICS: string = buildUrl("zpCommon/batch/statistic")
  public static readonly URL_COMMON_BATCH_USTATISTICS: string = buildUrl("zpCommon/batch/unloginStatistics")
  /**
   * 用户获取消息设置接口
   */
  public static readonly URL_USER_GET_NOTIFY_SETTINGS: string = buildUrl("zpchat/notify/setting/get")
  /**
   * 用户修改消息设置接口
   */
  public static readonly URL_USER_UPDATE_NOTIFY_SETTINGS: string = buildUrl("zpchat/notify/setting/update")
  /**
   * 用户设置微信
   */
  public static readonly URL_USER_UPDATE_WECHAT: string = buildUrl("zpuser/weChat/update")
  /**
   * 设置联系人置顶
   */
  public static readonly URL_FRIENDRELATION_ISTOP: string = buildUrl("zprelation/friend/setTop")
  /**
   * 举报用户4.93api
   */
  public static readonly URL_USER_REPORT_4_93: string = buildUrl("zprelation/user/report")
  /**
   * 更新牛人或boss的基本信息
   */
  public static readonly URL_USER_UPDATE_BASIC_INFO: string = buildUrl("zpboss/app/boss/update/base/info")
  /**
   * 更新牛人的简历信息
   */
  public static readonly URL_USER_UPDATE_RESUME: string = buildUrl("user/updateResume")
  /**
   * 更新职位状态 0 - 正常 | 1 - 隐藏
   */
  public static readonly URL_UPDATE_JOB_STATUS: string = buildUrl("zpboss/app/job/status/update")
  /**
   * 3.1 牛人感兴趣
   */
  public static readonly URL_GEEK_TAG_INTEREST: string = buildUrl("zprelation/geekTag/updateFlag")
  /**
   * 3.1 BOSS感兴趣
   */
  public static readonly URL_BOSS_TAG_INTEREST: string = buildUrl("zprelation/bossTag/updateJobTagFlag")
  /**
   * 3.2 修改手机号
   */
  public static readonly URL_USER_CHANGE_MOBILE: string = buildUrl("zppassport/user/changeMobile")
  public static readonly URL_USER_CHANGE_ACCOUNT: string = buildUrl("zppassport/user/getChangeAccount")
  public static readonly URL_USER_BOTH_ACCOUNT_INFO: string = buildUrl("zppassport/getBothAccountInfo")
  public static readonly URL_USER_LOGOUT_CHANGE_PHONE: string = buildUrl("zppassport/logoutAndChangePhone")
  /**
   * 3.2 用户上报行业名称
   */
  public static readonly URL_BOSS_CUSTOM_INDUSTRY: string = buildUrl("zpboss/app/custom/industry")
  public static readonly URL_GEEK_CUSTOM_INDUSTRY: string = buildUrl("zpgeek/cvapp/custom/industry")
  /*
* 添加其它渠道的推送，用于更新某个Push渠道的token
*/
  public static readonly URL_TOKEN_UPDATE: string = buildUrl("zpchat/apnstoken/update")
  public static readonly URL_TOKEN_CLEAR: string = buildUrl("zpchat/apnstoken/clear")
  /*
* 3.5 获取同事推荐牛人列表
*/
  public static readonly URL_MATESHARE_BOSSMATESHAREGEEKLIST: string = buildUrl("zpboss/app/mate/shareGeekList")
  /*
* 3.5 获取同事分享的牛人详细信息
*/
  public static readonly URL_MATESHARE_BOSSMATESHAREGEEKGET: string = buildUrl("zpboss/app/mate/share/detail")
  /**
   * 3.5 扫描二维码
   */
  public static readonly URL_WEB_SCAN_EDIT: string = buildUrl("zppassport/qrcode/webScanEdit")
  public static readonly URL_WEB_SCAN_LOGIN: string = buildUrl("zppassport/qrcode/login")
  /**
   * BOSS在聊天页面保存接收附件简历邮箱
   */
  public static readonly URL_SAVE_RESUME_EMAIL: string = buildUrl("zpboss/app/boss/resume/email/save")
  /**
   * 查看牛人是否已经上传附件简历
   */
  public static readonly URL_GEEK_HAS_ANNEX_RESUME: string = buildUrl("zpgeek/cvapp/geek/emailresume/queryexist")
  /**
   * geek附件简历详情
   */
  public static readonly URL_GEEK_MAIL_RESUME_DETAIL: string = buildUrl("zpgeek/cvapp/geek/emailresume/querydetail")
  /**
   * geek转发简历到指定邮箱
   */
  public static readonly URL_GEEK_MAIL_RESUME_FORWARD: string = buildUrl("zpgeek/cvapp/geek/emailresume/forward")
  /**
   * 牛人获取boss的信息
   */
  public static readonly URL_GEEK_GET_BOSS_PROFILE: string = buildUrl("geek/getBossProfile")
  /**
   * 牛人获取Boss的职位列表
   */
  public static readonly URL_GEEK_GET_BOSS_PROFILE_JOB_LIST: string = buildUrl("zpgeek/app/geek/bossprofile/queryjoblist")
  /**
   * Boss-预览职位列表
   */
  public static readonly URL_ZPBOSS_APP_JOB_LIST_PREVIEW: string = buildUrl("zpjob/job/moment/list")
  /**
   * 【人才经纪人】获取代招职位列表
   */
  public static readonly URL_ZPGEEK_APP_TALENTBROKER_QUERYJOBLIST: string = buildUrl("zpgeek/app/talentbroker/job/list")
  /**
   * geek删除简历
   */
  public static readonly URL_GEEK_MAIL_RESUME_DELETE: string = buildUrl("zpgeek/cvapp/geek/emailresume/delete")
  /**
   * 获取开关状态
   */
  public static readonly URL_USER_GEEK_RESUME_STATUS_UPDATE: string = buildUrl("zpgeek/cvapp/geek/resume/updatestatus")
  /**
   * 获取开关状态
   */
  public static readonly URL_SWITCH_GET: string = buildUrl("zpCommon/switchs/get")
  /**
   * 根据开关类型获取开关设置
   */
  public static readonly URL_BOSS_GET_USER_SETTING: string = buildUrl("zpchat/notify/setting/getWithType")
  /**
   * 更新开关配置
   */
  public static readonly URL_BOSS_USER_SETTING_UPDATE: string = buildUrl("zpchat/notify/setting/update")
  /**
   * 合规信息采集上报
   */
  public static readonly URL_ZPCHAT_COMPLIANCE_REPORT: string = buildUrl("zpchat/compliance/report")
  /**
   * 3.7 牛人创建个人基本信息接口
   */
  public static readonly URL_GEEK_ADD_BASE_INFO: string = buildUrl("zpgeek/cvapp/geek/baseinfo/add")
  /**
   * 3.7 牛人修改个人基本信息接口
   */
  public static readonly URL_GEEK_UPDATE_BASE_INFO: string = buildUrl("zpgeek/cvapp/geek/baseinfo/update")
  /**
   * 3.7 【在线简历】工作经历时间修改
   */
  public static readonly URL_GEEK_UPDATE_WORK_INFO: string = buildUrl("zpgeek/app/geek/workexp/workdate/update")
  /**
   * 7.15 BossF3 道具列表
   */
  public static readonly URL_ZPITEM_BOSS_GET_ITEM_MALL_F_3: string = buildUrl("zpitem/boss/getItemMallF3")
  /**
   * 1108.201【商业】新增待支付订单列表
   * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=204572013
   */
  public static readonly URL_USER_UNPAID_ORDER_LIST: string = buildUrl("zpp/app/user/unBzbOrderList")
  /**
   * 1124.201 充值引导卡片查询
   * <a href="https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=227134672">...</a>
   */
  public static readonly URL_APP_USER_RECHARGE_GUIDE: string = buildUrl("zpp/app/user/rechargeGuide")
  /**
   * 1201.245 app端简历交付获取交付进度
   * <a href="https://api.weizhipin.com/project/30/interface/api/665110">...</a>
   */
  public static readonly URL_ZPITEM_DELIVER_SCHEDULE: string = buildUrl("zpitem/resumeDeliver/getDeliverSchedule");
  /**
   * 1108.214【商业】安卓端部分用户缩短支付路径实验 - 弹窗取消支付通知确认
   */
  public static readonly URL_ZPP_APP_USER_CONFIRM_NOTIFICATION: string = buildUrl("zpp/app/user/confirmNotification")
  /**
   * 1108.201【待支付订单】待支付订单校验
   * https://api.weizhipin.com/project/30/interface/api/547957
   */
  public static readonly URL_USER_UNPAID_ORDER_CHECK: string = buildUrl("zpp/app/user/unBzbOrderCheck")
  /**
   * 7.07 性别更新接口
   */
  public static readonly URL_USER_UPDATE_GENDER: string = buildUrl("user/updateGender")
  /**
   * 简历没问题-上报
   */
  public static readonly URL_GEEK_GARBAGE_NO_PROBLEM: string = buildUrl("zpgeek/cvapp/geek/resume/garbage/report")
  /**
   * https://api.weizhipin.com/project/30/interface/api/4512
   * 3.7 牛人添加、更新工作经历接口
   */
  public static readonly URL_GEEK_UPDATE_WORK_EXPERIENCE: string = buildUrl("zpgeek/cvapp/geek/workexp/save")
  /**
   * 1016.4【培训经历】新增或修改培训经历
   * https://api.weizhipin.com/project/30/interface/api/452764
   */
  public static readonly URL_GEEK_UPDATE_TRAINING_EXP: string = buildUrl("zpgeek/cvapp/resume/training/save")
  /**
   * 1016.4【培训经历】删除培训经历
   * https://api.weizhipin.com/project/30/interface/api/452771
   */
  public static readonly URL_GEEK_DELETE_TRAINING_EXP: string = buildUrl("zpgeek/cvapp/resume/training/delete")
  /**
   * 1114.604 社团/组织经历
   * https://api.weizhipin.com/project/30/interface/api/584578
   */
  public static readonly URL_GEEK_UPDATE_CLUB_EXP: string = buildUrl("zpgeek/cvapp/resume/clubexp/save")
  /**
   * 1114.604 社团/组织经历
   * https://api.weizhipin.com/project/30/interface/api/584586
   */
  public static readonly URL_GEEK_DELETE_CLUB_EXP: string = buildUrl("zpgeek/cvapp/resume/clubexp/delete")
  /**
   * 3.7 牛人删除工作经历接口
   */
  public static readonly URL_GEEK_DELETE_WORK_EXPERIENCE: string = buildUrl("zpgeek/cvapp/geek/workexp/delete")
  /**
   * 8.08 牛人判断工作经历是否是实习经历接口
   */
  public static readonly URL_GEEK_JUDGE_WORK_TYPE: string = buildUrl("zpgeek/cvapp/geek/workexp/checkworktype")
  /**
   * 3.7 牛人添加、更新教育经历接口
   */
  public static readonly URL_GEEK_UPDATE_EDU_EXPERIENCE: string = buildUrl("zpgeek/cvapp/geek/eduexp/save")
  /**
   * 3.7 牛人添加、更新教育经历接口
   */
  public static readonly URL_GEEK_DELETE_EDU_EXPERIENCE: string = buildUrl("zpgeek/cvapp/geek/eduexp/delete")
  /**
   * 3.7 牛人添加、更新项目经历接口
   */
  public static readonly URL_GEEK_UPDATE_PROJECT_EXPERIENCE: string = buildUrl("zpgeek/cvapp/geek/projexp/save")
  /**
   * 3.7 牛人添加、更新项目经历接口
   */
  public static readonly URL_GEEK_DELETE_PROJECT_EXPERIENCE: string = buildUrl("zpgeek/cvapp/geek/projexp/delete")
  /**
   * 3.7 牛人增加或更新求职意向的接口
   */
  public static readonly URL_GEEK_UPDATE_EXPECT_POSITION: string = buildUrl("zpgeek/cvapp/geek/expectposition/save")
  /**
   * 3.7 牛人删除求职意向的接口
   */
  public static readonly URL_GEEK_DELETE_EXPECT_POSITION: string = buildUrl("zpgeek/cvapp/geek/expectposition/delete")
  /**
   * 3.7 用户上报职位名称
   */
  public static readonly URL_BOSS_CUSTOM_POSITION: string = buildUrl("zpjob/job/custom/position")
  public static readonly URL_GEEK_CUSTOM_POSITION: string = buildUrl("zpgeek/cvapp/custom/position")
  /**
   * 3.7 牛人查找职位获取匹配列表
   */
  public static readonly URL_GEEK_UNLOGIN_SEARCH_HOT_WORD: string = buildUrl("zpgeek/app/geek/suggest/searchhotword4unlogin")
  /**
   * 3.7 BOSS查找职位获取匹配列表
   */
  public static readonly URL_BOSS_UNLOGIN_SEARCH_HOT_WORD: string = buildUrl("boss/uGetSearchHotWord")
  /**
   * http://api.weizhipin.com/project/30/interface/api/163858
   * 3.7 牛人查找职位获取匹配列表
   */
  public static readonly URL_GEEK_SEARCH_HOT_WORD: string = buildUrl("zpgeek/app/geek/suggest/searchhotword")
  /**
   * 3.7 牛人查找职位获取匹配列表
   * http://api.weizhipin.com/project/30/interface/api/104528
   */
  public static readonly URL_GEEK_SEARCH_AUTO_COMPLETE: string = buildUrl("zpgeek/app/geek/suggest/searchkeyword")
  /**
   * 3.7 牛人查找职位获取匹配列表
   */
  public static readonly URL_ADD_UPDATE_SOCIAL_HOME: string = buildUrl("zpgeek/cvapp/geek/socialcontact/save")
  /**
   * 3.7 牛人搜索职位
   */
  public static readonly URL_GEEK_UNLOGIN_SEARCH_JOB: string = buildUrl("geek/uSearchJob")
  /**
   * 9.16  http://api.weizhipin.com/project/30/interface/api/210908
   * 【搜索】取消搜索订阅
   */
  public static readonly URL_GEEK_SEARCH_SUBSCRIBE_DEL: string = buildUrl("zpgeek/app/search/subscribe/del")
  public static readonly URL_GEEK_SAVE_SUBWAY: string = buildUrl("zpgeek/cvapp/geek/subwaystation/save")
  /**
   * 9.17 http://api.weizhipin.com/project/30/interface/api/215422
   * 【搜索】搜索反馈选项
   */
  public static readonly URL_GEEK_SEARCH_FEEDBACK_ITEMS: string = buildUrl("zpgeek/app/search/feedback/options")
  /**
   * 9.17  http://api.weizhipin.com/project/30/interface/api/215430
   * 【搜索】提交搜索反馈
   */
  public static readonly URL_GEEK_SEARCH_FEEDBACK_ADD: string = buildUrl("zpgeek/app/search/feedback/add")
  /**
   * 1003  https://api.weizhipin.com/project/30/interface/api/254457
   * 【职位】搜索订阅列表
   */
  public static readonly URL_GEEK_SEARCH_SUBSCRIBE_LIST: string = buildUrl("zpgeek/app/search/subscribe/list")
  /**
   * 1107.81   https://api.weizhipin.com/project/30/interface/api/537751
   * 【搜索】关闭残疾人专区
   */
  public static readonly URL_GEEK_SEARCH_HANDICAPPEDZONE_CLOSE: string = buildUrl("zpgeek/app/search/handicappedzone/close")
  /**
   * 全景图片静态图片配置
   * https://api.weizhipin.com/project/30/interface/api/335763
   */
  public static readonly URL_BRAND_COMPLETE_PANORAMIC_PICTURE_STATIC_IMAGE: string = buildUrl("zpCompany/brandComplete/panoramicPicture/staticImage")
  /**
   * 全景图片引导信息
   * https://api.weizhipin.com/project/30/interface/api/413283
   */
  public static readonly URL_BRAND_COMPLETE_PANORAMIC_GUIDE_INFO: string = buildUrl("zpCompany/brandComplete/panoramicPicture/guideInfo")
  /**
   * 1003  https://api.weizhipin.com/project/30/interface/api/254466
   * 【职位】更新搜索订阅
   */
  public static readonly URL_GEEK_SEARCH_SUBSCRIBE_UPDATE: string = buildUrl("zpgeek/app/search/subscribe/update")
  /**
   * 3.7 BOSS搜索Geek
   */
  public static readonly URL_BOSS_UNLOGIN_SEARCH_GEEK: string = buildUrl("boss/uSearchGeek")
  /**
   * 720 高搜获取职类关键字
   * http://api.kanzhun-inc.com/project/30/interface/api/145033
   */
  public static readonly URL_ZPITEM_BOSS_GETPOSITIONKEYWORDS_V2: string = buildUrl("zpitem/boss/getPositionKeyWordsV2")
  public static readonly URL_ZPITEM_BOSS_GUIDESEARCHJOB: string = buildUrl("zpitem/boss/guideSearchJob")
  /**
   * 3.7 删除社交主页URL
   */
  public static readonly URL_REMOVE_SOCIAL_PAGE: string = buildUrl("zpgeek/cvapp/geek/socialcontact/delete")
  /**
   * 3.8 添加快捷回复
   */
  public static readonly URL_ADD_QUICK_REPLY: string = buildUrl("zpchat/fastreply/update")
  /**
   * 5.0 删除快捷回复
   */
  public static readonly URL_DELETE_QUICK_REPLY: string = buildUrl("zpchat/fastreply/delete")
  /**
   * 3.9 牛人F2获取公司列表
   */
  public static readonly URL_BRAND_LIST: string = buildUrl("zpgeek/app/geek/brand/querylist")
  /**
   * 4.0 BOSS获取道具列表
   */
  public static readonly URL_BOSS_MY_ITEM: string = buildUrl("zpitem/boss/getMyItem")
  /**
   * 4.0 牛人获取道具列表
   */
  public static readonly URL_GEEK_MY_ITEM: string = buildUrl("zpitem/geek/getMyItem")
  /**
   * 4.1 创建钱包密码
   */
  public static readonly URL_USER_SET_WALLET_PASSWORD: string = buildUrl("zpp/app/user/setWalletPw")
  /**
   * 6.15 微信提现申请
   */
  public static readonly URL_USER_APPLY_WX_WITHDRAW: string = buildUrl("zpp/app/user/applyWxWithdraw")
  /**
   * 获取钱包信息
   */
  public static readonly URL_WALLET_DATA: string = buildUrl("zpp/app/user/getWallet")
  public static readonly URL_WALLET_BEAN_BAG: string = buildUrl("zpp/app/bean/getUserBean")
  /**
   * 4.1零钱明细
   */
  public static readonly URL_WALLET_DEAL_DETAIL: string = buildUrl("zpp/app/user/getWalletDetail")
  /**
   * 4.1忘记密码,冻结账号
   */
  public static readonly URL_FREEZON_ACCOUNT: string = buildUrl("zpp/app/user/forgetWalletPw")
  /**
   * 牛人搜素学校自动补全
   */
  public static readonly URL_SEARCH_SCHOOL_AUTO_COMPLETE: string = buildUrl("zpgeek/app/geek/suggest/school")
  /**
   * 牛人搜素专业自动补全
   */
  public static readonly URL_SEARCH_MAJOR_AUTO_COMPLETE: string = buildUrl("zpgeek/app/geek/suggest/major")
  /**
   * 牛人获取打招呼语
   */
  public static readonly URL_GET_GREETING_WORDS: string = buildUrl("zpchat/greeting/get")
  /**
   * 牛人修改打招呼语
   */
  public static readonly URL_GREETING_WORDS_UPDATE: string = buildUrl("zpchat/greeting/update")
  /**
   * 牛人修改打招呼语
   */
  public static readonly URL_UPDATE_RECRUIT: string = buildUrl("zpblock/job/expire/refresh")
  /**
   * 获取豆包支付详情
   */
  public static readonly URL_ZHIDOU_DEAL: string = buildUrl("zpp/app/bean/getUserBeanDetail")
  public static readonly URL_ZHIDOU_DEAL_AGAIN: string = buildUrl("zpp/app/bean/beanDetail/bzbAgain")
  /**
   * Boss创建信息注册流程
   */
  public static readonly URL_BOSS_CREATE_INFO: string = buildUrl("zpboss/app/boss/info/add")
  /**
   * 【suggest】公司名称
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/146060
   */
  public static readonly URL_ZPGEEK_APP_GEEK_SUGGEST_COMPANY: string = buildUrl("zpgeek/app/geek/suggest/company")
  /**
   * 获得公司搜索匹配列表
   * http://************:8088/project/30/interface/api/113747
   */
  public static readonly URL_COM_LIST_SUGGEST_V2: string = buildUrl("zpboss/app/company/suggest/v2")
  /**
   * 获取公司多地址
   */
  public static readonly URL_COM_ADDRESS_LIST: string = buildUrl("zpgeek/app/job/multiaddress/query")
  public static readonly URL_GET_AREA_LIST: string = buildUrl("zpCommon/listForCityAreaGps")
  /**
   * 获得品牌匹配列表
   */
  public static readonly URL_BRAND_MATCH_LIST: string = buildUrl("zpboss/app/brand/suggest")
  /**
   * Boss获得品牌下Boss列表
   */
  public static readonly URL_BRAND_COMPANY_BOSS_LIST: string = buildUrl("zpboss/app/brandCom/mate/list")
  /**
   * Boss进行搜索
   * http://api.kanzhun-inc.com/project/30/interface/api/29407
   */
  public static readonly URL_BOSS_ADVANCED_SEARCH_START: string = buildUrl("zpitem/boss/search")
  public static readonly URL_BOSS_ADVANCED_JOB_CHANGE_INFO: string = buildUrl("zpitem/boss/search/getJobChangeInfo")
  public static readonly URL_BLOCK_JOB_BZB_INFO_QUERY: string = buildUrl("zpblock/job/bzbInfo/query")
  /**
   * 搜索筛选
   * http://api.kanzhun-inc.com/project/30/interface/api/95757
   * zpitem/boss/search/getFilterConditions
   */
  public static readonly URL_BOSS_SEARCH_GETFILTERCONDITIONS: string = buildUrl("zpitem/boss/search/getFilterConditions")
  public static readonly URL_USE_GEEK_INFO_RIGHT: string = buildUrl("zpitem/boss/useGeekInfoRight")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/145047
   */
  public static readonly URL_ZPGEEK_APP_GEEK_EXPECTPOSITION_SALARYSUGGEST: string = buildUrl("zpgeek/cvapp/geek/expectposition/salarysuggest")
  /**
   * 获取开屏广告
   */
  public static readonly URL_SCREEN_ADVERT_GET: string = buildUrl("zpCommon/user/startupAd")
  /**
   * 意见反馈的URL
   */
  public static readonly URL_FAQ_FEEDBACK_CREATE: string = buildUrl("faqFeedback/create")
  /**
   * 牛人被求电话之后的反馈
   */
  public static readonly URL_GEEK_FEEDBACK_INFO: string = buildUrl("zpitem/geek/geekInfoRightFeedBackInfo")
  /**
   * 拒绝接口
   */
  public static readonly URL_CONTACT_REJECT: string = buildUrl("zprelation/userMark/add")
  public static readonly URL_ZPITEM_QUICK_TOP_PHONE_EXCHANGE: string = buildUrl("zpitem/quickTop/phone/exchange")
  public static readonly URL_PHONE_EXCHANGE_SWITCH_CHECK: string = buildUrl("zpboss/app/job/phoneExchangeSwitch/check")
  /**
   * 取消拒绝接口
   */
  public static readonly URL_CONTACT_UNREJECT: string = buildUrl("zprelation/userMark/del")
  /**
   * 牛人获取职位卡牌接口-聊天设置界面
   */
  public static readonly URL_GEEK_GET_JOB_CARD: string = buildUrl("zpgeek/app/geek/job/card/query")
  /**
   * boss获取牛人卡牌接口－聊天设置界面
   */
  public static readonly URL_BOSS_GET_GEEK_CARD: string = buildUrl("zpjob/chat/setup/geek/card")
  /**
   * boss获取牛人备注标签和备注
   */
  public static readonly URL_BOSS_GET_GEEK_LABEL_AND_NOTE: string = buildUrl("zprelation/userMark/getAllLabelsAndNote")
  /**
   * 1019.41 获取用户给好友设置的标签
   * https://api.weizhipin.com/project/30/interface/api/505480
   */
  public static readonly URL_GET_PREVIEW_LABELS: string = buildUrl("zprelation/userMark/getFriendLabels")
  /**
   * boss添加牛人标签
   */
  public static readonly URL_BOSS_ADD_GEEK_LABEL: string = buildUrl("zprelation/userMark/addLabel")
  /**
   * boss删除牛人标签
   */
  public static readonly URL_BOSS_DELETE_GEEK_LABEL: string = buildUrl("zprelation/userMark/delLabel")
  /**
   * F1曝光统计  7.17从/api/zpupload/logCollector 接口更改为 /api/zpCommon/log/collector
   */
  public static readonly URL_LOG_COLLECTOR: string = buildUrl("zpCommon/log/collector")
  /**
   * 发送职位卡片
   */
  public static readonly URL_SEND_JOB_CARD: string = buildUrl("zpjob/job/card/send")
  /**
   * http://api.weizhipin.com/project/30/interface/api/7411
   * 【搜索】牛人搜索职位、公司
   */
  public static readonly URL_GEEK_NORMAL_SEARCH: string = buildUrl("zpgeek/app/geek/search/cardlist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/248670
   * 1003 搜索内容推荐
   */
  public static readonly URL_GEEK_SEARCH_CONTENT_RECOMMEND: string = buildUrl("moment/searchcontent/recommendV2")
  /**
   * http://api.weizhipin.com/project/30/interface/api/205665
   * 912 未完善牛人搜索职位
   */
  public static readonly URL_GEEK_NO_COMPELETE_INFO_SEARCH_JOBLIST: string = buildUrl("zpgeek/app/expect/search/joblist")
  /**
   * 907获取埋点sessionId(用于埋点)
   */
  public static readonly URL_SEARCH_GET_SESSIONID: string = buildUrl("zpgeek/app/search/superid")
  /**
   * https://api.weizhipin.com/project/30/interface/api/247023
   * 1003【引导卡片】搜索页引导添加期望
   */
  public static readonly URL_GEEK_SEARCH_TIP_QUERY: string = buildUrl("zpgeek/app/search/suggestexpect/query")
  /**
   * https://api.weizhipin.com/project/30/interface/api/300375
   * 1006【搜索】搜索相似职位列表
   */
  public static readonly URL_GEEK_SEARCH_SIMILAR_JOBLIST: string = buildUrl("zpgeek/app/geek/search/similar/joblist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/300393
   * 1006【【搜索】搜索相似公司列表
   */
  public static readonly URL_GEEK_SEARCH_SIMILAR_BRANDLIST: string = buildUrl("zpgeek/app/geek/search/similar/brandlist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/497185
   * 1017【职位】语音搜索推荐词
   */
  public static readonly URL_GEEK_VOICE_SEARCH_SUGGEST_WORDS: string = buildUrl("zpgeek/app/voice/search/suggest/words")
  /**
   * 日志文件上传接口
   * http://api.weizhipin.com/project/30/interface/api/22532
   */
  public static readonly URL_LOG_UPLOAD: string = buildUrl("zpupload/uploadAppLog")
  /**
   * 切换开聊的职位
   */
  public static readonly URL_FRIEND_CHANGE_JOB_ID: string = buildUrl("zprelation/friend/changeJobId")
  public static readonly URL_GET_CITY_POSITION: string = buildUrl("zpCommon/config/cityposition")
  /**
   * 获得广告接口
   * https://api.weizhipin.com/project/30/interface/api/13756
   */
  public static readonly URL_GET_ADVERTISE_V2: string = buildUrl("zpCommon/adActivity/getV2")
  /**
   * 【蓝领】兼职蓝领主题banner
   * https://api.weizhipin.com/project/30/interface/api/474145
   */
  public static readonly URL_GET_BLUE_TOPIC_BANNER: string = buildUrl("zpgeek/app/bluecollar/topic/banner")
  /**
   * 【F1】小城市混推 职类筛选条件
   * https://api.weizhipin.com/project/30/interface/api/526157
   */
  public static readonly URL_MIX_POSITION_FILTER: string = buildUrl("zpgeek/app/f1/mix/position/filter")
  /**
   * 获取F1面试接口 f1使用
   */
  public static readonly URL_GET_F1_INTERVIEW: string = buildUrl("zpinterview/geek/interview/f1")
  /**
   * 留学生资讯
   */
  public static readonly URL_GET_F1_ARTICE: string = buildUrl("zpgeek/app/studyabroad/article/headlines")
  /**
   * 检验邮箱
   */
  public static readonly URL_CHECK_RESUME_EMAIL: string = buildUrl("zpboss/app/boss/email/check")
  /**
   * 牛人获取已投递职位列表
   */
  public static readonly URL_GEEK_GET_POSTED_RESUME_LIST: string = buildUrl("zprelation/resume/geekDeliverList")
  /**
   * 【F4交换过列表】
   */
  public static readonly URL_GEEK_CHANGE_EXCHANGE_LIST: string = buildUrl("zprelation/exchange/getExchangeList")
  /**
   * 一键投递
   */
  public static readonly URL_ONE_KEY_DELIVER_RESUME: string = buildUrl("zpgeek/app/geek/resume/onekey/deliver")
  /**
   *
   */
  public static readonly URL_COMMON_APP_ACTIVATE: string = buildUrl("zpCommon/app/activate")
  /**
   * 提交确认登录
   */
  public static readonly URL_SUBMIT_CONFIRM: string = buildUrl("app/submitConfirm")
  /**
   * 牛人关注取消关注公司
   */
  public static readonly URL_CONCERN_COMPANY: string = buildUrl("zpgeek/app/geek/focusbrand/add")
  /**
   * 公司主页弹窗
   */
  public static readonly URL_COMPANY_DIALOG_REQUEST: string = buildUrl("zpgeek/app/brand/tip/query")
  /**
   * 导航页 的推荐职位列表
   */
  public static readonly URL_GEEK_ROUTE_JOB_LIST: string = buildUrl("zpgeek/app/bluegeek/map/recommend/joblist")
  /**
   * 公司引导曝光弹窗
   */
  public static readonly URL_COMPANY_DIALOG_EXPOSE_REQUEST: string = buildUrl("zpgeek/app/brand/tip/close")
  /**
   * 收藏公司的 推荐
   */
  public static readonly URL_COMPANY_COLLECT_RECOMMEND_LIST: string = buildUrl("zpgeek/app/focusbrand/recommend/brandlist")
  public static readonly URL_JOB_COLLECT_RECOMMEND_LIST: string = buildUrl("zpgeek/app/high/salary/joblist")
  /**
   * 我关注的公司
   */
  public static readonly URL_GET_CONCERN_COMPANY: string = buildUrl("zpgeek/app/geek/focusbrand/querylist")
  /**
   * 检测牛人期望和职位是否匹配
   */
  public static readonly URL_CHECK_POSITION_MATCH: string = buildUrl("zpgeek/app/geek/positionmatch/check")
  /**
   * 牛人上传附件简历接口
   */
  public static readonly URL_ATTACHMENT_RESUME_UPLOAD: string = buildUrl("zpupload/uploadResumeFile")
  /**
   * 牛人检测是否可以预览附件简历
   */
  public static readonly URL_ATTACHMENT_RESUME_CAN_PREVIEW: string = buildUrl("zpgeek/cvapp/geek/resume/checkpreview")
  /**
   * 生成deviceid
   */
  public static readonly URL_GENERATE_DEVICE_ID: string = buildUrl("zpCommon/app/generateDeviceId")
  /**
   * boss分享牛人
   */
  public static readonly URL_MATE_SHARE_GEEK: string = buildUrl("zpboss/app/mate/save/share/geek")
  /**
   * 实习生专业职类推荐
   */
  public static readonly URL_INTERN_SUGGEST: string = buildUrl("zpgeek/cvapp/geek/expectposition/recommendinternposition")
  /**
   * 817【搜索】搜索推荐职类词
   * http://api.kanzhun-inc.com/project/30/interface/api/145101
   */
  public static readonly URL_SEARCH_RECOMMEND_POSITION: string = buildUrl("zpgeek/app/geek/search/recommendposition")
  /**
   * 提交公司全称,返回品牌列表
   * http://api.kanzhun-inc.com/project/30/interface/api/147317
   */
  public static readonly URL_COMPANY_SUGGEST_BRAND_LIST: string = buildUrl("zpboss/app/brandCom/suggest")
  /**
   * 根据公司全称、全称ID、品牌ID、品牌名称和行业获取匹配
   */
  public static readonly URL_BRAND_MATCH_DETAIL: string = buildUrl("zpboss/app/brandCom/match/detail")
  /**
   * 更换公司全称和品牌
   */
  public static readonly URL_BRAND_COM_JOIN: string = buildUrl("zpboss/app/brandCom/join")
  /**
   * 技能标签
   */
  public static readonly URL_SKILL: string = buildUrl("zpCommon/position/skill")
  /**
   * vip权益账号详情
   */
  public static readonly URL_VIP_RIGHTS: string = buildUrl("zpblock/page/vip/privilege")
  /**
   * 在线客服
   */
  /**
   * Boss筛选牛人
   */
  public static readonly URL_BOSS_FILTER: string = buildUrl("friendrelation/bossFilter")
  /**
   * 牛人筛选标签
   */
  public static readonly URL_GEEK_FILTER: string = buildUrl("friendrelation/geekFilter")
  /**
   * 职位名称校验
   */
  public static readonly URL_JOB_CHECK_NAME: string = buildUrl("zpjob/job/name/check")
  public static readonly URL_JOB_QUICK_PUB_CHECK_NAME: string = buildUrl("zpjob/job/quick/pub/name/check")
  /**
   * 牛人详情三合一接口
   */
  public static readonly URL_GEEK_DETAIL_MIX: string = buildUrl("zpjob/view/geek/info")
  public static readonly URL_GEEK_DETAIL_MIX_UNLOGIN: string = buildUrl("zpboss/app/visitor/geek/detail")
  /**
   * 获取要删除的最大messageId
   */
  public static readonly DISCARD_MESSAGE_ID: string = buildUrl("zpchat/message/discardlastmsgid")
  /**
   * 更新FriendId#Message
   */
  public static readonly SYNC_RECENT: string = buildUrl("zpchat/message/syncRecent")
  /**
   * 检测职务是否可以修改
   */
  public static readonly URL_CHECK_POSITION_NAME: string = buildUrl("zpboss/app/boss/edit/check")
  /**
   * Boss搜索职务
   */
  public static readonly URL_BOSS_POSITION_SEARCH: string = buildUrl("zpboss/app/boss/title/suggest")
  /**
   * Boss搜索职务(新)
   */
  public static readonly URL_BOSS_POSITION_SEARCH_NEW: string = buildUrl("zpboss/app/boss/title/suggest/v2")
  /**
   * 获取安全框架url
   */
  public static readonly URL_GET_SECURITY_URL: string = buildUrl("certification/security/get")
  /**
   * 检测牛人简历是否合格
   */
  public static readonly CHECK_RESUME_QUALITY: string = buildUrl("zpgeek/cvapp/geek/resume/queryquality")
  /**
   * 【黄条】修改求职状态黄条
   */
  public static readonly APPLY_STATUS_CHANGE_TIPS: string = buildUrl("zpgeek/cvapp/applystatus/change/tip")
  /**
   * 【黄条】修改求职状态黄条
   */
  public static readonly URL_GEEK_EDUEXP_CHANGE_TIP: string = buildUrl("zpgeek/cvapp/eduexp/change/tip")
  /**
   * 【F1】多眉毛类型筛选项
   */
  public static readonly URL_GEEK_RECOMMEND_FILTER: string = buildUrl("zpgeek/app/f1/recommend/filter")
  /**
   * 【黄条】修改求职状态黄条关闭按钮
   */
  public static readonly URL_GEEK_EDUEXP_CHANG_TIP_CLOSE: string = buildUrl("zpgeek/cvapp/eduexp/change/tip/close")
  /**
   * 5.48 Boss查找获取匹配列表
   * <p>
   * 808.32修改：将搜索匹配接口 zpboss/app/boss/search/autoComplete -> 调整为：zpitem/boss/search/autoComplete
   */
  public static readonly URL_BOSS_SEARCH_AUTO_COMPLETE: string = buildUrl("zpitem/boss/search/autoComplete")
  /**
   * 5.49 点击新版动态条
   */
  public static readonly URL_CLICK_DYNAMIC_BAR_V2: string = buildUrl("zpuser/dynamicBar/click")
  /**
   * 6.12 牛人搜索屏蔽公司建议接口
   */
  public static readonly URL_SUGGEST_MASK_COM_NAME: string = buildUrl("zpgeek/app/geek/suggest/maskcompanyname")
  /**
   * 5.49 牛人搜索屏蔽公司列表接口
   */
  public static readonly URL_SUGGEST_MASK_COMPANY_LIST: string = buildUrl("zpgeek/app/geek/maskcompany/suggest")
  /**
   * 5.49牛人添加屏蔽公司列表接口
   */
  public static readonly URL_ADD_MASK_COMPANY: string = buildUrl("zpgeek/app/geek/maskcompany/add")
  /**
   * 5.49 牛人获取屏蔽公司列表接口
   */
  public static readonly URL_MASK_COMPANY_LIST: string = buildUrl("zpgeek/app/geek/maskcompany/querylist")
  /**
   * 902.13 【屏蔽公司】获取关联品牌和邮箱
   * http://api.kanzhun-inc.com/project/30/interface/api/153176
   */
  public static readonly URL_MASK_COMPANY_RELATIONS: string = buildUrl("zpgeek/app/geek/maskcompany/relations/query")
  /**
   * 1003.13【屏蔽公司】异步解除屏蔽公司
   * https://api.weizhipin.com/project/30/interface/api/153183
   */
  public static readonly URL_MASK_COMPANY_UNBLOCK: string = buildUrl("zpgeek/app/geek/maskcompany/unblock")
  /**
   * 902.13 【屏蔽公司】判断屏蔽公司是否解除完成-轮询接口
   * http://api.kanzhun-inc.com/project/30/interface/api/153190
   */
  public static readonly URL_MASK_COMPANY_UNBLOCK_CHECK: string = buildUrl("zpgeek/app/geek/maskcompany/unblock/check")
  /**
   * 5.49 牛人移除屏蔽公司列表接口
   */
  public static readonly URL_DEL_MASK_COMPANY: string = buildUrl("zpgeek/app/geek/maskcompany/delete")
  /**
   * 获取职位详情
   */
  //    public static final String URL_GET_JOB_DETAIL = buildUrl("zpboss/app/job/detail",
  public static readonly URL_GET_JOB_DETAIL: string = buildUrl("zpjob/job/detail")
  /**
   * 用户设置不再提示某种类型的开聊提醒
   */
  public static readonly URL_USER_CHAT_REMIND_NO_MORE: string = buildUrl("zpgeek/app/geek/chatremind/shownomore/set")
  /**
   * 聊天加油包
   */
  public static readonly URL_VIP_CHEER_PACK: string = buildUrl("zpblock/page/app/chatBag")
  /**
   * 牛人获取感兴趣/沟通过的职位
   */
  public static readonly URL_GEEK_GET_JOBLIST_BY_JOBTAG: string = buildUrl("zprelation/geekTag/jobTag/geekGetJob")
  /**
   * 1017 兼职蓝领专区列表
   * https://api.weizhipin.com/project/30/interface/api/474172
   */
  public static readonly URL_ZPGEEK_BLUECOLLAR_TOPICK_JOBLIST: string = buildUrl("zpgeek/app/bluecollar/topic/joblist")
  /**
   * 1119 学生主题专区主题职位列表
   * https://api.weizhipin.com/project/30/interface/api/619754
   */
  public static readonly URL_ZPGEEK_STU_ZONE_JOBLIST: string = buildUrl("zpgeek/app/student/operate/zone/topic/joblist")
  /**
   * 1119 学生主题专区主题职位banner
   * https://api.weizhipin.com/project/30/interface/api/619709
   */
  public static readonly URL_STUDENT_FEATRUE: string = buildUrl("zpgeek/app/student/feature/query")
  /**
   * 1020.141 获取牛人推荐职位
   * https://api.weizhipin.com/project/30/interface/api/505426
   */
  public static readonly URL_1020_CHATDIALOG_JOBLIST: string = buildUrl("zprelation/recommend/geekGetJobs")
  /**
   * 【F1】推荐职位列表推荐主题职位列表
   * https://api.weizhipin.com/project/30/interface/api/502933
   */
  public static readonly URL_ZPGEEK_1019RCMD_TOPICK_JOBLIST: string = buildUrl("zpgeek/app/recommend/topic/joblist")
  /**
   * 【F1】行为动态推荐职位列表
   * https://api.weizhipin.com/project/30/interface/api/510160
   **/
  public static readonly URL_ZPGEEK_1102_TOPICK_JOBLIST: string = buildUrl("zpgeek/app/behavior/recommend/joblist")
  /**
   * 【无障碍求职】无障碍专区职位列表
   * https://api.weizhipin.com/project/30/interface/api/498787
   */
  public static readonly URL_ZPGEEK_HANDIZONE_JOBLIST: string = buildUrl("zpgeek/app/handicappedzone/joblist")
  /**
   * 1017 兼职蓝领专区详情
   * https://api.weizhipin.com/project/30/interface/api/474163
   */
  public static readonly URL_ZPGEEK_APP_BLUECOLLAR_DETAIL: string = buildUrl("zpgeek/app/bluecollar/topic/detail")
  /**
   * 1119 学生主题专区主题详情
   * https://api.weizhipin.com/project/30/interface/api/619745
   */
  public static readonly URL_APP_STUDENT_ZONE_DETIAL: string = buildUrl("zpgeek/app/student/operate/zone/topic/detail")
  public static readonly URL_ZPGEEK_THEME_ENTRANCE_DETAIL: string = buildUrl("zpgeek/app/student/topic/detail")
  public static readonly URL_ZPGEEK_THEME_ENTRANCE_JOB_LIST: string = buildUrl("zpgeek/app/student/topic/joblist")
  /**
   * 1018 【无障碍求职】无障碍职位专区详情
   * https://api.weizhipin.com/project/30/interface/api/498760
   */
  public static readonly URL_ZPGEEK_APP_HANDI_ZONE_DETAIL: string = buildUrl("zpgeek/cvapp/handicappedzone/detail")
  /**
   * 1018 【无障碍求职】修改无障碍职位订阅状态
   * https://api.weizhipin.com/project/30/interface/api/498778
   */
  public static readonly URL_ZPGEEK_APP_HANDI_STATUS_UPDATE: string = buildUrl("zpgeek/cvapp/handicapped/jobsubstatus/update")
  /**
   * 牛人 收藏过的行业列表
   * https://api.weizhipin.com/project/477/interface/api/324090
   */
  public static readonly URL_GEEK_GET_INDUSTRY_COLLECT: string = buildUrl("zpgeek/app/focus/industryatlas/querylist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/454045
   */
  public static readonly URL_GEEK_ADDRESS_CONTINUE_CHAT: string = buildUrl("zpgeek/app/friend/continuechat")
  /**
   * Boss获取感兴趣/沟通过的牛人
   */
  public static readonly URL_BOSS_GET_GEEKLIST_BY_GEEKTAG: string = buildUrl("zprelation/bossTag/bossGetGeek")
  public static readonly URL_BOSS_GET_BOSS_GET_GEEK_FOR: string = buildUrl("zprelation/bossTag/bossGetGeekFor7Days")
  /**
   * Boss在自己的职位详情中查看沟通过和查看过的牛人
   */
  public static readonly URL_BOSS_GET_GEEKLIST_BY_JOBTAG: string = buildUrl("zprelation/bossTag/jobTag/bossGetGeek")
  /**
   * 获取mqtt ip地址
   * 5.5.2添加
   */
  public static readonly URL_MQTT_IP: string = buildUrl("zpCommon/config/ips")
  /**
   * 获取服务端配置接口
   * 5.5.2添加
   */
  public static readonly URL_SERVER_CONFIG: string = buildUrl("zpCommon/config/custom")
  /**
   * Boss获得牛人【感兴趣】【看过我】【新牛人】
   */
  public static readonly URL_BOSS_GET_GEEK: string = buildUrl("zprelation/interaction/bossGetGeek")
  /**
   * F3互动职位列表
   * http://api.kanzhun-inc.com/project/30/interface/api/336
   */
  public static readonly URL_GEEK_GET_BOSS: string = buildUrl("zprelation/interaction/geekGetJob")
  public static readonly URL_F2_RECOMMEND_JOB_LIST: string = buildUrl("zprelation/interaction/geekGetHotJobRec")
  public static readonly URL_GEEK_JOB_RECOMMEND: string = buildUrl("zprelation/geekGetJobRecommend")
  /**
   * 查询F2是否需需要弹窗 1002 56
   */
  public static readonly URL_GEEK_F2_SHOW_FEED_BACK_SHOW: string = buildUrl("zpgeek/app/f2/tip/query")
  /**
   * F2弹窗关闭 1002 56
   */
  public static readonly URL_GEEK_F2_SHOW_FEED_BACK_CLOSE: string = buildUrl("zpgeek/app/f2/tip/close")
  /**
   * 是否是限制用户校验接口，点击交换电话和微信混合按钮时调用
   * http://api.kanzhun-inc.com/project/30/interface/api/146679
   */
  public static readonly URL_EXCHANGE_CHECK_USER: string = buildUrl("zpchat/exchange/checkUser")
  /**
   * 猜你想看不推荐暂停7天
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/88064
   */
  public static readonly URL_ZPRELATION_INTERACTION_DISABLE_GUESS_DZ_EXPECT: string = buildUrl("zprelation/interaction/disableGuessDZExpect")
  public static readonly URL_MOMENT_GET_RANDOM_USER: string = buildUrl("moment/get/randomUser")
  public static readonly URL_MOMENT_GET_SAVE_USER_INFO: string = buildUrl("moment/get/saveUserInfo")
  public static readonly URL_MOMENT_DISCOVER_HOME: string = buildUrl("moment/discover/home")
  public static readonly URL_GEEK_RESUME_SHOW: string = buildUrl("zpgeek/cvapp/geek/resume/layer/show")
  /**
   * 获取关注公司的职位列表
   */
  public static readonly URL_GET_FOCUS_COMPANY_JOB_LIST: string = buildUrl("zpgeek/app/geek/focusbrand/queryjoblist")
  public static readonly URL_GET_FOCUS_COMPANY_NEW_JOBS: string = buildUrl("zpgeek/app/focusbrand/queryjoblist")
  /**
   * 职位收藏 新职位列表
   */
  public static readonly URL_GET_FOCUS_POSITION_NEW_JOBS: string = buildUrl("zprelation/geekTag/geekInterestRec")
  public static readonly URL_GET_FOCUS_RED_DOT: string = buildUrl("zpgeek/app/focusbrand/redpoint")
  /**
   * 获取好友全量信息
   */
  public static readonly URL_FRIEND_FULL_INFO: string = buildUrl("zprelation/friend/getFullInfo")
  /**
   * 获取好友全量信息
   */
  public static readonly URL_FRIEND_BASE_INFO_LIST: string = buildUrl("zprelation/friend/getBaseInfoList")
  /**
   * 获取部分联系人列表
   * https://api.weizhipin.com/project/30/interface/api/284454
   */
  public static readonly URL_FRIEND_BASE_INFO: string = buildUrl("zprelation/friend/getBaseInfo")
  /**
   * https://api.weizhipin.com/project/30/interface/api/642071
   */
  public static readonly URL_FRIEND_LIST: string = buildUrl("zprelation/friend/getFriendIdList")
  /**
   * https://api.weizhipin.com/project/30/interface/api/110307
   * <p>
   * 【个人信息完善】看看别人怎么写查询
   */
  public static readonly URL_ZPGEEK_APP_OTHERS_INTRODUCE_QUERY: string = buildUrl("zpgeek/cvapp/others/introduce/query")
  public static readonly URL_ZPGEEK_APP_OTHERS_INTRODUCE_IMPROVEMENT_QUERY: string = buildUrl("zpgeek/cvapp/others/introduce/improvement/query")
  /**
   * 邮件上传附件简历
   */
  public static readonly URL_RESUME_UPLOAD_VIA_EMAIL: string = buildUrl("zpgeek/cvapp/geek/emailresume/senduploadviamail")
  /**
   * 邮件上传附件简历回复结果
   */
  public static readonly URL_RESUME_UPLOAD_VIA_EMAIL_RESULT: string = buildUrl("zpgeek/cvapp/geek/emailresume/queryuploadresult")
  public static readonly URL_FEEDBACK_BRAND: string = buildUrl("geek/feedbackBrand")
  /**
   * 6.02 牛人查看品牌详情 相关职位接口
   */
  public static readonly URL_BRAND_JOB_LIST_V2: string = buildUrl("zpgeek/app/geek/brand/queryjoblist")
  /**
   * 职位联想词接口
   */
  public static readonly URL_BRAND_SUGGEST_WORK: string = buildUrl("zpgeek/app/geek/suggest/searchkeyword")
  /**
   * 6.17 Boss主页热招职位
   */
  public static readonly URL_ZPBOSS_APP_BRAND_JOB_LIST: string = buildUrl("zpboss/app/brand/job/list")
  /**
   * 相关公司 列表
   */
  public static readonly URL_ZP_GEEK_CORRELATION_COM_LIST: string = buildUrl("zpgeek/app/geek/brand/queryrelatedbrandlist")
  /**
   * 优惠券弹窗接口
   */
  public static readonly URL_BOSS_GET_COUPON_POP_UP_LIST: string = buildUrl("zpblock/coupon/boss/get/gift")
  /**
   * 领取优惠券
   */
  public static readonly URL_BOSS_OBTAIN_COUPON: string = buildUrl("zpblock/coupon/boss/receive")
  /**
   * 获取职位详情接口
   */
  public static readonly URL_JOB_DETAIL_REQUEST: string = buildUrl("zpgeek/jobapp/geek/job/querydetail")
  /**
   * 开聊提醒框架
   */
  public static readonly URL_GET_CHAT_REMIND: string = buildUrl("zpgeek/app/geek/chatremind/check")
  /**
   * 受邀的群列表
   */
  public static readonly URL_GET_INVITED_GROUP_CONTACTS: string = buildUrl("zpchat/group/getInvitedGroups")
  /**
   * 感兴趣的群列表
   */
  public static readonly URL_GET_INTEREST_GROUP_CONTACTS: string = buildUrl("zpchat/group/getRecommGroups")
  /**
   * 已加入的群列表
   */
  public static readonly URL_GET_JOINED_GROUP_CONTACTS: string = buildUrl("zpchat/group/getJoinedGroups")
  public static readonly URL_GET_MIX_RECOMMEND_GROUP_CONTACTS: string = buildUrl("zpchat/group/getMixedRecommGroups")
  /**
   * 获得群设置信息
   */
  public static readonly URL_GET_GROUP_INFO: string = buildUrl("zpchat/group/getGroupInfo")
  /**
   * 更新群信息
   */
  public static readonly URL_UPDATE_GROUP_INFO: string = buildUrl("zpchat/group/updateGroupInfo")
  /**
   * 更新关注/消息免打扰
   */
  public static readonly URL_UPDATE_MEMBER_SETTING: string = buildUrl("zpchat/group/updateMemberSetting")
  /**
   * 移除群
   */
  public static readonly URL_GROUP_REMOVE_MEMBER: string = buildUrl("zpchat/group/removeMember")
  /**
   * 邀请入群
   */
  public static readonly URL_INVITE_USERS_TO_GROUP: string = buildUrl("zpchat/group/inviteUsersToGroup")
  /**
   * 选择邀请同公司同事列表接口
   */
  public static readonly URL_GET_USERS_BY_COMPANY: string = buildUrl("zpchat/group/getUsersByCompany")
  /**
   * 用户创建群接口
   */
  public static readonly URL_CREATE_GROUP: string = buildUrl("zpchat/group/createGroup")
  /**
   * 获得群资料
   */
  public static readonly URL_GET_GROUP_MATERIAL: string = buildUrl("zpchat/group/getGroupView")
  /**
   * 获取群成员列表
   */
  public static readonly URL_GET_GROUP_MEMBER_LIST: string = buildUrl("zpchat/group/getMembers")
  /**
   * 加入群聊
   */
  public static readonly URL_JOIN_GROUP: string = buildUrl("zpchat/group/joinGroup")
  /**
   * 获取群聊的分享职位
   */
  public static readonly URL_GET_SHARE_JOB_LIST: string = buildUrl("zpchat/group/getSharableJobs")
  /**
   * 获取群聊的分享简历
   */
  public static readonly URL_GET_SHARE_RESUME_LIST: string = buildUrl("zpchat/group/getSharableResumes")
  /**
   * 获取群名片
   */
  public static readonly URL_GET_MEMBER_INFO: string = buildUrl("zpchat/group/getMemberInfo")
  /**
   * 同步群成员信息
   */
  public static readonly URL_SYNC_GROUP_MEMBERS: string = buildUrl("zpchat/group/syncMembers")
  /**
   * 编辑群名片
   */
  public static readonly URL_EDIT_GROUP_USER_CARD: string = buildUrl("zpchat/group/updateGroupCard")
  /**
   * 转移群名片
   */
  public static readonly URL_TRANSFER_GROUP_ADMIN: string = buildUrl("zpchat/group/transferGroup")
  /**
   * 获取待加入的群成员
   */
  public static readonly URL_GET_INVITEES_LIST: string = buildUrl("zpchat/group/getInvitees")
  /**
   * 双人建群接口
   */
  public static readonly URL_CREATE_PRIVATE_GROUP: string = buildUrl("zpchat/group/createPrivateGroup")
  /**
   * 发送简历分享
   */
  public static readonly URL_GROUP_SEND_RESUME_SHARE: string = buildUrl("zpchat/group/sendResumeShare")
  /**
   * 发送职位分享
   */
  public static readonly URL_GROUP_SEND_JOB_SHARE: string = buildUrl("zpchat/group/sendJobShare")
  /**
   * 获取个人群名片信息
   */
  public static readonly URL_GET_USER_GROUP_CARD: string = buildUrl("zpchat/group/getGroupCard")
  /**
   * 牛人反馈收集标签
   * http://api.kanzhun-inc.com/project/30/interface/api/148031
   */
  public static readonly URL_GET_FEEDBACK_REASONS: string = buildUrl("zpchat/notify/setting/reasons")
  /**
   * 检查发送测试消息是否可用
   * http://api.kanzhun-inc.com/project/30/interface/api/143637
   */
  public static readonly URL_MESSAGE_CHECK_TEST_MSG_ENABLE: string = buildUrl("zpchat/message/checkTestMsgEnable")
  /**
   * 保存第二招呼语
   * http://api.kanzhun-inc.com/project/30/interface/api/144562
   */
  public static readonly URL_ZPCHAT_SECOND_GREETING_SAVE: string = buildUrl("zpchat/greeting/second/save")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/144567
   * 关闭第二招呼语
   */
  public static readonly URL_ZPCHAT_SECOND_GREETING_CLOSE: string = buildUrl("zpchat/greeting/second/close")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/144564
   * 校验第二招呼语状态
   */
  public static readonly URL_ZPCHAT_SECOND_GREETING_CHECK: string = buildUrl("zpchat/greeting/second/check")
  public static readonly URL_INTERACTION_BOSSGETGEEK_FILTERS: string = buildUrl("zprelation/interaction/bossGetGeek/filters")
  /**
   * 1016.250 wukong群聊用户下线校验
   * 1109.51【安心保】群聊功能优化
   * https://api.weizhipin.com/project/30/interface/api/457840
   */
  public static readonly URL_CHAT_GROUP_CHECK_WUKONG_OFFLINE: string = buildUrl("zpchat/group/userGroupEnter")
  /**
   * 1109.51 获取群聊历史分享职位
   * https://api.weizhipin.com/project/30/interface/api/554032
   */
  public static readonly URL_CHAT_GROUP_ALL_RESUME_JOB: string = buildUrl("zpchat/group/getShareJobs")
  /**
   * 1116.250 悟空群聊 获取任务发布地址
   * https://api.weizhipin.com/project/2335/interface/api/603242
   */
  public static readonly URL_CHAT_WUKONG_GET_PUBLISH_URL: string = buildUrl("zpwukong/employer/task/getTaskPublishUrl")
  /**
   * 1116.250 悟空群聊 获取聊天群聊中任务信息
   * https://api.weizhipin.com/project/2335/interface/api/603210
   */
  public static readonly URL_CHAT_WUKONG_GET_TASKS: string = buildUrl("zpwukong/employer/task/getChatGroupTask")
  /**
   * 1116.250 悟空群聊 发送任务卡片
   * https://api.weizhipin.com/project/2335/interface/api/603226
   */
  public static readonly URL_CHAT_WUKONG_SEND_TASK: string = buildUrl("zpwukong/employer/task/sendTaskCard")
  /**
   * 1116.250 悟空群聊 获取群聊案例信息
   * https://api.weizhipin.com/project/2335/interface/api/603258
   */
  public static readonly URL_CHAT_WUKONG_GET_EXAMPLES: string = buildUrl("zpwukong/supplier/getChatGroupExample")
  /**
   * 1116.250 悟空群聊 群聊案例卡片下发
   * https://api.weizhipin.com/project/2335/interface/api/603282
   */
  public static readonly URL_CHAT_WUKONG_SEND_EXAMPLE: string = buildUrl("zpwukong/supplier/sendExampleCard")
  /**
   * 职位诊断
   * http://api.kanzhun-inc.com/project/30/interface/api/76022
   */
  public static readonly URL_JOB_DESC_CHECK: string = buildUrl("zpjob/job/desc/check")
  /**
   * 获取异地招聘审核页数据
   * http://api.kanzhun-inc.com/project/30/interface/api/124620
   */
  public static readonly URL_JOB_DIFFERENT_CITY_AUDIT_PAGE: string = buildUrl("zpboss/app/job/different/city/audit/page")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/124632
   * 上传异地招聘材料
   */
  public static readonly URL_JOB_DIFFERENT_CITY_UPLOAD: string = buildUrl("zpboss/app/job/different/city/upload")
  public static readonly URL_JOB_DESC_SENIORMANAGEMENT_HINT: string = buildUrl("zpjob/job/seniorManagement/placeholder")
  /**
   * 获取同类型职位已通过职位（单条）
   * http://api.kanzhun-inc.com/project/30/interface/api/115159
   */
  public static readonly URL_JOB_DESC_SIMILAR: string = buildUrl("zpjob/job/get/passed/job4desc")
  /**
   * 提醒用户修改工作时间接口
   */
  public static readonly URL_GET_GEEK_WORK_YEAR: string = buildUrl("zpgeek/app/geek/remind/modifyworkyear")
  public static readonly URL_GET_BLUE_LABEL_CONFIG: string = buildUrl("zpgeek/cvapp/geek/label/config")
  public static readonly URL_SAVE_BLUE_LABEL_CONFIG: string = buildUrl("zpgeek/cvapp/geek/label/save")
  /**
   * Boss更新或添加自己的教育经历
   */
  public static readonly URL_BOSS_ADD_UPDATE_EDU_EXP: string = buildUrl("boss/addOrUpdateEduExp")
  /**
   * Boss删除自己的教育经历
   */
  public static readonly URL_BOSS_DELETE_EDU_EXP: string = buildUrl("boss/deleteEduExp")
  /**
   * Boss更新或添加自己的工作经历经历
   */
  public static readonly URL_BOSS_ADD_UPDATE_WORK_EXP: string = buildUrl("boss/saveOrUpdateWorkExp")
  /**
   * Boss删除自己的工作经历经历
   */
  public static readonly URL_BOSS_DELETE_WORK_EXP: string = buildUrl("boss/deleteWorkExp")
  /**
   * 获取标签
   */
  public static readonly URL_BOSS_GET_LABELS: string = buildUrl("boss/getLabels")
  /**
   * boss自己看自己
   */
  public static readonly URL_BOSS_GET_BOSS_PROFILE: string = buildUrl("boss/getBossProfile")
  /**
   * Boss-获取Boss基本信息 619用于完善流程回填
   */
  public static readonly URL_GET_BOSS_PROFILE: string = buildUrl("moment/boss_profile/getBossProfile")
  /**
   * geek看猎头
   */
  public static readonly URL_GEEK_GET_HUNTER_PROFILE: string = buildUrl("zpgeek/app/geek/getHunterProfileV2")
  public static readonly URL_BOSS_MATERIAL_CHECK: string = buildUrl("zpboss/app/boss/material/check")
  /**
   * 获取分享朋友圈信息
   */
  public static readonly URL_GET_SHARE_TIMELINE: string = buildUrl("share/getShareTimeline")
  /**
   * 分享职位回调
   */
  public static readonly URL_SHARE_CALLBACK: string = buildUrl("job/shareCallback")
  public static readonly URL_BATCH_COMMUNICATE: string = buildUrl("zpitem/batchCommunication/pullMessage")
  /**
   * 搜索同事
   */
  public static readonly URL_GET_MATE_LIST_V2: string = buildUrl("zpboss/app/mate/v2/list")
  // 公共分享小程序
  public static readonly URL_GET_SHARE_APP_MESSAGE: string = buildUrl("zpinterview/boss/interview/app/share")
  public static readonly URL_GET_SHARE_GEEK: string = buildUrl("zpboss/app/share/geek")
  public static readonly URL_ZPGEEK_APP_GEEK_JOB_SHAREMINIAPP: string = buildUrl("zpgeek/app/geek/job/shareminiapp")
  // 公共分享小程序
  public static readonly URL_GET_SHARE_INTERVIEW: string = buildUrl("zpinterview/boss/interview/app/share")
  public static readonly URL_GET_SHARE_JD_GROUP: string = buildUrl("zpgeek/app/geek/job/group/shareminiapp")
  public static readonly URL_GET_SHARE_UNION: string = buildUrl("moment/share/union")
  public static readonly URL_GET_RESUME_LIST: string = buildUrl("zpgeek/cvapp/geek/resume/querylist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/359779
   * 【在线简历】在线简历页按钮配置
   */
  public static readonly URL_ZPGEEK_APP_GEEK_WEBRESUME_BTN_CONFIG_QUERY: string = buildUrl("zpgeek/cvapp/geek/webresume/btn/config/query")
  /**
   * 【附件简历】Nlp附件解析结果轮询
   * http://api.kanzhun-inc.com/project/30/interface/api/152707
   */
  public static readonly URL_ZPGEEK_APP_NLP_RESUME_PARSER_STATUS_CHECK: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/status/check")
  /**
   * 【附件简历】Nlp附件解析结果查询
   * http://api.kanzhun-inc.com/project/30/interface/api/152714
   */
  public static readonly URL_ZPGEEK_APP_NLP_RESUME_PARSER_RESULT_QUERY: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/result/query")
  /**
   * 812.207 蓝领专属简历投递 - 自动生成在线简历
   * http://api.kanzhun-inc.com/project/30/interface/api/117906
   */
  public static readonly URL_GET_RESUME_TRANSFER_ACTION: string = buildUrl("zpgeek/app/geek/resume/cvtransfer/action")
  /**
   * 812.207 蓝领专属简历投递 - 轮询简历是否生成接口
   * http://api.kanzhun-inc.com/project/30/interface/api/117912
   */
  public static readonly URL_GET_RESUME_TRANSFER_CHECK: string = buildUrl("zpgeek/app/geek/resume/cvtransfer/checkstatus")
  public static readonly URL_EXCHANGE_RESUME_GET: string = buildUrl("zpchat/exchange/auth/resume/get")
  public static readonly URL_EXCHANGE_AUTH_ACCEPT: string = buildUrl("zpchat/exchange/auth/accept")
  public static readonly URL_EXCHANGE_AUTH_REJECT: string = buildUrl("zpchat/exchange/auth/button/update")
  public static readonly URL_SHARE_COMPLETE: string = buildUrl("zpjob/wjd/wxShareComplete")
  public static readonly URL_GET_BOSS_MEDIA: string = buildUrl("user/getBossMedia")
  public static readonly URL_RESUME_UPDATE_NAME: string = buildUrl("zpgeek/cvapp/geek/resume/updatecustomname")
  public static readonly URL_GREETING_CUSTOM_UPDATE: string = buildUrl("zpchat/greeting/customUpdate")
  public static readonly URL_GREETING_CUSTOM_DELETE: string = buildUrl("zpchat/greeting/customDelete")
  public static readonly URL_VIEW_MEDIA: string = buildUrl("user/viewMedia")
  public static readonly URL_DEL_MEDIA: string = buildUrl("boss/delMedia")
  public static readonly URL_FAVOR_MEDIA: string = buildUrl("favor/media")
  /**
   * https://api.weizhipin.com/project/30/interface/api/470710
   * <p>
   * 【附件简历】查询附件简历名称
   */
  public static readonly URL_ZPGEEK_APP_GEEK_RESUME_NAME_QUERY: string = buildUrl("zpgeek/cvapp/geek/resume/name/query")
  /**
   * https://api.weizhipin.com/project/30/interface/api/470725
   * 【附件简历】简历名称提示关闭
   */
  public static readonly URL_ZPGEEK_APP_GEEK_RESUME_NAME_TIP_CLOSE: string = buildUrl("zpgeek/cvapp/geek/resume/name/tip/close")
  /**
   * 海外留学生认证弹窗接口
   */
  public static readonly URL_GEEK_STUDY_OVERSEAS_AUTHENTICATION: string = buildUrl("zpgeek/app/geek/studyabroad/certguide/check")
  /**
   * 海外留学生认证弹窗更新接口
   */
  public static readonly URL_GEEK_STUDY_OVERSEAS_AUTHENTICATION_UPDATE: string = buildUrl("zpgeek/app/geek/studyabroad/certguide/update")
  /**
   * 教育经历学历检查接口
   */
  public static readonly URL_GEEK_CHECK_EDU_DEGREE: string = buildUrl("zpgeek/cvapp/geek/edudegree/check")
  /**
   * 教育经历学历检查接口
   */
  public static readonly URL_GEEK_CHECK_EDU_DEGREE_CONFIRM: string = buildUrl("zpgeek/cvapp/geek/edudegree/confirm")
  /**
   * 添加表情接口
   */
  public static readonly URL_ADD_EMOTION: string = buildUrl("zpchat/sticker/add")
  /**
   * 删除表情
   */
  public static readonly URL_EMOTION_DELETE: string = buildUrl("zpchat/sticker/delete")
  /**
   * 职位名称推荐接口
   */
  public static readonly URL_JOB_NAME_SUGGEST: string = buildUrl("zpjob/job/name/suggest")
  /**
   * JD页查询直播信息
   */
  public static readonly URL_GEEK_JOB_QUERYLIVEINFO: string = buildUrl("zpgeek/app/geek/job/queryliveinfo")
  /**
   * JD页查询Banner信息
   */
  public static readonly URL_GEEK_JOB_QUERYBANNNER: string = buildUrl("zpgeek/jobapp/geek/job/querybanner")
  /**
   * JD页查询相似职位信息
   */
  public static readonly URL_GEEK_JOB_SIMILAR_LIST: string = buildUrl("zpgeek/jobapp/search/similar/joblist")
  /**
   * JD页查询相似职位信息
   */
  public static readonly URL_ZPCHAT_GREETING_TIPTEMPLATE: string = buildUrl("zpchat/greeting/getTipTemplate")
  /**
   * 【推荐职位列表】无效职位推荐职位列表
   * http://api.weizhipin.com/project/30/interface/api/215810
   */
  public static readonly URL_ZPGEEK_APP_INVALIDJOB_RECOMMEND_JOBLIST: string = buildUrl("zpgeek/jobapp/invalidjob/recommend/joblist")
  /**
   * 职位编辑获取信息
   */
  public static readonly URL_JOB_JOB_PUSH_JOB_INFO: string = buildUrl("zpjob/job/push/job/info")
  public static readonly URL_JOB_JOB_EDIT: string = buildUrl("zpjob/job/edit")
  public static readonly URL_JOB_PREFERRED_PROJECT_JOB_EDIT: string = buildUrl("zpjob/job/preferred/project/job/edit")
  public static readonly URL_JOB_JOB_PRE_EDIT: string = buildUrl("zpjob/job/pre/edit")
  public static readonly URL_JOB_JOB_RECOMMEND_ACCEPT: string = buildUrl("zpjob/job/recommend/accept")
  public static readonly URL_JOB_JOB_UPDATE_ADMIN_ADJUST_SUGGEST: string = buildUrl("zpjob/job/update/admin/adjust/suggest")
  public static readonly URL_JOB_JOB_BILL_PUB_QUICK_TOP: string = buildUrl("zpjob/job/bill/pub/quick/top")
  public static readonly URL_BOSS_GET_LIMIT_POSITION_LIST: string = buildUrl("zpboss/app/recruitCompany/getPositionByProxyComId")
  public static readonly URL_JOB_CONFIG_CITY_POSITION: string = buildUrl("zpjob/config/city/position")
  /**
   * 牛人NLP
   */
  public static readonly URL_GEEK_SUGGEST_POSITION: string = buildUrl("zpgeek/app/geek/suggest/position")
  public static readonly URL_ZPGEEK_APP_GEEK_SUGGEST_COURSE: string = buildUrl("zpgeek/app/geek/suggest/course")
  /**
   * 职位类型推荐接口
   */
  public static readonly URL_JOB_CLASS_SUGGEST: string = buildUrl("zpjob/job/position/suggest")
  /**
   * 发布职位获取同类型已通过职位
   * http://api.kanzhun-inc.com/project/30/interface/api/94490
   */
  public static readonly URL_JOB_GET_PASSED_JOBS: string = buildUrl("zpjob/job/get/passed/jobs")
  /**
   * 联系人标签备注中，获取全部标签的接口
   */
  public static readonly URL_USER_GET_ALL_LABELS: string = buildUrl("zprelation/userMark/getAllLabels")
  /**
   * 保存添加的备注和标签
   */
  public static readonly URL_SAVE_NOTE_AND_LABELS: string = buildUrl("zprelation/userMark/saveNoteAndLabels")
  /**
   * 牛人端职位详情页增加投简历按钮
   */
  public static readonly URL_GEEK_SEND_RESUME: string = buildUrl("zpgeek/app/geek/resume/onekey/viajob")
  /**
   * 联系人搜索时给出标签的接口
   */
  public static readonly URL_FILTER_LABELS: string = buildUrl("zprelation/friend/getFilterLabels")
  /**
   * 联系人搜索确定返回的FriendIds
   */
  public static readonly URL_FILTER_FRIENDIDS: string = buildUrl("zprelation/friend//bossFilterNew")
  /**
   * 7.11灰度 联系人搜索确定返回的FriendIds
   */
  public static readonly URL_FILTER_FRIENDIDS_NEW: string = buildUrl("zprelation/friend/bossFilterV2")
  /**
   * https://api.weizhipin.com/project/30/interface/api/434179
   * 获取系统抽屉信息
   */
  public static readonly URL_FRIEND_GET_DRAWER_INFO: string = buildUrl("zprelation/friend/getDrawerInfo")
  /**
   * https://api.weizhipin.com/project/30/interface/api/434075
   * 免打扰系统通知抽屉
   */
  public static readonly URL_FRIEND_SET_NO_DISTURB: string = buildUrl("zprelation/friend/setNoDisturb")
  public static readonly URL_BOSS_GET_IMPROPERREPLY: string = buildUrl("zpchat/improper/bossGetSettings")
  public static readonly URL_OPEN_GET_IMPROPERREPLY: string = buildUrl("zpchat/improper/bossSwitch")
  public static readonly URL_SAVE_REPLAY_CONTENGT: string = buildUrl("zpchat/improper/bossSaveReply")
  public static readonly URL_CHANGE_IMPROPER_REPLAY_TYPE: string = buildUrl("zpchat/improper/bossChangeType")
  /**
   * BossF1推荐列表反馈，保存接口
   */
  public static readonly URL_BOSS_RECOMMEND_JOB_FEEDBACK: string = buildUrl("zpboss/app/f1/feedback")
  /**
   * boss使用牛电前获取弹框和ab test分类
   */
  public static readonly URL_BOSS_PRE_USE_GEEK_INFO_RIGHT: string = buildUrl("zpitem/boss/preUseGeekInfoRight")
  /**
   * boss使用成功后提交牛电评论
   */
  public static readonly URL_BOSS_ADD_GEEK_INFO_RIGHT_COMMENT: string = buildUrl("boss/addGeekInfoRightComment")
  /**
   * 牛人 F2 公司职位聚合专题入口 接口
   */
  public static readonly URL_GET_BANNER_LIST: string = buildUrl("brandTopic/geekBannerList")
  /**
   * 添加收藏表情
   */
  public static readonly URL_ADD_FAVOURITE_EMOTION: string = buildUrl("zpchat/sticker/favorite")
  /**
   * 旧的品牌聚合页调用的接口（这里在919版本做个过渡，支持一下跳转旧的聚合页。后面应该会下掉）
   */
  public static readonly URL_OLD_COMPANY_AGGREGATION: string = buildUrl("zpgeek/app/geek/brandaggregation/queryjoblist")
  /**
   * http://api.weizhipin.com/project/30/interface/api/217682
   * 【品牌聚合】集团/标准品牌聚合页
   */
  public static readonly URL_COMPANY_AGGREGATION: string = buildUrl("zpgeek/app/geek/brandaggregationv2/queryjoblist")
  /**
   * http://api.weizhipin.com/project/30/interface/api/217680
   * 【品牌聚合】标准品牌聚合品牌列表
   */
  public static readonly URL_STBRAND_BRAND_LIST: string = buildUrl("zpgeek/app/geek/stbrand/querybrandlist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/222381
   * 【品牌聚合】品牌聚合页反馈
   */
  public static readonly URL_BRAND_AGGREGATION_FEEDBACK_ADD: string = buildUrl("zpgeek/app/brandaggregation/feedback/add")
  /**
   * 详情点击更多，查询所有的公司品牌信息
   */
  public static readonly URL_GEEK_GET_BRAND_LIST: string = buildUrl("brandTopic/geekGetBrandList")
  /**
   * 职位分享查询数据
   */
  public static readonly URL_BRAND_TOPIC_SHARE: string = buildUrl("brandTopic/share")
  /**
   * 删除标签
   */
  public static readonly URL_DEL_FRIEND_LABEL: string = buildUrl("user/delFriendLabel")
  /**
   * 高搜获取道具介绍页接口
   */
  public static readonly URL_BOSS_GET_ADVANCED_SEARCH_GEEK_PAGE: string = buildUrl("zpitem/boss/getAdvanceSearchGeekPage")
  /**
   * 高搜获取道具介绍页接口V2
   * http://api.kanzhun-inc.com/project/30/interface/api/24931
   */
  public static readonly URL_BOSS_GET_ADVANCED_SEARCH_GEEK_PAGE_V2: string = buildUrl("zpitem/searchChatCard/getAdvanceSearchPageV2")
  /**
   * boss购买搜索畅聊卡
   */
  public static readonly URL_BOSS_SEARCH_CHAT_CARD_BUY: string = buildUrl("boss/buysearchchatcard")
  /**
   * boss使用搜索畅聊卡
   */
  public static readonly URL_BOSS_SEARCH_CHAT_CARD_USE: string = buildUrl("zpitem/boss/usesearchchatcard")
  /**
   * 原生页面搜索畅聊卡激活 (916.243【商业】搜畅赠送免费开聊—场景化)
   * <p>
   * http://api.weizhipin.com/project/30/interface/api/210132
   */
  public static readonly URL_ZP_ITEM_SEARCH_CHAT_CARD_DIRECT_ACTIVE: string = buildUrl("zpitem/searchChatCard/directActive")
  public static readonly URL_UPDATE_SHOW_TYPE: string = buildUrl("zpboss/app/boss/showType/update")
  /**
   * 使用微信认证返回的code登录（6.09）
   */
  public static readonly URL_THIRDPART_LOGIN: string = buildUrl("zppassport/wx/login")
  /**
   * 检测手机号是否已经被绑定 6.09
   */
  public static readonly URL_CHECK_PHONE: string = buildUrl("zppassport/wx/checkBind")
  /**
   * 手机绑定   6.09使用
   */
  public static readonly URL_THIRDPART_BIND_PHONE: string = buildUrl("zppassport/wx/bindPhone")
  /**
   * 设置页面绑定微信接口  6.09使用
   * https://api.weizhipin.com/project/30/interface/api/310302
   */
  public static readonly URL_BIND_THIRD: string = buildUrl("zppassport/wx/bind")
  /**
   * 解除微信绑定 6.09使用
   */
  public static readonly URL_UNBIND: string = buildUrl("zppassport/wx/unbind")
  public static readonly URL_BLOCK_UNLOCK_CHAT: string = buildUrl("zpboss/app/chatBlock/unlockChat")
  public static readonly URL_ADDRESS_SUGGEST: string = buildUrl("zpjob/job/address/suggest")
  public static readonly URL_BOSS_COMMENTS_LIST: string = buildUrl("moment/boss/feed/listCommentsByFeedId")
  public static readonly URL_GEEK_COMMENTS_LIST: string = buildUrl("moment/geek/feed/listCommentsByFeedId")
  public static readonly URL_BOSS_TREND_DETAIL: string = buildUrl("moment/boss/feed/detail")
  public static readonly URL_GEEK_TREND_DETAIL: string = buildUrl("moment/geek/feed/detail")
  public static readonly URL_GEEK_HOME_SAVE_OR_UPDATE: string = buildUrl("moment/geek_profile/saveOrUpdateProfile")
  public static readonly URL_GEEK_LIKE_TREND: string = buildUrl("moment/geek/feed/likeById")
  public static readonly URL_BOSS_LIKE_TREND: string = buildUrl("moment/boss/feed/likeById")
  public static readonly URL_GEEK_TOPIC_DETAIL: string = buildUrl("moment/geek/topic/detail")
  public static readonly URL_BOSS_TOPIC_DETAIL: string = buildUrl("moment/boss/topic/detail")
  public static readonly URL_GEEK_TOPIC_MORE: string = buildUrl("moment/geek/feed/listByTopicId")
  public static readonly URL_BOSS_TOPIC_MORE: string = buildUrl("moment/boss/feed/listByTopicId")
  public static readonly URL_BOSS_SCHOOL_CIRCLE_HIDE: string = buildUrl("moment/boss/schoolCircle/hideEntrance")
  public static readonly URL_CHAT_FILTER_LIST: string = buildUrl("zprelation/friend/filterLayout")
  /**
   * 7.11 筛选选项结果
   */
  public static readonly URL_CHAT_FILTER_LIST_NEW: string = buildUrl("zprelation/friend/filterLayoutV2")
  /**
   * 链接解析title
   */
  public static readonly URL_LINK_RESOLVE: string = buildUrl("moment/link/resolve")
  public static readonly URL_MOMENT_BOSS_PROFILE_HOMEPAGEV2: string = buildUrl("moment/boss_profile/homePageV2")
  /**
   * 【BOSS主页】个人主页标签详情
   *
   * @since 806 http://api.kanzhun-inc.com/project/30/interface/api/95526
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_LABELDETAIL: string = buildUrl("moment/boss_profile/labelDetail")
  /**
   * Boss-热门话题
   */
  public static readonly URL_MOMENT_BOSS_TOPIC_HOTLIST: string = buildUrl("moment/boss/topic/hotList")
  /**
   * Geek-热门话题列表
   */
  public static readonly URL_MOMENT_GEEK_TOPIC_HOTLIST: string = buildUrl("moment/geek/topic/hotList")
  /**
   * 多图片上传
   */
  public static readonly URL_MULTIPLE_PHOTO_UPLOAD: string = buildUrl("zpupload/uploadMultiPhoto")
  /**
   * APP批量上传网络文件
   */
  public static readonly URL_ZPUPLOAD_MULTI_UPLOAD_NET_FILE: string = buildUrl("zpupload/multiUploadNetFile")
  /**
   * Boss-获取boss动态列表
   */
  public static readonly URL_MOMENT_BOSS_FEED_LIST: string = buildUrl("moment/boss/feed/list")
  /**
   * Geek-获取Geek动态列表
   */
  public static readonly URL_MOMENT_GEEK_FEED_LIST: string = buildUrl("moment/geek/feed/list")
  /**
   * 【7.20】设置当前公司开始时间
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_SET_CURRENT_WORK_START_DATE: string = buildUrl("moment/boss_profile/setCurrentWorkStartDate")
  /**
   * 【BOSS主页】牛人工作经历（补全）
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_GEEK_WORK_EXP_LIST: string = buildUrl("moment/boss_profile/geekWorkExpList")
  /**
   * 【BOSS主页】牛人教育经历（补全）
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_GEEK_EDU_EXP_LIST: string = buildUrl("moment/boss_profile/geekEduExpList")
  /**
   * 【【BOSS主页】批量添加工作经历
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_BATCHSAVEWORKEXPS: string = buildUrl("moment/boss_profile/batchSaveWorkExps")
  /**
   * 【BOSS主页】批量添加教育经历
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_BATCHSAVEEDUEXPS: string = buildUrl("moment/boss_profile/batchSaveEduExps")
  /**
   * 【BOSS主页】添加标签
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_SAVELABELS: string = buildUrl("moment/boss_profile/saveLabels")
  /**
   * Boss新增or编辑教育信息
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_SAVE_OR_UPDATE_EDU_EXP: string = buildUrl("moment/boss_profile/addOrUpdateEduExp")
  /**
   * Boss删除教育信息
   */
  public static readonly URL_MOMENT_BOSS_PROFILE_DELETE_EDU_EXP: string = buildUrl("moment/boss_profile/deleteEduExp")
  /**
   * Boss-获取个人经历信息
   */
  public static readonly URL_GET_BOSS_EXP: string = buildUrl("moment/boss_profile/getBossExp")
  /**
   * Boss-置顶话题列表
   */
  public static readonly URL_MOMENT_BOSS_TOPIC_TOPTOPICLIST: string = buildUrl("moment/boss/topic/topTopicList")
  /**
   * boss获取公司页信息
   */
  public static readonly URL_ZPBOSS_APP_BRANDINFO_GETBRANDINFO: string = buildUrl("zpboss/app/brandInfo/getBrandInfo")


  /**
   * geek获取公司页信息
   */
  public static readonly URL_ZPGEEK_APP_BRANDINFO_GETBRANDINFO: string = buildUrl("zpgeek/app/brandInfo/getBrandInfo")
  /**
   * 【生日】未成年人保护
   */
  public static readonly URL_ZPGEEK_APP_BRANDINFO_PRECHECK: string = buildUrl("zpgeek/cvapp/birthday/precheck")
  /**
   * geek获取公司工作体验列表
   */
  public static readonly URL_ZPGEEK_APP_BRANDINFO_WORKTASTE_QUERYLIST: string = buildUrl("zpgeek/app/brandInfo/worktaste/querylist")
  /**
   * boss获取公司工作体验列表
   */
  //    public static final String URL_ZPBOSS_APP_BRANDINFO_GET_WORK_TASTE_LIST = buildUrl("zpboss/app/brandInfo/getWorkTasteList",
  /**
   * 工作体验列表
   */
  public static readonly URL_ZPBOSS_APP_BRANDINFO_GET_WORK_TASTE_LIST: string = buildUrl("zpboss/app/brandInfo/taste/feed")
  /**
   * geek邀请完善品牌福利
   */
  public static readonly URL_ZPGEEK_APP_BRANDINFO_INVITECOMPLETEWELFARE: string = buildUrl("zpgeek/app/brandInfo/inviteCompleteWelfare")
  /**
   * 资讯列表
   */
  public static readonly URL_MOMENT_BRANDNEWS_LIST: string = buildUrl("moment/brandNews/list")
  /**
   * geek获取品牌举报原因列表
   */
  public static readonly URL_ZPGEEK_APP_BRANDINFO_GETREPORTREASON: string = buildUrl("zpgeek/app/brandInfo/getReportReason")
  /**
   * 举报资讯
   */
  public static readonly URL_MOMENT_BRANDNEWS_REPORT: string = buildUrl("moment/brandNews/report")
  /**
   * geek举报品牌
   */
  public static readonly URL_ZPGEEK_APP_BRANDINFO_REPORTBRAND: string = buildUrl("zpgeek/app/brandInfo/reportBrand")
  /**
   * 6.13新增接口：推荐牛人期望职位，1.应届毕业生命中规则，在启动APP时跳转期望职位推荐聊表
   */
  public static readonly URL_GEEK_SUGGEST_EXPECT_POSITION: string = buildUrl("zpgeek/cvapp/geek/expectposition/querysuggest")
  public static readonly URL_USER_NAME_EDIT: string = buildUrl("zpboss/app/boss/name/check")
  /**
   * 牛人提示修改已认证海外教育经历接口
   */
  public static readonly URL_GEEK_EDU_UPDATE_WARN: string = buildUrl("zpgeek/cvapp/geek/eduexp/updatewarn")
  /**
   * 6.11新增接口：牛人端急速处理列表
   */
  public static readonly URL_GEEK_GET_QUICK_HANDLE_JOB_LIST: string = buildUrl("zpgeek/app/geek/fastdeal/querylist")
  /**
   * 6.11新增接口：boss急速处理列表
   */
  public static readonly URL_BOSS_GET_QUICK_HANDLE_RESUME_LIST: string = buildUrl("zpboss/app/fastdeal/geek/list")
  /**
   * 牛人面试投诉列表 f1使用
   */
  public static readonly URL_GEEK_COMPLAIN_TIP: string = buildUrl("zpinterview/geek/interview/f1/complainTip")
  /**
   * 牛人面试爽约关闭F1提示 f1使用
   */
  public static readonly URL_GEEK_CLOSE_COMPLAIN_TIP: string = buildUrl("zpinterview/geek/interview/f1/closeComplainTip")
  /**
   * 牛人兼职引导F1提示
   */
  public static readonly URL_GEEK_CLOSE_PARTTIME_TIP: string = buildUrl("zpgeek/app/f1/tip/close")
  public static readonly URL_GEEK_CHAT_SMILIAR_JOBLIST: string = buildUrl("zpgeek/app/chat/similar/joblist/close")
  public static readonly URL_GEEK_CHAT_RCMD_JOBLIST_CLOSE: string = buildUrl("zpgeek/app/f1/chat/recommend/close")
  /**
   * 牛人兼职引导F1提示
   */
  public static readonly URL_GEEK_PARTIME_TYPE_UPDATE: string = buildUrl("zpgeek/cvapp/geek/expectposition/parttime/update")
  /**
   * 牛人保存多意愿城市
   */
  public static readonly URL_GEEK_SAVE_EXPECT_CITY: string = buildUrl("zpgeek/cvapp/custom/interestlocation/save")
  public static readonly URL_GET_RECENT_SHARE: string = buildUrl("zpboss/app/mate/share/recent/list")
  /**
   * 牛人问猎头
   */
  public static readonly URL_GEEK_ASK_HUNTER: string = buildUrl("zpgeek/app/geek/ask/hunter")
  /**
   * 认证手机号码是否一致
   */
  public static readonly URL_PHONE_VERIFY: string = buildUrl("zppassport/phone/verify")
  /**
   * 工作地点保存时，增加服务端的校验 （6.13）
   */
  public static readonly URL_JOB_ADDRESS_CHECK: string = buildUrl("zpboss/app/job/address/check")
  /**
   * boss使用牛炸牛人感兴趣接口
   */
  public static readonly URL_LIKE_JOB_WITH_BOMB: string = buildUrl("zpitem/geek/bombInterest")
  /**
   * 聊天阻断
   */
  public static readonly URL_CHAT_BLOCK: string = buildUrl("zpboss/app/chatBlock/preCheckChat")
  /**
   * 使用聊天卡片
   */
  public static readonly URL_USE_CHAT_CARD: string = buildUrl("zpblock/chatBlock/useChatCard")
  /**
   * 检查技能词合法性
   */
  public static readonly URL_CHECK_SKILL_WORD: string = buildUrl("zpjob/job/skillWord/check")
  /**
   * 【特征标签】查询已填特征
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/100958
   */
  public static readonly URL_ZPGEEK_APP_GEEK_TRAIT_QUERY: string = buildUrl("zpgeek/app/geek/trait/query")
  /**
   * 【特征标签】保存ai预测答案
   * https://api.weizhipin.com/project/30/interface/api/647379
   */
  public static readonly URL_GEEK_TRAIT_AI_ANSWER_SAVE: string = buildUrl("zpgeek/app/geek/trait/aianswer/save")
  /**
   * 检查工作内容技能词合法性
   */
  public static readonly URL_CHECK_WORK_DIRECTION_SKILL_WORD: string = buildUrl("zpgeek/cvapp/sensitiveword/check")
  /**
   * 获取用户牛人详情
   * https://api.weizhipin.com/project/30/interface/api/2888
   */
  public static readonly URL_GET_USER_GEEK_DETAIL: string = buildUrl("zpgeek/cvapp/geek/baseinfo/query")
  /**
   * APP本地职位数据
   */
  public static readonly URL_GET_BOSS_JOB_LIST: string = buildUrl("zpjob/job/local/data")
  /**
   * 获取用户牛人详情
   */
  public static readonly URL_GET_USER_GEEK_DH_DETAIL: string = buildUrl("zpgeek/app/dz/baseinfo/query")
  /**
   * 设置完善简历详情页弹出气泡的开关设置
   */
  public static readonly URL_GET_USER_GEEK_DH_QUICKCOMP_UPDATE: string = buildUrl("zpgeek/app/quickcomp/tip/update")
  /**
   * boss-获取F2是否展示入口
   */
  public static readonly URL_BOSS_GET_MSG_ENTRANCE: string = buildUrl("moment/boss/brandTopic/topic/getMsgEntrance")
  /**
   * geek-获取F2是否展示入口
   */
  public static readonly URL_GEEK_GET_MSG_ENTRANCE: string = buildUrl("moment/geek/brandTopic/topic/getMsgEntrance")
  /**
   * 获取动态条
   * https://api.weizhipin.com/project/30/interface/api/43128
   */
  public static readonly URL_GET_USER_DYNAMIC_BAR: string = buildUrl("zpuser/dynamicBar/get")
  /**
   * 获取用户底部按钮
   */
  public static readonly URL_GET_USER_BOTTOM_BTNS: string = buildUrl("zpuser/user/getBottomBtns")
  /**
   * 获取用户快速回复
   */
  public static readonly URL_USER_QUICK_REPLY: string = buildUrl("zpchat/fastreply/get")
  /**
   * 获取面试数量 公共接口
   */
  public static readonly URL_GEEK_INTERVIEW_FIT_COUNT: string = buildUrl("zpinterview/geek/interview/fitcount")
  /**
   * 获取GIF表情
   */
  public static readonly URL_GET_USER_STICKER: string = buildUrl("zpchat/sticker/get")
  /**
   * AB 功能的接口
   */
  public static readonly URL_GET_USER_FEATURE: string = buildUrl("zpuser/user/getFeature")
  public static readonly URL_GET_USER_FEATURE_V1: string = buildUrl("zpuser/user/getFeatureV1")
  /**
   * 1106 60 获取账号认证状态
   */
  public static readonly URL_GET_USER_ACCOUNT_STATUS: string = buildUrl("zpuser/user/account/auth/status")
  public static readonly URL_GET_USER_ACCOUNT_STATUS_UPDATE: string = buildUrl("zpuser/user/account/auth/status/update")
  public static readonly URL_JOB_GRAY_CONFIG_GET: string = buildUrl("zpjob/gray/config/get")
  public static readonly URL_JOB_BOSS_ADDRESS_GRAY_CONFIG: string = buildUrl("zpjob/boss/address/gray/config")
  /**
   * 牛人f1列表是否白头引导查看职位（6.14）
   */
  public static readonly URL_NEED_VIEW_JOB_GUIDE: string = buildUrl("zpgeek/app/geek/guide/viewjob")
  public static readonly URL_GET_BOSS_ITEM_MALL: string = buildUrl("zpitem/boss/getItemMall")
  public static readonly URL_GET_GEEK_ITEM_MALL: string = buildUrl("zpitem/geek/getItemMall")
  /**
   * 809学生F1列表不合适列表
   */
  public static readonly URL_GET_IMPROPER_LIST: string = buildUrl("zpgeek/app/negativefeedback/reasons")
  /**
   * 809学生F1列表 提交反馈请求
   */
  public static readonly URL_POST_IMPROPER_REASON: string = buildUrl("zpgeek/app/negativefeedback/save")
  /**
   * 用户满意度调查
   */
  public static readonly URL_GET_SATISFACTION_INVESTIGATION: string = buildUrl("zprelation/investigate")
  public static readonly URL_GET_BOSS_JOB_SORT_LIST: string = buildUrl("zpjob/job/sort/list")
  public static readonly URL_GET_USER_F1_PAGE_GUIDE: string = buildUrl("zpjob/guide/f1")
  public static readonly URL_GET_BOSS_POSITION_VERSION: string = buildUrl("zpboss/app/job/position/version")
  public static readonly URL_GET_BLACK_APP_LIST: string = buildUrl("zpCommon/blackApk/list")
  public static readonly URL_UPLOAD_RUNNING_INFO: string = buildUrl("zpCommon/blackApk/statistic")
  public static readonly URL_NPS_SUBMIT: string = buildUrl("zpdac/nps/submit")
  /**
   * 6.17 充值新接口
   */
  public static readonly URL_USER_RECHARGE_BEAN: string = buildUrl("zpp/app/user/rechargeBean")
  /**
   * 消息撤回
   */
  public static readonly URL_POST_MESSAGE_WITHDRAW: string = buildUrl("zpchat/message/withdraw")
  public static readonly URL_ZPITEM_GEEK_PRODUCT_PRE_ORDER: string = buildUrl("zpitem/geek/product/preOrder")
  /**
   * 719 新增获取同品牌下同事列表
   */
  public static readonly URL_GET_MATE_BRAND_LIST: string = buildUrl("zpboss/app/mate/brand/list")
  /**
   * Boss-获取推荐同事信息
   */
  public static readonly URL_GET_RECOMMEND_COLLEAGUES: string = buildUrl("moment/boss_profile/getRecommendColleagues")
  /**
   * Boss-新增or编辑同事信息
   */
  public static readonly URL_SAVE_OR_UPDATE_COLLEAGUES: string = buildUrl("moment/boss_profile/saveOrUpdateColleagues")
  /**
   * Boss-修改同事状态信息
   */
  public static readonly URL_UPDATE_COLLEAGUES_STATUS: string = buildUrl("moment/boss_profile/updateColleaguesStatus")
  /**
   * boss获取品牌工作时间
   */
  public static readonly URL_BOSS_GET_BRAND_WORK_TIME: string = buildUrl("zpCompany/brandComplete/getBrandWorkTime")
  /**
   * boss获取自己的工作体验
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/97332
   */
  public static readonly URL_BOSS_GET_USER_WORK_TASTE: string = buildUrl("zpboss/app/brandInfo/taste/getUserWorkTaste")
  /**
   * boss添加/更新品牌工作时间
   */
  public static readonly URL_UPDATE_BRAND_WORK_TIME: string = buildUrl("zpCompany/brandComplete/updateBrandWorkTime")
  /**
   * 校验敏感词
   */
  public static readonly URL_CHECK_SENSITIVE_WORDS: string = buildUrl("zpboss/app/brandInfo/checkSensitiveWords")
  public static readonly URL_SHARE_INVITECOMPLETE: string = buildUrl("zpboss/app/brandComplete/shareInviteComplete")
  /**
   * 6.17新增接口，获取联系牛人的boss的同一个公司全称同事列表
   */
  public static readonly URL_BOSS_MATE_GET_GEEK_CHAT_OTHERS: string = buildUrl("zpboss/app/mate/geek/chat/other/bosses")
  /**
   * 删除群聊
   */
  public static readonly URL_BOSS_GROUP_HIDE_GROUP: string = buildUrl("zpchat/group/hideGroup")
  public static readonly URL_GROUP_DISMISS_GRAVITY_GROUPS: string = buildUrl("zpchat/group/hideGroups")
  /**
   * 3.2 根据公司全称品牌名称建议
   */
  public static readonly URL_SUGGEST_BRAND_NAME: string = buildUrl("zpboss/app/brand/suggestBrandName")
  /**
   * 3.3 根据品牌名称获取相同名称品牌
   */
  public static readonly URL_GET_SAMPLE_BRAND_BY_NAME: string = buildUrl("zpboss/app/brandInfo/getSampleBrandByName")
  /**
   * 3.1 boss预览其他品牌主页
   */
  public static readonly URL_GET_OTHER_BRAND_INFO: string = buildUrl("zpboss/app/brandInfo/getOtherBrandInfo")
  /**
   * 获取微信公众号二维码
   */
  public static readonly URL_WECHAT_NOTIFY_QRURL: string = buildUrl("wechat/notify/qrUrl")
  /**
   * 关闭微信通知 （618）
   */
  public static readonly URL_WECHAT_NOTIFY_OFF: string = buildUrl("wechat/notify/off")
  /**
   * 获取阻断预订单
   */
  public static readonly URL_BLOCK_PAY_PRE_ORDER: string = buildUrl("zpblock/order/preorder")
  /**
   * 获取新阻断预下单
   */
  public static readonly URL_ZP_BLOCK_V2_PAY_PRE_ORDER: string = buildUrl("zpblock/order/v2/preorder")
  /**
   * 道具预下单
   */
  public static readonly URL_ITEM_PAY_PRE_ORDER: string = buildUrl("zpitem/preOrder")
  /**
   * 线下道具使用接口
   */
  public static readonly URL_ZP_ITEM_ITEM_USE_EXECUTE: string = buildUrl("zpitem/item/use/execute")
  /**
   * 获取订单信息详情
   * https://api.weizhipin.com/project/30/interface/api/19354
   */
  public static readonly URL_GET_PAY_ORDER_INFO: string = buildUrl("zpp/app/user/bzbOrderInfo")
  /**
   * 支付接口
   */
  public static readonly URL_PAY_ORDER: string = buildUrl("zpp/app/user/bzbOrder")
  /**
   * 获取支付成功结果
   */
  public static readonly URL_PAY_SUCCESS_RESULT: string = buildUrl("zpp/app/user/bzbQuery")
  public static readonly URL_JOB_QUICK_PUB_DESC_GENERATE: string = buildUrl("zpjob/job/quick/pub/desc/generate")
  public static readonly URL_JOB_QUICK_PUB_DESC_CHECK: string = buildUrl("zpjob/job/quick/pub/desc/check")
  /**
   * 1117.105【招聘者】极速发布职位请求职位描述修改成异 - app极速发布异步生成职位描述
   */
  public static readonly URL_JOB_QUICK_PUB_DESC_ASYNC_GENERATE: string = buildUrl("zpjob/job/quick/pub/desc/async/generate")
  public static readonly URL_JOB_JOB_SKILL_GET: string = buildUrl("zpjob/job/getskills")
  public static readonly URL_JOB_POPUP_SUGGEST: string = buildUrl("zpjob/job/popup/suggest")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/193576
   */
  public static readonly URL_JOB_REQUIRE_INDUSTRY_PREDICT: string = buildUrl("zpjob/job/require/industries/predict")
  /**
   * https://api.weizhipin.com/project/30/interface/api/391171
   */
  public static readonly URL_BOSSUP_JOB_HOTLINE_CALL: string = buildUrl("zpgeek/app/bossupjob/hotline/call")
  /**
   * https://api.weizhipin.com/project/30/interface/api/391147
   */
  public static readonly URL_BOSSUP_JOB_HOTLINE_STATUS: string = buildUrl("zpgeek/app/bossupjob/hotline/status/query")
  /**
   * https://api.weizhipin.com/project/30/interface/api/521081
   */
  public static readonly URL_HOTLINE_APPLYFINISH: string = buildUrl("zpgeek/app/bossupjob/hotline/applyfinish")
  /**
   * 职位描述提取薪资详情信息
   * http://api.kanzhun-inc.com/project/30/interface/api/146489
   */
  public static readonly URL_JOB_SALARY_INFO_TRANSFER: string = buildUrl("zpjob/job/salary/info/transfer")
  /**
   * APP端 检查猎企添加的客户公司是否超限
   * http://api.kanzhun-inc.com/project/30/interface/api/199672
   */
  public static readonly URL_BOSS_RECRUIT_COMPANY_ADD_PRE_CHECK: string = buildUrl("hunter/app/recruitCompany/addCustomerPreCheck")
  /**
   * 获取公司认证通过的地址
   * http://api.kanzhun-inc.com/project/30/interface/api/146024
   */
  public static readonly URL_JOB_COM_ADDRESS_LIST: string = buildUrl("zpjob/com/address/list")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/146025
   * 设置个人地址
   */
  public static readonly URL_JOB_COM_ADDRESS_BOSS_SET: string = buildUrl("zpjob/com/address/boss/set")
  /**
   * 打入环境认证
   * http://api.kanzhun-inc.com/project/20/interface/api/146144
   */
  public static readonly URL_JOB_COM_ADDRESS_ENV_CERT: string = buildUrl("zpjob/com/address/env/cert")
  /**
   * 【810】发送图谱训练消息
   */
  public static readonly GET_LEARN_SKILL_NOTIFY: string = buildUrl("moment/get/learnSkill/notifyAtlas")
  /**
   * 完善福利获取关键词
   */
  public static readonly URL_JOB_SKILL_PERFECT_GET: string = buildUrl("zpjob/job/skills/perfect/get")
  /**
   * 获取优惠券列表
   */
  public static readonly URL_GET_USER_DISCOUNT_LIST: string = buildUrl("zpp/app/user/bzbDiscountList")
  public static readonly URL_ZP_FAST_REPLAY_SORT: string = buildUrl("zpchat/fastreply/sort")
  /**
   * 6.19 获取牛人搜索推荐的推荐期望职位
   */
  public static readonly URL_GET_SEARCH_RECOMMEND_EXPECT_JOB: string = buildUrl("zpgeek/app/expect/getSimilarPositionList")
  /**
   * 6.19获得公司信息
   */
  public static readonly URL_GET_BRAND_INFO: string = buildUrl("zpboss/app/brandComplete/getCompleteInfo")
  /**
   * 6.19 更新品牌logo
   */
  public static readonly URL_UPDATE_COMPANY_LOGO: string = buildUrl("zpboss/app/brandComplete/updateBrandLogo")
  /**
   * VR 公司环境图片列表
   */
  public static readonly URL_COMPANY_VR_PIC_LIST: string = buildUrl("zpCompany/brandComplete/panoramicPicture/getList")
  /**
   * 添加/更新
   * https://api.weizhipin.com/project/30/interface/api/152959
   */
  public static readonly URL_COMPANY_VR_ADD_OR_UPDATE: string = buildUrl("zpCompany/brandComplete/panoramicPicture/addOrUpdate")
  public static readonly URL_COMPANY_VR_VIEW_NUM: string = buildUrl("zpCompany/brandComplete/panoramicPicture/addViewCount")
  /**
   * 1017
   * 获取品牌下vr列表
   * https://api.weizhipin.com/project/30/interface/api/472470
   */
  public static readonly URL_ZPJOB_VR_MAPPING_VR_LIST: string = buildUrl("zpjob/vr/mapping/vr/list")
  /**
   * 1017
   * 可同步Vr的职位列表
   * https://api.weizhipin.com/project/30/interface/api/472320
   */
  public static readonly URL_ZPJOB_VR_MAPPING_SIMILAR_JOB_LIST: string = buildUrl("zpjob/vr/mapping/similar/job/list")
  /**
   * 1017
   * 职位Vr信息提交
   * https://api.weizhipin.com/project/30/interface/api/472325
   */
  public static readonly URL_ZPJOB_VR_MAPPING_SUBMIT: string = buildUrl("zpjob/vr/mapping/submit")
  /**
   * 1101.81
   * 修改用户保留意向
   * https://api.weizhipin.com/project/30/interface/api/507480
   */
  public static readonly URL_PANORAMICPICTURE_USERKEEPINTENTION_UPDATE: string = buildUrl("zpCompany/brandComplete/panoramicPicture/userKeepIntention/update")
  /**
   * 1101.81
   * 审核中预览页面点击“重新拍摄”后的审核状态
   * https://api.weizhipin.com/project/30/interface/api/507523
   */
  public static readonly URL_PANORAMICPICTURE_RESHOOT_CHECK: string = buildUrl("zpCompany/brandComplete/panoramicPicture/waitAuditPreview/reshoot/check")
  /**
   * 删除VR
   */
  public static readonly URL_COMPANY_VR_DELETE: string = buildUrl("zpCompany/brandComplete/panoramicPicture/delete")
  /**
   * 1102.702
   * 获取地址认证信息
   * https://api.weizhipin.com/project/30/interface/api/508594
   */
  public static readonly URL_GET_ADDRESS_AUTHENTICATION_INFO: string = buildUrl("certification/vrCert/getFileInfo")
  /**
   * 1102.702
   * 引导弹窗信息
   * https://api.weizhipin.com/project/30/interface/api/508618
   */
  public static readonly URL_GET_GUIDE_NOTIFICATION: string = buildUrl("certification/vrCert/getGuideNotification")
  /**
   * 1102.702
   * 算法模型识别图片结果
   * https://api.weizhipin.com/project/30/interface/api/508630
   */
  public static readonly URL_GET_IMG_DISTINGUISH: string = buildUrl("certification/vrCert/getImgDistinguish")
  /**
   * 1102.702
   * 离开拍摄页面文案信息
   * https://api.weizhipin.com/project/30/interface/api/508636
   */
  public static readonly URL_GET_LEAVE_PAGE_INFO: string = buildUrl("certification/vrCert/getLeavePageInfo")
  /**
   * 1102.702
   * 上传信息
   * https://api.weizhipin.com/project/30/interface/api/508588
   */
  public static readonly URL_CERTIFICATION_UPLOAD_VR_INFO: string = buildUrl("certification/vrCert/uploadVrInfo")
  /**
   * 1102.702
   * 下发环境认证
   * https://api.weizhipin.com/project/30/interface/api/510658
   */
  public static readonly URL_CERTIFICATION_ENABLE_ENV_CERT: string = buildUrl("certification/vrCert/enableEnvCert")
  /**
   * 6.19 更新公司融资状态
   */
  public static readonly URL_UPDATE_COMPANY_STAGE: string = buildUrl("zpboss/app/brandComplete/updateBrandStage")
  /**
   * 6.19 更新公司规模
   */
  public static readonly URL_UPDATE_COMPANY_SCALE: string = buildUrl("zpboss/app/brandComplete/updateBrandScale")
  /**
   * 6.19 更新公司官网
   */
  public static readonly URL_UPDATE_COMPANY_WEBSITE: string = buildUrl("zpboss/app/brandComplete/updateBrandWebsite")
  /**
   * 6.19 更新公司简介
   */
  public static readonly URL_UPDATE_COMPANY_BRIEF: string = buildUrl("zpboss/app/brandComplete/introduce/addOrUpdate")
  /**
   * 1003 公司简介 新接口
   */
  public static readonly URL_UPDATE_COMPANY_BRIEF_1003: string = buildUrl("zpboss/app/brandComplete/introduce/addOrUpdate/v2")
  /**
   * 1012.10 【简历助手】关闭简历填写助手
   * https://api.weizhipin.com/project/30/interface/api/409547
   */
  public static readonly URL_GEEK_RESUME_QUICK_INPUT_ENTRY_CLOSE: string = buildUrl("zpgeek/cvapp/resume/assistant/close")
  /**
   * 6.19 更新公司简介
   */
  public static readonly URL_UPDATE_COMPANY_PHOTOS: string = buildUrl("zpCompany/brandComplete/picture/addOrUpdate")
  /**
   * Boss个人主页单独编辑职业照
   */
  public static readonly URL_SAVE_OR_UPDATE_BACKGROUND_IMG: string = buildUrl("moment/boss_profile/saveOrUpdateBackGroundImg")
  /**
   * Boss-获取Boss主页背景图默认图片
   */
  public static readonly URL_GET_DEFAULT_IMAGE: string = buildUrl("moment/boss_profile/getDefaultImage")
  /**
   * Boss-获取Boss样例信息接口
   */
  public static readonly URL_GET_BOSS_SAMPLE_PROFILE: string = buildUrl("moment/boss_profile/getBossSampleProfile")
  /**
   * Boss-领取完善奖励宣章
   */
  public static readonly URL_APPLY_REWARD: string = buildUrl("moment/boss_profile/applyReward")
  /**
   * https://api.weizhipin.com/project/30/interface/api/87889
   * 8.04 添加/更新品牌产品
   */
  public static readonly URL_ADD_OR_UPDATE_PRODUCTION: string = buildUrl("zpCompany/brandComplete/production/addOrUpdate")
  /**
   * 添加/更新品牌高管
   */
  public static readonly URL_ADD_OR_UPDATE_SENIOR: string = buildUrl("zpCompany/brandComplete/senior/addOrUpdateSenior")
  /**
   * 6.19 更新产品名字
   */
  public static readonly URL_UPDATE_PRODUCTION: string = buildUrl("zpboss/app/brandComplete/production/addOrUpdateName")
  /**
   * 6.19 更新产Solgan
   */
  public static readonly URL_UPDATE_SLOGAN: string = buildUrl("zpboss/app/brandComplete/production/updateSlogan")
  /**
   * 6.19产品亮点
   */
  public static readonly URL_UPDATE_BRIGHT: string = buildUrl("zpboss/app/brandComplete/production/updateBright")
  /**
   * 更新产品网址
   */
  public static readonly URL_UPDATE_APP_WEBSITE: string = buildUrl("zpboss/app/brandComplete/production/updateWebsite")
  /**
   * 更新产品LOGO
   */

  public static readonly URL_UPDATE_APP_LOGO: string = buildUrl("zpboss/app/brandComplete/production/updateLogo")
  /**
   * 产品排序
   */
  public static readonly URL_PRODUCTION_ORDER: string = buildUrl("zpCompany/brandComplete/production/reorder")
  /**
   * 删除产品
   */
  public static readonly URL_PRODUCTION_DELETE: string = buildUrl("zpCompany/brandComplete/production/delete")
  /**
   * 获取品牌产品列表
   */
  public static readonly URL_BRAND_COMPLETE_PRODUCTION_LIST: string = buildUrl("zpCompany/brandComplete/production/list")
  /**
   * 品牌高管列表
   */
  public static readonly URL_BRAND_COMPLETE_SENIOR_LIST: string = buildUrl("zpCompany/brandComplete/senior/list")
  /**
   * 【suggest】城市
   * http://api.kanzhun-inc.com/project/30/interface/api/108581
   */
  public static readonly URL_ZPGEEK_APP_GEEK_SUGGEST_CITY: string = buildUrl("zpgeek/app/geek/suggest/city")
  /**
   * 6.19 更新高管头像
   */
  public static readonly URL_UPDATE_SENIOR_LOGO: string = buildUrl("zpboss/app/brandComplete/senior/updateAvatar")
  /**
   * 获取推荐logo
   */
  public static readonly URL_GET_RECOMMEND_LOGO: string = buildUrl("zpboss/app/brandComplete/suggestBrandLogo")
  /**
   * 6.19 更新高管基本信息
   */
  public static readonly URL_UPDATE_SENIOR_BASIC: string = buildUrl("zpboss/app/brandComplete/senior/addOrUpdate")
  /**
   * 6.19 更新高管简介
   */
  public static readonly URL_UPDATE_SENIOR_INTRO: string = buildUrl("zpboss/app/brandComplete/senior/updateIntroduce")
  /**
   * 6.19 更新高管排序
   */
  public static readonly URL_UPDATE_SENIOR_ORDER: string = buildUrl("zpCompany/brandComplete/senior/reorder")
  /**
   * 6.19 删除高管
   */
  public static readonly URL_DELETE_SENIOR: string = buildUrl("zpCompany/brandComplete/senior/delete")
  public static readonly URL_JOB_POSITION_DIVIDE: string = buildUrl("zpboss/app/job/position/divide")
  /**
   * 8.04 职类预测
   * http://api.kanzhun-inc.com/project/30/interface/api/88260
   */
  public static readonly URL_JOB_POSITION_PREDICT: string = buildUrl("zpjob/job/position/predict")
  /**
   * 9.08 职类预测
   * http://api.kanzhun-inc.com/project/30/interface/api/188704
   */
  public static readonly URL_JOB_POSITION_PREDICT_NEW: string = buildUrl("zpjob/job/position/predict/new")
  public static readonly URL_JOB_PUBLISH_COMPLETE: string = buildUrl("zpjob/job/publish/complete")
  /**
   * 职位类型变化
   * http://api.kanzhun-inc.com/project/30/interface/api/108070
   */
  public static readonly URL_JOB_POSITION_CHANGE: string = buildUrl("zpjob/job/position/change")
  /**
   * 7.16-68 首善发布职位 描述联想
   */
  public static readonly URL_JOB_POSITION_DESC_ASSOCIATE: string = buildUrl("zpjob/job/desc/associate")
  /**
   * 6.21 NLP简历解析-查询更新建议
   */
  public static readonly URL_NLP_RESUME_PARSER_QUERY: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/queryrecord")
  /**
   * NLP简历解析-更新状态并获取下一条数据
   * https://api.weizhipin.com/project/30/interface/api/22414
   */
  public static readonly URL_NLP_RESUME_PARSER_QUERY_DETAIL: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/querydetail")
  /**
   * 8.21【NLP简历解析】更新状态
   */
  public static readonly URL_NLP_RESUME_PARSER_STATUS_UPDATE: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/status/update")
  /**
   * 8.21【NLP简历解析】简历解析一键同步
   */
  public static readonly URL_NLP_RESUME_PARSER_SYNC: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/sync")
  /**
   * NLP简历解析-暂不需要更新建议
   */
  public static readonly URL_NLP_RESUME_PARSER_UPDATE_STATUS: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/updatelayerstatus")
  /**
   * 获取短信通知信息
   */
  public static readonly URL_GET_ITEM_DETAIL: string = buildUrl("zpitem/item/detailUnite")
  /**
   * 短信通知预操作
   */
  public static readonly URL_SMS_PRE_USE: string = buildUrl("zpitem/smsNotice/preUse")
  /**
   * 提交聊天媒体状态 聊天模块依赖
   */
  public static readonly URL_POST_MEDIA_STATUS: string = buildUrl("zpinterview/interview/media/status")
  /**
   * 获得聊天媒体状态 聊天模块依赖
   */
  public static readonly URL_MEDIA_GET: string = buildUrl("zpinterview/interview/media/get")
  /**
   * 视频预约状态同步 聊天模块依赖
   */
  public static readonly URL_GET_INTERVIEW_STATUS: string = buildUrl("zprelation/interview/video/status")
  /**
   * 【家庭住址】保存家庭住址
   */
  public static readonly URL_HOME_ADDRESS_SAVE: string = buildUrl("zpgeek/cvapp/geek/homeaddress/save")
  /**
   * 保存期望地址
   */
  public static readonly URL_EXPECT_ADDRESS_SAVE: string = buildUrl("zpgeek/cvapp/geek/expectaddress/save")
  /**
   * 7.01短信提交
   */
  public static readonly URL_COMMIT_MESSAGE: string = buildUrl("zpitem/smsNotice/use")
  /**
   * 7.01 Boss进入聊天
   */
  public static readonly URL_BOSS_ENTER_CHAT: string = buildUrl("zpchat/session/bossEnter")
  /**
   * https://api.weizhipin.com/project/30/interface/api/505441
   */
  public static readonly URL_RECOMMEND_TIP_CLOSE: string = buildUrl("zpchat/shortmsg/recommend/tipClose")
  /**
   * 志愿者经历-保存或修改
   */
  public static readonly URL_VOLUNTEER_SAVE: string = buildUrl("zpgeek/cvapp/resume/volunteer/save")
  /**
   * 志愿者经历-删除
   */
  public static readonly URL_VOLUNTEER_DELETE: string = buildUrl("zpgeek/cvapp/resume/volunteer/delete")
  /**
   * 【职类】查询职类特性
   * http://api.weizhipin.com/project/30/interface/api/22636
   */
  public static readonly URL_ZPGEEK_APP_POSITIONFEATURE_CHECK: string = buildUrl("zpgeek/cvapp/positionfeature/check")
  /**
   * https://api.weizhipin.com/project/30/interface/api/502693
   * 【牛人完善】流程简化判断
   */
  public static readonly URL_ZPGEEK_APP_COMPLETE_STEPFORWARD: string = buildUrl("zpgeek/cvapp/complete/stepforward")
  /**
   * 【曾经做过】保存曾经做过
   * http://api.weizhipin.com/project/30/interface/api/205671
   */
  public static readonly URL_ZPGEEK_APP_DONEWORK_SAVE = buildUrl("zpgeek/cvapp/donework/save");
  /**
   * 813.215 删除岗位经验
   */
  public static readonly URL_GEEK_BLUE_POSTEXP: string = buildUrl("zpgeek/cvapp/geek/postexp/save")
  /**
   * 813.215 增加/保存岗位经验
   */
  public static readonly URL_GEEK_BLUE_DELETEEXP: string = buildUrl("zpgeek/cvapp/geek/postexp/delete")
  /**
   * http://api.weizhipin.com/project/30/interface/api/95820
   */
  public static readonly URL_GEEK_F1_BANNER_QUERY: string = buildUrl("zpgeek/app/f1/banner/query")
  /**
   * 蓝领引导地址添加
   */
  public static readonly URL_BLUE_COLLAR_HOME_ADDRESS_GUIDE_CHECK: string = buildUrl("zpgeek/app/homeaddresstip/check")
  /**
   * 判断手机号是否是新用户
   */
  public static readonly URL_USER_JUDGE: string = buildUrl("zppassport/user/judge")
  /**
   * (919）更换设备扫码-确认登录
   */
  public static readonly URL_ZPPASSPORT_QRCODE_CHANGEDEVICELOGIN: string = buildUrl("zppassport/qrcode/changeDeviceLogin")
  /**
   * 判断手机号是否是新用户 获取开关
   */
  public static readonly URL_USER_SWITCH: string = buildUrl("zppassport/user/switch")
  public static readonly URL_USER_CREATE: string = buildUrl("zppassport/user/create")
  public static readonly URL_USER_BINDPHONE: string = buildUrl("zppassport/user/bindPhone")
  public static readonly URL_USER_BIND_INFO: string = buildUrl("zppassport/user/bindInfo")
  /**
   * 授权给店长直聘登录用到的 ticket
   */
  public static readonly URL_DIRECT_HIRES_AUTH_TICKET: string = buildUrl("zppassport/dz/getToken")
  /**
   * 店长直聘授权登录
   */
  public static readonly URL_DIRECT_HIRES_TICKET_LOGIN: string = buildUrl("zppassport/dz/authLogin")
  /**
   * 店长直聘授权登录，账户不安全，需要发送验证码登录
   */
  public static readonly URL_DIRECT_HIRES_LOGIN_SEND_SMS: string = buildUrl("zppassport/dz/sendSms")
  /**
   * 店长直聘授权验证码登录
   */
  public static readonly URL_DIRECT_HIRES_CODE_LOGIN: string = buildUrl("zppassport/dz/codeLogin")
  /**
   * 获取牛人直通卡代金券
   */
  public static readonly URL_GET_JOB_TOP_COUPON_GIFT: string = buildUrl("zpitem/boss/getJobTopActivityGift")
  /**
   * boss通过messageId获取第三方会议信息 ZPManager中使用
   */
  public static readonly URL_INTERVIEW_INTENTION_MEETING: string = buildUrl("zpinterview/boss/interview/getThirdMeetingInfo")
  /**
   * 领取牛人直通卡优惠券
   */
  public static readonly URL_RECEIVE_JOB_TOP_COUPON_GIFT: string = buildUrl("boss/receiveJobTopCoupon")
  public static readonly URL_SAVE_BLUE_COLLAR_POSITION: string = buildUrl("zpgeek/cvapp/bluecollarposition/custom/save")
  public static readonly URL_GET_BLUE_COLLAR_POSITION_LIST: string = buildUrl("zpgeek/app/bluecollarposition/list")
  public static readonly URL_WECHAT_NOTIFY_TOGGLE: string = buildUrl("wechat/notify/toggle")
  public static readonly URL_BLOCK_VIP_STATE: string = buildUrl("zpblock/vip/state")
  public static readonly URL_VIP_SWITCH: string = buildUrl("zpblock/vip/switch/onOff")
  public static readonly URL_VIP_SWITCH_ONE_BODY: string = buildUrl("zpblock/vip/switch/resumeAssistant/turnOff")
  public static readonly URL_VIP_SETTING_LIST: string = buildUrl("zpblock/vip/switch/setting/list")
  public static readonly URL_BOSS_ASSISTANT_GUIDE_CLOSE: string = buildUrl("zpblock/vip/switch/guide/close")
  public static readonly URL_BOSS_COMPANY_ASSISTANT: string = buildUrl("zpblock/resume/assistant/company/assistant")
  public static readonly URL_BOSS_VIP_IS_DISPLAY: string = buildUrl("zpblock/vip/switch/isDisplayEntrance")
  /**
   * poi地址转换
   */
  public static readonly URL_POI_TRANSLATION: string = buildUrl("zpjob/job/poi/transfer")
  public static readonly URL_ADDRESS_COM_POI_SEARCH: string = buildUrl("zpjob/boss/address/comPoi/search")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/23615
   */
  public static readonly URL_AUDIT_INFO: string = buildUrl("zpjob/job/audit/info")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/105228
   */
  public static readonly URL_AUDIT_URGE: string = buildUrl("zpjob/job/audit/urge")
  /**
   * 获取上次发布职位地址
   */
  public static readonly URL_BOSS_JOB_POST_ADDRESS_AUTO_FILL: string = buildUrl("zpjob/job/last/address")
  /**
   * boss f1关键词列表
   */
  public static readonly URL_BOSS_GET_F1_FILTER_KEYWORD: string = buildUrl("zpboss/app/geek/keywords")
  /**
   * 牛人f1关键词列表
   */
  public static readonly URL_GEEK_GET_F1_FILTER_KEYWORD: string = buildUrl("zpgeek/app/geek/recommend/keywords")
  /**
   * f1关键词收录
   */
  public static readonly URL_BOSS_F1_FILTER_KEYWORD_ADD: string = buildUrl("zpboss/app/geek/addKeyword")
  /**
   * 牛人端f1关键词收录
   */
  public static readonly URL_GEEK_F1_FILTER_KEYWORD_ADD: string = buildUrl("zpgeek/app/geek/recommend/addkeyword")
  /**
   * 7.03【招聘者】限制线下付费企业的公司主页修改权限
   */
  public static readonly URL_RAND_COMPLETE_ALLOW: string = buildUrl("zpboss/app/brandComplete/allow")
  /**
   * 获取道具banner条
   */
  public static readonly URL_JOB_MANAGE_GET_ITEM_BANNER: string = buildUrl("zpitem/jobManage/getItemBanner")
  /**
   * 下拉刷新 同步未读消息
   */
  public static readonly URL_MESSAGE_SYNC_READ: string = buildUrl("zpchat/message/syncRead")
  public static readonly URL_MESSAGE_SYNC_READV2: string = buildUrl("zpchat/message/syncReadV2")
  /**
   * 获取好友公司名称列表
   */
  public static readonly URL_LIST_FRIEND_COMPANY: string = buildUrl("zpboss/app/friend/listFriendCompany")
  /**
   * 编辑头像接口
   */
  public static readonly URL_AVATAR_EDIT: string = buildUrl("zpuser/avatar/edit")
  /**
   * 检查牛人头像审核状态
   */
  public static readonly URL_GEEK_CHECK_AVATAR: string = buildUrl("zpuser/geek/checkAvatar")
  /**
   * 检查Boss头像审核状态
   */
  public static readonly URL_BOSS_CHECK_AVATAR: string = buildUrl("zpuser/boss/checkAvatar")
  /**
   * 7.05 打开关闭智能机器人开关
   */
  public static readonly URL_MOMENT_BRANDNEWS_SWITCH: string = buildUrl("moment/brandNews/switch")
  /**
   * 7.05 预览新闻输入框
   */
  public static readonly URL_BRANDNEWS_PREVIEW: string = buildUrl("moment/brandNews/preview")
  /**
   * 7.05 发布公司资讯
   */
  public static readonly URL_BRANDNEWS_RELEASE: string = buildUrl("moment/brandNews/releaseBrandNews")
  /**
   * 7.05 删除公司资讯
   */
  public static readonly URL_DELETE_BRANDNEWS: string = buildUrl("moment/brandNews/deleteBrandNews")
  /**
   * 【牛人面板】App-geek 查询面板信息
   */
  public static readonly URL_GEEK_PANEL_QUERY: string = buildUrl("zpgeek/app/panel/query")
  /**
   * 【简历诊断】F3诊断卡片列表
   */
  public static readonly URL_ZPGEEK_RESUME_SUGGEST_F3_CARD_LIST: string = buildUrl("zpgeek/cvapp/geek/resume/suggest/f3/card/list")
  /**
   * 【牛人面板】App-geek 关闭面板信息
   */
  public static readonly URL_GEEK_PANEL_CLOSE: string = buildUrl("zpgeek/app/panel/cancel")
  /**
   * 【913.20】【屏蔽公司】B切换身份查询是否已屏蔽当前公司
   */
  public static readonly URL_GEEK_CHECK_SHIELD_COMPANY: string = buildUrl("zpgeek/app/geek/maskcompany/boss/check")
  /**
   * 当前boss是否弹窗赠送
   */
  public static readonly URL_GET_BOSS_CEO_ITEM: string = buildUrl("zpitem/boss/compensateItem2CEO")
  /**
   * 当前boss是否弹窗赠送
   */
  public static readonly URL_CEO_ITEM_RECEIVE: string = buildUrl("zpitem/boss/clickCompensateItem2CEO")
  public static readonly URL_HUNTER_SETTING: string = buildUrl("zpgeek/app/hunter/setting")
  public static readonly URL_CHECK_EDIT: string = buildUrl("zpboss/app/boss/edit/check")
  public static readonly URL_ACCOUNT_REMARK_IS_INNER: string = buildUrl("zpboss/app/accountRemark/isInnerAccount")
  public static readonly URL_ACCOUNT_REMARK_GET_REMARK_LIST: string = buildUrl("zpboss/app/accountRemark/getRemarkList")
  public static readonly URL_MESSAGE_UPDATE_REFRESH: string = buildUrl("zpchat/message/refresh")
  //获取人机验证code
  public static readonly URL_MACHINE_VERIFY_GEET: string = buildUrl("zpAntispam/verify/geet")
  //获取人机验证确认
  public static readonly URL_MACHINE_VERIFY_CONFIRM: string = buildUrl("zpAntispam/verify/confirm")
  // 【转发牛人】转发牛人V2
  public static readonly URL_APP_SHARE_GEEKV2: string = buildUrl("zpboss/app/share/geekV2")
  // 牛人F4 道具列表
  public static readonly URL_GEEK_GET_ITEM_MALL_F4: string = buildUrl("zpitemGeek/geek/getItemMallF4")
  // 进入账号与绑定页，获取是否绑定微信、是否设置过密码等信息
  public static readonly URL_GET_ACCOUNT_STAUTS: string = buildUrl("zppassport/user/accountStatus")
  // 非登录态配置接口
  public static readonly URL_COMMON_CONFIG: string = buildUrl("zpCommon/common/config")
  // 登录态配置接口（需要登录）
  public static readonly URL_COMMON_USER_CONFIG: string = buildUrl("zpCommon/userConfig")
  /**
   * 7.06 f2顶部黄条接口
   */
  public static readonly URL_GET_F2_TOPBAR: string = buildUrl("zprelation/friend/getF2TopBar")
  public static readonly URL_VIP_CLICK_F2_CARD: string = buildUrl("zpitem/geek/vip/clickF2Card")
  /**
   * 7.06 设置为符合要求的牛人符合要求
   */
  public static readonly URL_SET_UNFIT_GEEK_FIT: string = buildUrl("zprelation/friend/bossRemoveFilter")
  /**
   * 7.08设置为符合要求的Boss符合要求
   */
  public static readonly URL_SET_UNFIT_BOSS_FIT: string = buildUrl("zprelation/friend/geekRemoveFilter")
  /**
   * 满意度详情
   * https://api.weizhipin.com/project/207/interface/api/428011
   */
  public static readonly URL_KEFU_EVALUATION_DETAIL: string = buildUrl("kefu/app/user/evaluation/detail")
  /**
   * 满意度评价
   * https://api.weizhipin.com/project/207/interface/api/428019
   */
  public static readonly URL_KEFU_SUBMIT_MESSAGE_EVALUATE_V2: string = buildUrl("kefu/app/user/submitMessageEvaluateV2")
  /**
   * 1103.16问答接口
   * https://api.weizhipin.com/project/30/interface/api/508276
   */
  public static readonly URL_ZPGEEK_RESUME_CHAT_FAQ: string = buildUrl("zpgeek/cvapp/resume/bot/chat/faq")
  /**
   * 1105.8【C】提升作品类牛人图片覆盖率
   * https://api.weizhipin.com/project/30/interface/api/521077
   */
  public static readonly URL_CVAPP_RESUME_DESIGN_CRAWL_FINISH: string = buildUrl("zpgeek/cvapp/resume/design/crawl/finish")
  /**
   * 1103.16问答联想接口
   * https://api.weizhipin.com/project/30/interface/api/508294
   */
  public static readonly URL_ZPGEEK_RESUME_CHAT_THINK: string = buildUrl("zpgeek/cvapp/resume/bot/chat/think")
  /**
   * 1103.16问答历史查询
   * https://api.weizhipin.com/project/30/interface/api/508426
   */
  public static readonly URL_ZPGEEK_RESUME_CHAT_HISTORY: string = buildUrl("zpgeek/cvapp/resume/bot/chat/history")
  /**
   * 1015.059
   * 查看更多常用问
   * https://api.weizhipin.com/project/207/interface/api/445918
   */
  public static readonly URL_KEFU_COMMON_QUESTION_FIND_MORE: string = buildUrl("kefu/app/user/commonQuestion/findMore")
  /**
   * 7.07 分享图片数据接口
   */
  public static readonly URL_WJD_SHARE_PICTURE: string = buildUrl("zpjob/wjd/share/picture")
  public static readonly URL_STUDY_ABROAD_TAG_LIST: string = buildUrl("zpgeek/app/studyabroad/zone")
  public static readonly URL_STUDY_ABROAD_LIST: string = buildUrl("zpgeek/app/studyabroad/articlelist")
  /**
   * 706 添加或者更新招呼语问题
   */
  public static readonly URL_BOSS_GREETING_QUESTION_UPDATE: string = buildUrl("zpchat/greeting/question/save")
  /**
   * 706 获取打招呼语的问题列表
   */
  public static readonly URL_BOSS_GREETING_QUESTION_LIST: string = buildUrl("zpchat/greeting/question/list")
  public static readonly URL_TASTE_PULL: string = buildUrl("zpboss/app/brandInfo/taste/pull")
  public static readonly URL_COM_WORK_EXP_GUIDE: string = buildUrl("zpboss/app/brandInfo/taste/hasEditTip")
  public static readonly URL_COM_WORK_EXP_ACT_GUIDE: string = buildUrl("zpboss/app/brandInfo/taste/tasteAddGuide")
  //region 公司圈
  public static readonly URL_BOSS_GET_TOPIC_LIST: string = buildUrl("moment/boss/brandTopic/topic/getList")
  //geek-获取点赞和回复列表 http://api.kanzhun-inc.com/project/30/interface/api/25479
  public static readonly URL_BOSS_GET_REPLAY_LIST: string = buildUrl("moment/boss/brandTopic/reply/getReplyList")
  //boss-获取点赞和回复列表
  public static readonly URL_GEEK_GET_REPLAY_LIST: string = buildUrl("moment/geek/brandTopic/reply/getReplyList")
  public static readonly URL_GEEK_GET_TOPIC_LIST: string = buildUrl("moment/geek/brandTopic/topic/getList")
  public static readonly URL_GEEK_TOPIC_GET_DETAIL: string = buildUrl("moment/geek/brandTopic/topic/getDetail")
  public static readonly URL_BOSS_TOPIC_GET_DETAIL: string = buildUrl("moment/boss/brandTopic/topic/getDetail")
  public static readonly URL_GEEK_TOPIC_GET_BRAND_LIST: string = buildUrl("moment/geek/brandTopic/topic/getBrandList")
  public static readonly URL_GEEK_GET_PROFILE: string = buildUrl("moment/geek/brandTopic/geek/getProfile")
  public static readonly URL_GEEK_ADD_PROFILE: string = buildUrl("moment/geek/brandTopic/geek/addProfile")
  public static readonly URL_GEEK_ADD_TOPIC: string = buildUrl("moment/geek/brandTopic/topic/addTopic")
  public static readonly URL_BOSS_ADD_TOPIC: string = buildUrl("moment/boss/brandTopic/topic/addTopic")
  public static readonly URL_GEEK_TOPIC_LIKE: string = buildUrl("moment/geek/brandTopic/topic/like")
  public static readonly URL_BOSS_TOPIC_LIKE: string = buildUrl("moment/boss/brandTopic/topic/like")
  public static readonly URL_GEEK_COMMENT_ADD: string = buildUrl("moment/geek/brandTopic/comment/addComment")
  public static readonly URL_BOSS_COMMENT_ADD: string = buildUrl("moment/boss/brandTopic/comment/addComment")
  public static readonly URL_GEEK_DEL_TOPIC: string = buildUrl("moment/geek/brandTopic/topic/delTopic")
  public static readonly URL_BOSS_DEL_TOPIC: string = buildUrl("moment/boss/brandTopic/topic/delTopic")
  //region 7.13新增接口
  //geek-获取点赞和回复历史列表 http://api.kanzhun-inc.com/project/30/interface/api/34297
  public static readonly URL_BOSS_GET_REPLY_HISTORY_LIST: string = buildUrl("moment/boss/brandTopic/reply/getReplyHistoryList")
  //boss-获取点赞和回复历史列表 http://api.kanzhun-inc.com/project/30/interface/api/34285
  public static readonly URL_GEEK_GET_REPLY_HISTORY_LIST: string = buildUrl("moment/geek/brandTopic/reply/getReplyHistoryList")
  public static readonly URL_GEEK_CLEAR_RECORD: string = buildUrl("moment/geek/brandTopic/reply/clearRecord")
  public static readonly URL_BOSS_CLEAR_RECORD: string = buildUrl("moment/boss/brandTopic/reply/clearRecord")
  public static readonly URL_GEEK_GET_SHARE_DETAIL: string = buildUrl("moment/geek/brandTopic/topic/getShareDetail")
  public static readonly URL_GEEK_SHARE_CONFIG: string = buildUrl("moment/geek/brandTopic/share/config")
  public static readonly URL_BOSS_SHARE_CONFIG: string = buildUrl("moment/boss/brandTopic/share/config")
  //endregion

  //endregion


  /**
   * 706 获取未设置问答的职位列表
   */
  public static readonly URL_BOSS_GREETING_QUESTION_UNANSWER_JOB_LIST: string = buildUrl("zpchat/greeting/question/getJobList")
  /**
   * 706 删除招呼语回复
   */
  public static readonly URL_BOSS_GREETING_QUESTION_REPLY_DELETE: string = buildUrl("zpchat/greeting/question/remove")
  /**
   * 707 公司工作体验列表
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_QUERY: string = buildUrl("zpboss/app/brandInfo/taste/query")
  /**
   * 707 公司工作体验删除
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_DEL: string = buildUrl("zpboss/app/brandInfo/taste/del")
  /**
   * 707 邀请同事编写工作体验
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_SHARE_INVITE: string = buildUrl("zpboss/app/brandInfo/taste/shareInvite")
  /**
   * 716 公司工作体验引导校验（校验成功的可以弹窗）
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_GUIDE: string = buildUrl("zpboss/app/brandInfo/taste/guide")
  /**
   * 719 公司工作体验增加阅读数(点击图片时请求)
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_ADD_VIEW_COUNT: string = buildUrl("zpboss/app/brandInfo/taste/addViewCount")
  /**
   * 719 公司工作体验修改用户信息
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_UPDATE_USER_INFO: string = buildUrl("zpboss/app/brandInfo/taste/updateUserInfo")
  /**
   * 719 公司工作体验点击标题
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_CLICK_SUB_TITLE: string = buildUrl("zpboss/app/brandInfo/taste/clickSubTitle")
  /**
   * 公司列表头部工作体验话题聚合
   */
  public static readonly URL_COMPANY_EXP_TOPIC_LIST: string = buildUrl("zpboss/app/brandInfo/taste/hotTasteTopic")
  /**
   * 工作体验灰度
   */
  public static readonly URL_COMPANY_EXP_GRAY: string = buildUrl("zpboss/app/brandInfo/taste/checkGray")
  /**
   * 707 离开公司预检验
   */
  public static readonly URL_BOSS_PRE_CHECK_LEAVE_COMPANY: string = buildUrl("zpboss/app/brandCom/preLeaveCheck")
  /**
   * 707 离开公司
   */
  public static readonly URL_BOSS_LEAVE_COMPANY: string = buildUrl("zpboss/app/brandCom/leave")
  /**
   * 707 邀请同事填写工作体验
   */
  public static readonly URL_APP_INTEREST_ADD: string = buildUrl("zpboss/app/brandInfo/taste/interest/add")
  /**
   * 707 工作体验提交
   */
  public static readonly URL_APP_BRAND_INFO_TASTE_SAVE: string = buildUrl("zpboss/app/brandInfo/taste/saveOrUpdate")
  /**
   * 707 Geek经历异常提醒
   */
  public static readonly URL_GEEK_EXP_REMIND_UPDATE: string = buildUrl("zpgeek/cvapp/geek/remind/warnexp")
  public static readonly URL_F2_VIP_FRIEND_FILTER: string = buildUrl("zpblock/vip/fiendfilter/get/recover")
  public static readonly URL_F2_EXPOSURE_BAR: string = buildUrl("zpblock/passive/exposure/f2/bar")
  public static readonly URL_F2_VIP_FRIEND_RECOVER: string = buildUrl("zpblock/vip/fiendfilter/recover")
  public static readonly URL_INTERACTION_HIGH_GEEK: string = buildUrl("zprelation/interaction/getHighGeek")
  public static readonly URL_ASSISTANT_CALL: string = buildUrl("zpchat/contact/assistant/call")
  public static readonly URL_ASSISTANT_TEST: string = buildUrl("zpchat/contact/assistant/test")
  public static readonly URL_ASSISTANT_EXCHANGE: string = buildUrl("zpchat/contact/assistant/exchange")
  public static readonly URL_INTERACTION_HIGH_GEEK_STATUS: string = buildUrl("zprelation/interaction/setHighGeekStatus")
  public static readonly URL_INTERACTION_CLOSE_POUP: string = buildUrl("zprelation/interaction/closePopup")
  public static readonly URL_CLOSE_YELLOW_STATUS: string = buildUrl("zprelation/interaction/closeYellowStatus")
  /**
   * 708 牛人发起面试拉取job信息
   */
  public static readonly URL_FRIEND_GET_SIMPLE_INFO: string = buildUrl("zprelation/friend/getSimpleInfo")
  /**
   * 708 推荐期望暂不添加
   */
  public static readonly URL_SUGGEST_EXPECT_TEMPORARY_NOT_ADD: string = buildUrl("zpgeek/cvapp/geek/expectposition/suggest/setnomore")
  /**
   * 708 [牛直体验]点击顶部条的操作
   */
  public static readonly URL_ITEM_FREE_EXPERIENCE_CLICK: string = buildUrl("zpitem/experiment/clickF2JobTopBanner")
  /**
   * 709 f1/f2职位延长卡片
   */
  public static readonly URL_GET_BLOCK_JOB_DELAY_CARD: string = buildUrl("zpblock/prolong/card")
  /**
   * 709 f1/f2职位延长卡片关闭
   */
  //    public static final String URL_BLOCK_JOB_DELAY_CARD_CLOSE = buildUrl("zpblock/prolong/hunter_icon_improper_revoke_close",
  public static readonly URL_BLOCK_JOB_DELAY_CARD_CLOSE: string = buildUrl("zpblock/prolong/close")
  /**
   * 709 牛人收藏职位喂奶判断
   */
  public static readonly URL_GET_USING_ITEM_TYPE: string = buildUrl("zpitem/geek/getUsingItemType")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/28087
   */
  public static readonly URL_BOSS_GET_BASE_INFO: string = buildUrl("moment/boss/schoolCircle/getBaseInfo")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/26927
   */
  public static readonly URL_BOSS_GET_SCHOOL_BOSS_LIST: string = buildUrl("moment/boss/schoolCircle/getSchoolBossList")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/26919
   */
  public static readonly URL_BOSS_GET_SCHOOL_GEEK_LIST: string = buildUrl("moment/boss/schoolCircle/getSchoolGeekList")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/26911
   */
  public static readonly URL_BOSS_SCHOOLCIRCLE_ADD_SCHOOL_INFO: string = buildUrl("moment/boss/schoolCircle/addSchoolInfo")
  public static readonly URL_BOSS_SCHOOLCIRCLE_DELETE_SCHOOL_INFO: string = buildUrl("moment/boss/schoolCircle/deleteSchoolInfo")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/26903
   */
  public static readonly URL_BOSS_SCHOOLCIRCLE_GET_SCHOOL_INFO: string = buildUrl("moment/boss/schoolCircle/getSchoolInfo")
  /**
   * 709 新手引导弹窗
   */
  public static readonly URL_NOVICE_GUIDE_POPUP: string = buildUrl("zpgeek/app/geek/remind/trial")
  /**
   * 709 激活短信通知
   */
  public static readonly URL_SMS_ITEM_ACTIVATE: string = buildUrl("zpitem/smsNotice/activate")
  /**
   * 708 【引导提示】用户协议更新提示
   */
  public static readonly URL_GET_USER_AGREEMENT_POP_UP: string = buildUrl("zpgeek/app/agreement/update/tip")
  /**
   * 708 【引导提示】同意用户协议
   */
  public static readonly URL_USER_AGREEMENT_CONFIRM: string = buildUrl("zpgeek/app/agreement/agree")
  //    /**
  //     * 709 校友圈Boss引导
  //     */
  //    public static final String URL_MOMENT_BOSS_SCHOOL_CIRCLE_GUIDE = buildUrl("moment/boss/schoolCircle/guide",

  //    /**
  //     * 709 校友圈GEEK引导
  //     */
  //    public static final String URL_MOMENT_GEEK_SCHOOL_CIRCLE_GUIDE = buildUrl("zpgeek/app/schoolmate/guide",

  /**
   * 709 【资格证书】获取资格证书基础列表
   */
  public static readonly URL_GEEK_CERTIFICATION_LIST: string = buildUrl("zpgeek/cvapp/geek/certification/config/list")
  /**
   * 709 【资格证书】保存或修改牛人资格证书
   * <a href="https://api.weizhipin.com/project/30/interface/api/27367">...</a>
   */
  public static readonly URL_GEEK_SAVE_CERTIFICATION: string = buildUrl("zpgeek/cvapp/geek/certification/save")
  /**
   * 1106.3【资格证书】新增牛人资格证书
   * https://api.weizhipin.com/project/30/interface/api/528333
   */
  public static readonly URL_GEEK_ADD_CERTIFICATION: string = buildUrl("zpgeek/cvapp/geek/certification/add")
  //https://api.weizhipin.com/project/30/interface/api/508288
  public static readonly URL_GEEK_SAVE_HONOR: string = buildUrl("zpgeek/cvapp/geek/honor/save")
  /**
   * 901 【资格证书】JD页展示弹窗
   */
  public static readonly URL_JOB_LABEL_SHOW: string = buildUrl("zpgeek/app/geek/job/label/guide/show")
  /**
   * 710 牛人F3互动道具卡片入口
   * http://api.kanzhun-inc.com/project/30/interface/api/29101
   */
  public static readonly URL_GET_F3_ITEM_CARD: string = buildUrl("zpitem/geek/getF3ItemCard")
  /**
   * 710 修改职位职类
   * http://api.kanzhun-inc.com/project/30/interface/api/28855
   */
  public static readonly URL_JOB_REVISE_SUGGEST: string = buildUrl("zpboss/app/job/revise/position")
  /**
   * 710 驳回修改状态用户主动取消
   * http://************:8088/project/30/interface/api/29587
   */
  public static readonly URL_JOB_REVISE_GIVE_UP: string = buildUrl("zpboss/app/job/revise/abandon")
  public static readonly URL_JOB_TOP_COM_JOB_INFO: string = buildUrl("zpjob/topCom/jobInfo")
  /**
   * 完善福利提交关键词
   */
  public static readonly URL_JOB_SKILL_PERFECT_UPDATE: string = buildUrl("zpjob/job/skills/update")
  /**
   * 710 公司工作体验点赞
   */
  public static readonly URL_BOSS_BRAND_INFO_TASTE_LIKE: string = buildUrl("zpboss/app/brandInfo/taste/like")
  /**
   * 711【在线简历】获取工作经历
   * http://api.kanzhun-inc.com/project/30/interface/api/30355
   */
  public static readonly URL_GEEK_WORK_EXP_QUERY: string = buildUrl("zpgeek/app/geek/workexp/query")
  /**
   * 711【在线简历】获取项目经验
   * http://api.kanzhun-inc.com/project/30/interface/api/30361
   */
  public static readonly URL_GEEK_PROJECT_EXP_QUERY: string = buildUrl("zpgeek/app/geek/projexp/query")
  /**
   * 711【在线简历】查询教育经历
   * http://api.kanzhun-inc.com/project/30/interface/api/30367
   */
  public static readonly URL_GEEK_EDU_EXP_QUERY: string = buildUrl("zpgeek/app/geek/eduexp/query")
  /**
   * 【蓝领】查询蓝领职类技能标签
   * http://api.weizhipin.com/project/30/interface/api/30415
   */
  public static readonly URL_GEEK_SUGGEST_SKILL: string = buildUrl("zpgeek/app/geek/suggest/skill")
  /**
   * 【蓝领】查询蓝领推荐职类
   * http://api.kanzhun-inc.com/project/30/interface/api/30421
   */
  //    public static final String URL_BLUE_RECOMMAND_POSITION = buildUrl("zpgeek/app/bluecollarposition/recommendposition",
  public static readonly URL_BLUE_RECOMMAND_POSITION: string = buildUrl("zpdac/position/recommend")
  /**
   * http://api.weizhipin.com/project/30/interface/api/217434
   * 【期望】职场人职类推荐
   */
  public static readonly URL_ZPGEEK_APP_GEEK_EXPECTPOSITION_RECOMMENDCAREERPOSITION: string = buildUrl("zpgeek/cvapp/geek/expectposition/recommendcareerposition")
  /**
   * https://api.weizhipin.com/project/30/interface/api/248355
   * 【岗位经验】获取推荐职类
   */
  public static readonly URL_ZPGEEK_POST_EXP_POSITION_CONFIG: string = buildUrl("zpgeek/cvapp/geek/postexp/position/config")
  /**
   * 【工作经历】职类选择-查询推荐职类
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/88078
   */
  public static readonly URL_ZPGEEK_APP_GEEK_WORKEXP_RECOMMENDPOSITION: string = buildUrl("zpgeek/cvapp/geek/workexp/recommendposition")
  /**
   * C端F1推荐关键词
   */
  public static readonly URL_GEEK_RECOMMEND_POPKEYWORD: string = buildUrl("zpgeek/app/geek/recommend/popkeyword")
  /**
   * C端F1教育专业弹窗标记
   */
  public static readonly URL_GEEK_MAJOR_DIALOG_CLOSE: string = buildUrl("zpgeek/cvapp/geek/remind/warnexp/close")
  /**
   * 7.11获取商圈列表
   */
  public static readonly URL_BOSS_AREA_ALTERNATIVE_GET: string = buildUrl("zpjob/job/area/alternative/get")
  /**
   * 7.11 B50 获取用户手机号码
   */
  public static readonly URL_B50_CONTACT_ME: string = buildUrl("zpitem/boss/getPhone")
  /**
   * 7.11 B50 提交电话号码
   */
  public static readonly URL_B50_RESERVE_TEL: string = buildUrl("zpitem/boss/reserveTel")
  /**
   * 7.13 获取好友电话号码
   */
  public static readonly URL_ZPCHAT_CONTACT_GET: string = buildUrl("zpchat/contact/get")
  /**
   * 7.13 同意好友直接获取电话联系
   */
  public static readonly URL_ZPCHAT_CONTACT_AUTH: string = buildUrl("zpchat/contact/auth")
  /**
   * 【完善】促完善激励活动
   * http://api.weizhipin.com/project/30/interface/api/208433
   */
  public static readonly URL_ZPGEEK_APP_ACTIVITY_COMPLETEENCOURAGE: string = buildUrl("zpgeek/app/activity/completeencourage")
  /**
   * 7.13 资格证书suggest
   */
  public static readonly URL_GEEK_CERTIFICATION_AUTO_COMPLETE: string = buildUrl("zpCommon/certificate/autoComplete/v2")
  public static readonly URL_GEEK_HONOR_AUTOCOMPLET: string = buildUrl("zpgeek/cvapp/geek/honor/autocomplete")
  //https://api.weizhipin.com/project/30/interface/api/509170
  public static readonly URL_GEEK_WORD_RANK: string = buildUrl("zpgeek/cvapp/geek/workexp/positiontitle/recommend")
  public static readonly URL_OTHERPOSITION_RECPOSITIONS: string = buildUrl("zpgeek/cvapp/otherposition/recpositions")
  public static readonly URL_GEEK_WORD_RAND_POSITION_TITLE_AUTO: string = buildUrl("zpgeek/cvapp/geek/workexp/positiontitle/autocomplete")
  /**
   * 7.13 boss查看详情获取相关牛人
   */
  public static readonly URL_GEEK_RELATED_LIST: string = buildUrl("zpboss/app/geek/getRelatedInfo")
  /**
   * 7.14 boss查看牛人详情页引导搜索
   */
  public static readonly URL_GET_SEARCH_SIMILAR_INFO: string = buildUrl("zpboss/app/geek/getSearchSimilarInfo")
  /**
   * 820.48【商业】相似牛人推荐场景加强
   * http://api.kanzhun-inc.com/project/30/interface/api/147265
   */
  public static readonly URL_BOSS_GET_RELATED_INFO: string = buildUrl("zpitem/boss/getRelatedInfo")
  /**
   * 901.522 地址管理 获取地址信息
   * http://api.kanzhun-inc.com/project/30/interface/api/148773
   */
  public static readonly URL_BOSS_ADDRESS_EDIT: string = buildUrl("zpjob/boss/address/edit")
  /**
   * 911.521 推荐同事地址
   * http://api.kanzhun-inc.com/project/30/interface/api/148773
   */
  public static readonly URL_BOSS_ADDRESS_REC_LIST: string = buildUrl("zpjob/boss/address/rec/list")
  public static readonly URL_BOSS_ADDRESS_BATCH_UPDATE: string = buildUrl("zpjob/boss/address/batch/update")
  /**
   * 901.522 地址管理 提交、更新地址信息
   * http://api.kanzhun-inc.com/project/30/interface/api/148780
   */
  public static readonly URL_BOSS_ADDRESS_UPDATE: string = buildUrl("zpjob/boss/address/update")
  public static readonly URL_ADDRESS_REPORT: string = buildUrl("zpjob/boss/address/report/address")
  /**
   * 901.522 地址管理 获取商圈
   * http://api.kanzhun-inc.com/project/30/interface/api/148787
   */
  public static readonly URL_BOSS_ADDRESS_AREA_LIST: string = buildUrl("zpjob/boss/address/area/list")
  /**
   * 901.522 地址管理 获取商圈
   * http://api.kanzhun-inc.com/project/30/interface/api/148787
   */
  public static readonly URL_BOSS_ADDRESS_MATE_LIST: string = buildUrl("zpjob/boss/address/mate/rec/list")
  /**
   * 901.522 地址管理 获取商圈
   * http://api.kanzhun-inc.com/project/30/interface/api/148787
   */
  public static readonly URL_BOSS_ADDRESS_DELETE: string = buildUrl("zpjob/boss/address/del")
  /**
   * 901.522 地址管理 发布职位获取boss地址列表
   * http://api.kanzhun-inc.com/project/30/interface/api/149578
   */
  public static readonly URL_BOSS_ADDRESS_LIST: string = buildUrl("zpjob/boss/address/list")
  public static readonly URL_JOB_TEMPLATE_ADDRESS_LIST: string = buildUrl("zpjob/job/template/addr/list")
  public static readonly URL_JOB_PROJECT_ADDRESS_LIST: string = buildUrl("zpjob/preferred/addr/list")
  /**
   * 920.81 VR拍摄完毕后添加拍摄地点信息，选择地址
   */
  public static readonly URL_BOSS_SELECT_TAKE_PHOTO_ADDRESS_LIST: string = buildUrl("zpjob/boss/address/valid/list")
  /**
   * 1019.82
   * 品牌全景照片-品牌是否具有VR权限
   */
  public static readonly URL_BOSS_PANORAMIC_PICTURE_CHECK_BRAND_HAS_PERMISSION: string = buildUrl("zpCompany/brandComplete/panoramicPicture/checkBrandHasPermission")
  /**
   * http://api.weizhipin.com/project/30/interface/api/204854
   */
  public static readonly URL_BOSS_ADDRESS_RELATE_JOB_LIST: string = buildUrl("zpjob/boss/address/relation/job/list")
  /**
   * 【求职期望】完善预处理
   */
  public static readonly URL_GEEK_EXPECT_POSTION: string = buildUrl("zpgeek/cvapp/geek/expectposition/completecheck")
  /**
   * 【基本信息】牛人配置开关
   */
  public static readonly URL_GEEK_PROFILE_SETTING_VALUE: string = buildUrl("zpgeek/cvapp/geek/profile/settingvalue")
  /**
   * 【基本信息】学生期望顺序
   */
  public static readonly F1_TAB_CONFIG_SORT: string = buildUrl("zpgeek/app/geek/f1tab/config/sort")
  /**
   * 【期望】新职类基础数据
   */
  public static readonly URL_GEEK_EXPECT_POSITION_CONFIG: string = buildUrl("zpgeek/cvapp/geek/expectposition/config")
  /**
   * 7.14 牛人简历页引导简历刷新
   * http://api.kanzhun-inc.com/project/30/interface/api/39361
   */
  public static readonly URL_GUIDE_RESUME_REFRESH: string = buildUrl("zpitemGeek/resume/guideResumeRefresh")
  /**
   * 7.14 牛人F4公司页改版，【品牌】f4品牌页数据
   */
  public static readonly URL_GET_BRAND_F4_DATA: string = buildUrl("zpgeek/app/brand/f4/data")
  /**
   * 7.14 牛人F4公司页改版，【品牌】f4推荐品牌列表
   */
  public static readonly URL_GET_BRAND_RCD_LIST: string = buildUrl("zpgeek/app/brand/rcd/list")
  /**
   * 7.14 批量保存作品图片
   */
  public static readonly URL_SAVE_RESUME_DESIGN_WORK: string = buildUrl("zpgeek/cvapp/resume/design/save")
  /**
   * 获取简历指定类型的图片
   */
  public static readonly URL_GET_RESUME_PHOTO_FOR_TYPE: string = buildUrl("zpgeek/cvapp/resume/design/list")
  /**
   * 7.14 【F4求职攻略】查询列表
   */
  public static readonly URL_GET_STUDENT_TOPIC_LIST: string = buildUrl("zpgeek/app/studenttopic/querylist")
  /**
   * 7.14 【附件简历】附件简历保存
   */
  public static readonly URL_SAVE_GEEK_RESUME: string = buildUrl("zpgeek/cvapp/geek/resume/save")
  /**
   * 7.14 下载简历文件接口
   */
  public static readonly URL_DOWN_GEEK_RESUME: string = buildUrl("zpupload/resume/download")
  /**
   * 7.15 获取boss和牛人匹配的职位
   * http://api.kanzhun-inc.com/project/30/interface/api/42942
   */
  public static readonly URL_MATCH_ITEM_JOB: string = buildUrl("zpitem/matchItemJob")
  /**
   * 909.46 匿名牛人开聊默认职位选择
   * http://api.kanzhun-inc.com/project/30/interface/api/196138
   */
  public static readonly ZP_ITEM_BOSS_DEFAULT_JOB4SELECT: string = buildUrl("zpitem/boss/defaultJob4Select")
  // 聊天模块中使用
  public static readonly URL_VIDEO_INTERVIEW_APPLY: string = buildUrl("zpchat/videoInterview/apply")
  /**
   * 7.15 编辑品牌行业
   * http://api.kanzhun-inc.com/project/30/interface/api/42966
   */
  public static readonly URL_BRAND_COMPLETE_UPDATE_INDUSTRY: string = buildUrl("zpboss/app/brandComplete/updateBrandIndustry")
  /**
   * 7.15 获取职位分享图片列表接口
   * http://api.kanzhun-inc.com/mock/30/api/zpboss/app/wjd/image/list
   * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=52498877
   */
  public static readonly URL_WJD_SHARE_PICTURE_LIST: string = buildUrl("zpjob/wjd/image/list")
  /**
   * 715 boss高搜   搜索畅聊卡余量
   */
  public static readonly URL_BOSS_ADVANCED_SEARCH_CHAT_CARD: string = buildUrl("zpitem/searchChatCard/getRemainInfo")
  /**
   * 1120.233【商业】vvip-精选版升级 - APP获取匿名牛人开聊消耗类型
   */
  public static readonly URL_ZPITEM_BOSS_ANONYMOUS_CHAT_USETYPE: string = buildUrl("zpitem/boss/anonymous/chat/useType")
  /**
   * 1120.233【商业】vvip-精选版升级 - 获取VVIP批量开聊基本信息
   */
  public static readonly URL_ZPBLOCK_VVIP_BATCH_CHAT_GET_INFO: string = buildUrl("zpblock/vvip/batch/chat/get/info")
  /**
   * 715 boss高搜   批量使用聊天卡
   */
  public static readonly URL_BOSS_ADVANCED_SEARCH_CHAT_CARD_BATCHUSE: string = buildUrl("zpitem/searchChatCard/batchUse")
  /**
   * 715 geek-获取主题配置列表
   */
  public static readonly URL_GEEK_GET_THEME_LIST: string = buildUrl("moment/geek/brandTopic/qa/getThemeList")
  /**
   * 715 geek-添加问题
   */
  public static readonly URL_GEEK_ADD_QUESTION: string = buildUrl("moment/geek/brandTopic/qa/addQuestion")
  /**
   * 715 geek-获取邀请回答列表
   */
  public static readonly URL_GEEK_INVITE_LIST: string = buildUrl("moment/geek/brandTopic/qa/inviteList")
  /**
   * 715 boss-获取邀请回答列表
   */
  public static readonly URL_BOSS_INVITE_LIST: string = buildUrl("moment/boss/brandTopic/qa/inviteList")
  /**
   * 715 geek-问题聚合页
   */
  public static readonly URL_GEEK_QUESTION_AGGREGAATION: string = buildUrl("moment/geek/brandTopic/qa/questionAggregation")
  /**
   * 715 geek-问题聚合页
   */
  public static readonly URL_BOSS_QUESTION_AGGREGAATION: string = buildUrl("moment/boss/brandTopic/qa/questionAggregation")
  /**
   * 715 BOSS-回答问题
   */
  public static readonly URL_BOSS_ADD_ANSWER: string = buildUrl("moment/boss/brandTopic/qa/addAnswer")
  /**
   * https://api.weizhipin.com/project/30/interface/api/298224
   */
  public static readonly URL_FAST_REPLY_GET_TEMPLATE: string = buildUrl("zpchat/fastreply/getTemplate")
  public static readonly URL_JOB_JOB_QUALITY_SCORE: string = buildUrl("zpjob/job/quality/score")
  public static readonly URL_JOB_JOB_QUALITY_SCORE_CHANGE: string = buildUrl("zpjob/job/quality/score/change")
  public static readonly URL_JOB_JOB_QUALITY_DEMO: string = buildUrl("zpjob/job/quality/demo")
  /**
   * https://api.weizhipin.com/project/30/interface/api/297900
   */
  public static readonly URL_FEED_BACK_REASSURANEC_TEMPLATE: string = buildUrl("zpchat/feedback/reassuranceTemplate")
  /**
   * https://api.weizhipin.com/project/30/interface/api/297873
   */

  public static readonly URL_FEED_BACK_REASSURANEC_ANSWER: string = buildUrl("zpchat/feedback/reassuranceAnswer")
  /**
   * 直播引导条
   */
  public static readonly URL_BRAND_LIVE_GUIDE: string = buildUrl("zpgeek/app/geek/brand/related/query")
  /**
   * // C端的面试信息 岗位信息 工商信息  拆出作为额外接口  1006.39
   */
  public static readonly URL_BRAND_COM_INFO_QUERY: string = buildUrl("zpgeek/app/companyinfo/query")
  /**
   * b获取公司主页浮窗信息
   */
  public static readonly URL_BRAND_FLOATING_LAYER: string = buildUrl("zpboss/app/brandInfo/getFloatingLayer")
  /**
   * 715 geek-回答问题
   */
  public static readonly URL_GEEK_ADD_ANSWER: string = buildUrl("moment/geek/brandTopic/qa/addAnswer")
  /**
   * 715 geek-主题聚合页
   */
  public static readonly URL_GEEK_THEME_AGGREGAATION: string = buildUrl("moment/geek/brandTopic/qa/themeAggregation")
  /**
   * 715 boss-问题聚合页
   */
  public static readonly URL_BOSS_THEME_AGGREGAATION: string = buildUrl("moment/boss/brandTopic/qa/themeAggregation")
  /**
   * 716 电话直拨f1引导卡片接口
   * http://api.kanzhun-inc.com/project/30/interface/api/45542
   */
  public static readonly URL_DIRECT_CALL_GUIDE: string = buildUrl("zpitem/directCall/guide")
  /**
   * 716 关闭电话直拨授权
   * http://api.kanzhun-inc.com/project/30/interface/api/45549
   */
  public static readonly URL_DIRECT_CALL_CANCEL: string = buildUrl("zpitem/directCall/cancel")
  /**
   * 关闭电话直拨授权 展示原因列表
   * https://api.weizhipin.com/project/30/interface/api/627530
   */
  public static readonly URL_DIRECT_CALL_CANCEL_WINDOW: string = buildUrl("zpitem/directCall/cancelWindow")
  /**
   * 1008 赠送简历刷新
   * https://api.weizhipin.com/project/30/interface/api/320112
   */
  public static readonly URL_DIRECT_CALL_ACTIVITY: string = buildUrl("zpitem/directCall/activity/info")
  /**
   * 1008 赠送简历刷新-开启授权
   * https://api.weizhipin.com/project/30/interface/api/320139
   */
  public static readonly URL_DIRECT_SETTING_UPDATE: string = buildUrl("zpitem/directCall/activity/setting/update")
  /**
   * 716 电话直拨预校验
   * http://api.kanzhun-inc.com/project/30/interface/api/45556
   */
  public static readonly URL_DIRECT_CALL_PRE_USE: string = buildUrl("zpitem/directCall/preUse")
  /**
   * 716 使用电话直拨
   * http://api.kanzhun-inc.com/project/30/interface/api/45563
   */
  public static readonly URL_DIRECT_CALL_USE: string = buildUrl("zpitem/directCall/use")
  /**
   * 牛人拨打电话，预操作
   * http://api.weizhipin.com/project/30/interface/api/217832
   */
  public static readonly URL_DIRECT_CALL_GEEK_PRE_USE: string = buildUrl("zpitem/directCall/geek/preUse")
  public static readonly URL_DIRECT_CALL_GEEK_USE: string = buildUrl("zpitem/directCall/geek/use")
  /**
   * 716 垃圾简历优化
   */
  public static readonly URL_USER_GARBAGE_RESUME_UPDATE: string = buildUrl("zpgeek/app/resume/userdesc/label/query")
  /**
   * 8.07【我的优势】生成我的优势文案
   */
  public static readonly URL_USER_GENERATE_ADVANTAGE: string = buildUrl("zpgeek/app/resume/userdesc/generate")
  /**
   * nlp 简历解析反馈
   * http://api.kanzhun-inc.com/project/30/interface/api/45507
   */
  public static readonly URL_NLP_RESUME_CALLBACK: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/tips")
  /**
   * 716 【POI】根据省市区信息匹配直聘城市code
   */
  public static readonly URL_POI_MATCH_CITY: string = buildUrl("zpgeek/app/poi/matchcity")
  /**
   * 717 【v3小课】开始/结束资源
   * http://api.kanzhun-inc.com/project/30/interface/api/45964
   */
  public static readonly URL_GET_COURSE_OPERATE: string = buildUrl("moment/get/course/operateResource")
  /**
   * 717
   * 付费在线职位修改职位名称前校验
   * http://api.kanzhun-inc.com/project/207/interface/api/48462
   */
  public static readonly URL_BOSS_WISDOM_CHANGE_JOB_CHECK: string = buildUrl("zpboss/app/job/name/change/check")
  /**
   * 717
   * 付费在线职位修改名称
   * http://api.kanzhun-inc.com/project/207/interface/api/48470
   */
  public static readonly URL_BOSS_WISDOM_CHANGE_JOB: string = buildUrl("zpboss/app/job/name/change")
  /**
   * 717 卡片曝光统计接口
   * http://************:8088/project/30/interface/api/48926
   */
  public static readonly URL_CARD_EXPOSURE_STATISTIC: string = buildUrl("zpblock/prolong/exposure")
  //718公司圈相关

  /**
   * 718 geek-获取问题详情
   * http://api.kanzhun-inc.com/project/30/interface/api/51152
   */
  public static readonly CIRCLE_GEEK_GET_QUESTION_DETAIL: string = buildUrl("moment/geek/brandTopic/question/getQuestionDetail")
  /**
   * 718 geek-获取答案详情
   * http://api.kanzhun-inc.com/project/30/interface/api/51312
   */
  public static readonly CIRCLE_GEEK_GET_ANSWER_DETAIL: string = buildUrl("moment/geek/brandTopic/question/getAnswerDetail")
  /**
   * 718 geek-答案评论
   * http://api.kanzhun-inc.com/project/30/interface/api/51267
   */
  public static readonly CIRCLE_GEEK_POST_ANSWER_COMMENT: string = buildUrl("moment/geek/brandTopic/question/answerComment")
  /**
   * 718 boss-获取问题详情
   * http://api.kanzhun-inc.com/project/30/interface/api/51157
   */
  public static readonly CIRCLE_BOSS_GET_QUESTION_DETAIL: string = buildUrl("moment/boss/brandTopic/question/getQuestionDetail")
  /**
   * 718 boss-获取答案详情
   * http://api.kanzhun-inc.com/project/30/interface/api/51327
   */
  public static readonly CIRCLE_BOSS_GET_ANSWER_DETAIL: string = buildUrl("moment/boss/brandTopic/question/getAnswerDetail")
  /**
   * 718 boss-答案评论
   * http://api.kanzhun-inc.com/project/30/interface/api/51257
   */
  public static readonly CIRCLE_BOSS_POST_ANSWER_COMMENT: string = buildUrl("moment/boss/brandTopic/question/answerComment")
  /**
   * 简历助手记录提示牛人的最后开聊语
   */
  public static readonly URL_RESUME_ASSISTANT_RECORD_GEEK: string = buildUrl("zpblock/resume/assistant/record/geek")
  /**
   * 记录简历助手boss询问牛人记录
   */
  public static readonly URL_RESUME_ASSISTANT_RECORD_ASK_GEEK: string = buildUrl("zpblock/resume/assistant/record/ask")
  /**
   * face++人脸认证结果保存为文件，然后上传服务器
   */
  public static readonly URL_FACE_PLUS_FILE_UPLOAD: string = buildUrl("certification/trueman/uploadFaceData")
  /**
   * 【用户召回】获取老用户活跃时间
   * http://api.kanzhun-inc.com/project/30/interface/api/52122
   */
  public static readonly URL_USER_ACTIVE_QUERY: string = buildUrl("zpgeek/cvapp/useractive/query")
  /**
   * 【用户召回】关闭召回引导页
   * https://api.weizhipin.com/project/30/interface/api/52127
   */

  public static readonly URL_USER_ACTIVE_CLOSE: string = buildUrl("zpgeek/cvapp/useractive/delete")
  /**
   * 1103.14【用户召回】获取添加工作经历建议数据
   * https://api.weizhipin.com/project/30/interface/api/512896
   */
  public static readonly URL_USER_ACTIVE_EXP_ADD_SUG_QUERY: string = buildUrl("zpgeek/cvapp/useractive/newactive/expaddsug/query")
  /**
   * 1103.14【用户召回】跳过添加工作经历引导
   * https://api.weizhipin.com/project/30/interface/api/515386
   */
  public static readonly URL_USER_ACTIVE_EXP_ADD_SUG_CLOSE: string = buildUrl("zpgeek/cvapp/useractive/newactive/expaddsug/close")
  /**
   * 1105.131 未读新招呼牛人列表
   * https://api.weizhipin.com/project/30/interface/api/520732
   */
  public static readonly URL_ZPRELATION_FRIEND_UNREAD_NEWGREET_LIST: string = buildUrl("zprelation/friend/unReadNewGreetList")
  /**
   * http://api.weizhipin.com/project/30/interface/api/203857
   **/
  public static readonly URL_APPLYSTATUS_TIP_CLOSE: string = buildUrl("zpgeek/cvapp/applystatus/change/tip/close")
  // public static readonly URL_RECORD_SET_STATISTIC_TIME: string = buildUrl("zprelation/applyHistory/setStartDate")
  /**
   * 718 获取推荐行业
   */
  public static readonly URL_GEEK_GET_RECOMMEND_INDUSTRY: string = buildUrl("zpgeek/app/recommend/industry/query")
  /**
   * 718 获取推荐行业列表(服务端重新排序后的)
   */
  public static readonly URL_GEEK_GET_RECOMMEND_INDUSTRY_LIST: string = buildUrl("zpgeek/app/industry/order/list")
  /**
   * 面试附近推荐职位弹窗  f1batch接口依赖
   * http://api.kanzhun-inc.com/project/30/interface/api/54667
   */
  public static readonly URL_GET_F1_INTERVIEW_RECOMMEND_JOB_GUIDE: string = buildUrl("zpgeek/app/f1/interview/recjob/tip")
  /**
   * 1119 学生主题所有专区
   * https://api.weizhipin.com/project/30/interface/api/619736
   */
  public static readonly URL_GET_STU_OPERATE_DETAIL: string = buildUrl("zpgeek/app/student/operate/zone/detail")
  /**
   * 719 查询工作方向标签
   * http://api.kanzhun-inc.com/project/30/interface/api/54227
   */
  public static readonly URL_GET_GEEK_DIRECTION_LIST: string = buildUrl("zpgeek/app/geek/expectposition/querydirection")
  /**
   * 719 保存工作方向标签
   * http://api.kanzhun-inc.com/project/30/interface/api/54232
   */
  public static readonly URL_SAVE_GEEK_DIRECTION_LIST: string = buildUrl("zpgeek/app/geek/expectposition/setdirection")
  /**
   * 719 引导添加期望工作方向
   * http://api.kanzhun-inc.com/project/30/interface/api/54462
   */
  public static readonly URL_GET_GEEK_DIRECTION_GUIDE: string = buildUrl("zpgeek/app/geek/guide/employmentdirection")
  public static readonly URL_GET_F1_TIP_QUERY: string = buildUrl("zpgeek/app/f1/tip/query")
  public static readonly URL_SUGGEST_EXPECT_QUERY: string = buildUrl("zpgeek/app/f1/suggestexpect/query")
  /**
   * 1109【F1】小城市混推 周边推荐城市补量职位
   * https://api.weizhipin.com/project/30/interface/api/553564
   */
  public static readonly URL_NEARBY_CITY_JOBLIST: string = buildUrl("zpgeek/app/f1/nearby/city/joblist")
  /**
   * 动态条排序
   * http://api.kanzhun-inc.com/project/30/interface/api/153449
   */
  public static readonly URL_ZPUSER_DYNAMIC_BAR_SAVE_SORT: string = buildUrl("zpuser/dynamicBar/saveSort")
  /**
   * 812  学生F1 职位 城市 保存
   */
  public static readonly URL_STUDENT_CITY_POSITION_SAVE: string = buildUrl("zpgeek/app/f1/student/expect/cityposition/save")
  /**
   * 719 电话交换助手-打开/关闭
   * http://api.kanzhun-inc.com/project/30/interface/api/54592
   */
  public static readonly URL_PHONE_EXCHANGE_SWITCH: string = buildUrl("zpboss/app/job/phoneExchangeSwitch/change")
  /**
   * 719 电话交换助手-新发职位
   * http://api.kanzhun-inc.com/project/30/interface/api/54577
   */
  public static readonly URL_BOSS_JOB_PHONE_EXCHANGE_SWITCH_GET: string = buildUrl("zpboss/app/job/phoneExchangeSwitch/get")
  public static readonly URL_BOSS_JOB_GET_STATUS: string = buildUrl("zpboss/app/job/phoneExchangeSwitch/getStatus")
  /**
   * 719 f1筛选获取限免剩余时间
   * http://api.kanzhun-inc.com/project/30/interface/api/54632
   */
  public static readonly URL_GET_VIP_PRIVILEGE_FILTER_REMAIN_TIME: string = buildUrl("zpboss/app/recomment/filter/remain/time")
  /**
   * 801 发现 搜索联想词
   * http://api.kanzhun-inc.com/mock/30/api/moment/discover/search/autoComplete
   */
  public static readonly URL_GET_SEARCH_AUTO_COMPLETE: string = buildUrl("moment/discover/search/autoComplete")
  /**
   * 801 发现 搜索功能
   * http://api.kanzhun-inc.com/project/30/interface/api/57952
   */
  public static readonly URL_GET_SEARCH_CONTENT: string = buildUrl("moment/discover/search/content")
  /**
   * 【815搜索】搜索话题
   * https://api.weizhipin.com/project/30/interface/api/143407
   */
  public static readonly URL_SEARCH_CONTENT_TOPIC: string = buildUrl("moment/discover/search/topic")
  /**
   * 1004【搜索】综合搜索列表数据
   * https://api.weizhipin.com/project/30/interface/api/265338
   */
  public static readonly URL_SEARCH_COMPOSITE_MIXED: string = buildUrl("moment/discover/search/mixed")
  /**
   * 1004【NLP简历解析】查询指定类型的待同步内容
   * https://api.weizhipin.com/project/30/interface/api/261117
   */
  public static readonly URL_ZPGEEK_NLP_RESUME_PARSER_QUERY_ITEM: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/item/content/query")
  /**
   * 1004【NLP简历解析】查询指定类型的待新增列表
   * https://api.weizhipin.com/project/30/interface/api/261162
   */
  public static readonly URL_ZPGEEK_NLP_RESUME_PARSER_ADDITIONAL_LIST: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/item/additional/list")
  /**
   * https://api.weizhipin.com/project/30/interface/api/178624
   * 906搜索- 职位百科
   */
  public static readonly URL_SEARCH_CONTENT_JOB_WIKI: string = buildUrl("moment/get/searchWiki")
  /**
   * 910 简历范本入口
   * http://api.weizhipin.com/project/30/interface/api/200320
   */
  public static readonly URL_GEEK_VIP_RESUME_MODEL_ENTRANCE: string = buildUrl("zpitem/geek/vip/resume/model/entrance")
  /**
   * 【在线简历预览】获取推荐牛人卡片示例
   * https://api.weizhipin.com/project/30/interface/api/325053
   */
  public static readonly URL_ZPGEEK_RESUME_PREVIEW_RECOMMEND_GEEK_QUERY: string = buildUrl("zpgeek/cvapp/preview/example/recommendgeek/query")
  /**
   * 801 发现 热门话题
   * http://api.kanzhun-inc.com/project/30/interface/api/59852
   */

  public static readonly URL_GET_HOT_TOPIC: string = buildUrl("moment/discover/hotTopic")
  /**
   * 801 发现 热门话题
   * http://api.kanzhun-inc.com/project/30/interface/api/59857
   */

  public static readonly URL_GET_HOT_QUESTION: string = buildUrl("moment/discover/hotQuestion")
  /**
   * 1004 热门评论榜单
   * https://api.weizhipin.com/project/30/interface/api/269226
   */
  public static readonly URL_GET_HOT_COMMENT: string = buildUrl("moment/youle/hotComment")
  /**
   * moment/youle/hotLongText
   * https://api.weizhipin.com/project/30/interface/api/269001
   */
  public static readonly URL_GET_HOT_COLUMN: string = buildUrl("moment/youle/hotLongText")
  /**
   * 805 发现 获取发现页红点是否存在
   * http://api.kanzhun-inc.com/project/30/interface/api/93202
   */

  public static readonly URL_GET_RED_DOT_EXIST: string = buildUrl("moment/discover/icon/redDotExist")
  /**
   * 801 搜畅引导牛人读消息
   * http://api.kanzhun-inc.com/project/30/interface/api/62192
   */
  public static readonly URL_GET_SEARCH_CHAT_CARD_GUIDE_MESSAGE: string = buildUrl("zpitem/searchChatCard/guideViewMsg")
  public static readonly URL_GOLDEN_GEEK_INVITATIION: string = buildUrl("zpgeek/app/f1/goldengeek/invitation")
  /***
   * https://api.weizhipin.com/project/30/interface/api/600002
   **/
  public static readonly URL_STU_EXP_MANAGE_GUIDE: string = buildUrl("zpgeek/app/expectmanager/useguide/query")
  public static readonly URL_STU_EXP_MANAGE_GUIDE_CLOSE: string = buildUrl("zpgeek/app/expectmanager/useguide/close")
  /**
   * 【F1弹窗】安心保职位弹窗
   * https://api.weizhipin.com/project/30/interface/api/596282
   */
  public static readonly URL_AXB_JOB_TIP: string = buildUrl("zpgeek/app/f1/axb/job/tip")
  public static readonly URL_GEEK_RESUME_SYNC_LIST: string = buildUrl("zpgeek/app/geek/resume/synclist")
  public static readonly URL_GEEK_CHECK_PHONE_STATUS: string = buildUrl("zppassport/user/checkPhoneStatus")
  public static readonly URL_GEEK_CHECK_PHONE_FINISH: string = buildUrl("zppassport/user/finishCheckPhoneStatus")
  public static readonly URL_ZPGEEK_POPUP_TIP_CLOSE: string = buildUrl("zpgeek/app/f1/popup/tip/close")
  public static readonly URL_GEEK_CLICK_SECURITY_GUIDE: string = buildUrl("zpuser/account/clickSecurityGuide")
  /**
   * https://api.weizhipin.com/project/30/interface/api/406267
   * 获取国家码
   */
  public static readonly URL_USER_COUNTTRY_CODE: string = buildUrl("zpuser/countryCode")
  public static readonly URL_GEEK_GET_SECURITY_GUIDE: string = buildUrl("zpuser/account/getSecurityGuide")
  public static readonly URL_GEEK_RESUME_SEND: string = buildUrl("zpgeek/app/geek/resume/send")
  public static readonly URL_EXCHANGE_AUT_SAVE: string = buildUrl("zpchat/exchange/auth/save")
  public static readonly URL_EXCHANGE_AUTH_RESET: string = buildUrl("zpchat/exchange/auth/reset")
  public static readonly URL_EXCHANGE_TEST: string = buildUrl("zpchat/exchange/test")
  public static readonly URL_EXCHANGE_GET_PHONE: string = buildUrl("zpchat/exchange/getPhoneV2")
  public static readonly URL_EXCHANGE_JD_BUSINESS_CALL: string = buildUrl("zpblock/passivity/phone/call/use")
  public static readonly URL_BOSS_PROFILE_GET_AVATAR: string = buildUrl("moment/boss_profile/getAvatarStickerInfo")
  /**
   * 聊天中通过ZPManager中查看面试间状态，进入面试间
   */
  public static readonly URL_INTERVIEW_MEDIA_STATUS: string = buildUrl("zpinterview/interview/media/roomStatus")
  public static readonly URL_BOSS_PROFILE_ADD_AVATAR: string = buildUrl("moment/boss_profile/addAvatarSticker")
  public static readonly URL_BOSS_PROFILE_REMOVE_AVATAR: string = buildUrl("moment/boss_profile/removeAvatarSticker")
  public static readonly URL_GEEK_BLUE_BRAND_INFO: string = buildUrl("zpgeek/app/dz/brandInfo/getBrandInfo")
  /**
   * 获取阿里sdkinfo
   * http://api.kanzhun-inc.com/project/30/interface/api/182684
   */
  public static readonly URL_ZPPASSPORT_PHONE_SDK_INFO: string = buildUrl("zppassport/phone/sdkInfo")
  /**
   * 720 领取试用的电话直拨
   * http://api.kanzhun-inc.com/project/30/interface/api/59247
   */
  public static readonly URL_VIRTUAL_CALL_RECEIVE_GIFT_ITEM: string = buildUrl("zpitem/directCall/getGiftItem")
  /**
   * 720 代招公司
   * 获取用户填写过的代招公司信息
   * http://api.kanzhun-inc.com/project/30/interface/api/61352
   */
  public static readonly URL_BOSS_RECRUITCOMPANY_MYRECRUIT: string = buildUrl("hunter/app/recruitCompany/myRecruit")
  /**
   * http://api.kanzhun-inc.com/project/20/interface/api/111587
   * （app）获取中介公司代招的所有品牌
   */
  public static readonly URL_BOSS_RECRUITCOMPANY_GETCOMPROXYRECRUITBRAND: string = buildUrl("zpboss/app/recruitCompany/getComProxyRecruitBrand")
  public static readonly URL_JOB_PREFERRED_GETBRANDLIST: string = buildUrl("zpjob/preferred/getBrandList")
  /**
   * 校验选中的代招公司和品牌信息
   * http://api.kanzhun-inc.com/project/30/interface/api/61682
   */
  public static readonly URL_BOSS_RECRUITCOMPANY_CHECK: string = buildUrl("hunter/app/recruitCompany/check")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/88043
   * 804 发布代招职位获取推荐地址
   */
  public static readonly URL_BOSS_RECRUIT_ADDRESS_SUGGEST: string = buildUrl("zpboss/app/job/recruit/address/suggest")
  /**
   * 根据公司名称模糊匹配公司
   * http://api.kanzhun-inc.com/project/30/interface/api/61357
   */
  public static readonly URL_BOSS_RECRUITCOMPANY_GETCOMPANY: string = buildUrl("zpboss/app/recruitCompany/getCompany")
  /**
   * 根据公司ID公司名称获取品牌
   * http://api.kanzhun-inc.com/project/30/interface/api/61362
   */
  public static readonly URL_BOSS_RECRUITCOMPANY_GETBRAND: string = buildUrl("zpboss/app/recruitCompany/getBrand")
  /**
   * 添加用户新建的公司代招信息
   * http://api.kanzhun-inc.com/project/30/interface/api/61367
   */
  public static readonly URL_BOSS_RECRUITCOMPANY_ADDCOMP: string = buildUrl("hunter/app/recruitCompany/addComp")
  /**
   * boss拨打电话400
   * http://api.kanzhun-inc.com/project/30/interface/api/76137
   */
  public static readonly URL_DIAL_PHONE: string = buildUrl("zpitem/boss/dialPhone")
  /**
   * 牛人评价人才经纪人保存
   * http://api.kanzhun-inc.com/project/30/interface/api/62177
   */
  public static readonly URL_EVALUATE_AGENTRECRUIT_SAVE_TAGS: string = buildUrl("zpgeek/app/intermediary/evaluation/save")
  /**
   * 牛人评价人才经纪人追评
   * http://api.kanzhun-inc.com/project/30/interface/api/137166
   */
  public static readonly URL_EVALUATE_AGENTRECRUIT_ADDITIONAL_SAVE: string = buildUrl("zpgeek/app/intermediary/evaluation/additional/save")
  /**
   * 牛人评价人才经纪人页面
   * http://api.kanzhun-inc.com/project/30/interface/api/62172
   */
  public static readonly URL_EVALUATE_AGENTRECRUIT_TAGS: string = buildUrl("zpgeek/app/intermediary/evaluation/tags")
  /**
   * 802 获取高级筛选专业筛选项
   * http://api.kanzhun-inc.com/project/30/interface/api/75692
   */
  public static readonly URL_GET_SENIOR_FILTER_MAJOR: string = buildUrl("zpblock/filter/seniorScreen/get")
  /**
   * 803 geek个人主页动态列表
   * http://api.kanzhun-inc.com/project/30/interface/api/78072
   */
  public static readonly URL_GET_GEEK_HOMEPAGE_DYNAMIC_LIST: string = buildUrl("moment/get/homePage/geek/dynamic/list")
  public static readonly URL_GET_GEEK_HOMEPAGE_DYNAMIC_LISTV2: string = buildUrl("moment/get/homePage/geek/dynamic/listV2")
  /**
   * 803 geek个人主页问答
   * http://api.kanzhun-inc.com/project/30/interface/api/78547
   */
  public static readonly URL_GET_GEEK_HOMEPAGE_QA_LIST: string = buildUrl("moment/get/homePage/geek/qa/list")
  public static readonly URL_CHASE_CHAT: string = buildUrl("zpdac/chaseChat/judge")
  public static readonly URL_CHAT_AGAIN_BATCH_BANNER: string = buildUrl("zpitem/chatAgainBatch/f2/banner")
  public static readonly URL_CHAT_GREETING_JOB_QUESTION_BANNER: string = buildUrl("zpchat/greeting/job/question/banner")
  public static readonly URL_CHAT_BATCH_CHAT_F2_CARD: string = buildUrl("zpblock/batchChat/f2/card")
  public static readonly URL_CHAT_AGAIN_NUM_CHECK: string = buildUrl("zpitem/chatAgainBatch/num/check")
  public static readonly URL_CHAT_AGAIN_CLOSE: string = buildUrl("zpitem/chatAgainBatch/operation/close")
  public static readonly URL_CHASE_QUERY: string = buildUrl("zpdac/chaseChat/query")
  public static readonly URL_BATCH_CHAT_F2_CLOSE: string = buildUrl("zpblock/batchChat/f2/card/click/closed")
  public static readonly URL_BATCH_CHAT_F2_RED_POINT_CLICK: string = buildUrl("zpblock/batchChat/f2/redPoint/click")
  /*1002.181*/
  public static readonly URL_GET_CHASE_CHAT_LIST: string = buildUrl("zprelation/friend/getChaseChatList")
  public static readonly URL_GET_CHAT_LEAD_TEMPLATE: string = buildUrl("zpchat/message/getChatLeadTemplate")
  public static readonly URL_USER_MARK_LABEL_MANAGER_LIST: string = buildUrl("zprelation/userMark/labelManageList")
  public static readonly URL_USER_MARK_LABEL_MANAGER_DEL: string = buildUrl("zprelation/userMark/labelManageDel")
  public static readonly URL_MAP_GET_GEEK_ADDRESS: string = buildUrl("zpgeek/cvapp/geek/homeaddress/query")
  public static readonly URL_GEEK_MAP_SEARCH_JOBLIST: string = buildUrl("zpgeek/app/geek/map/search/joblist")
  // 工作体验  看看别人怎么写
  public static readonly URL_WORK_EXP_LOOK_OTHER_WRITE: string = buildUrl("zpboss/app/brandInfo/taste/templateList")
  // B.C聊天点击咨询 获取 要跳转的URL
  public static readonly URL_CLICK_QUESTION_SKIP_URL: string = buildUrl("moment/connect/geek/getReqLink")
  //804  直联消息详情
  public static readonly URL_MAP_GET_GEEK_NOTICE_DETAIL: string = buildUrl("moment/connect/geek/noticeDetail")
  //805 校招F1入口
  public static readonly URL_GEEK_LIVE_F1_GUIDE: string = buildUrl("zpgeek/app/live/recruit/bubble")
  /**
   * 获取小程序token
   * <p>
   * https://api.weizhipin.com/project/30/interface/api/455508
   */
  public static readonly URL_ZPPASSPORT_WX_GET_MINI_TOKEN: string = buildUrl("zppassport/wx/getMiniToken")
  public static readonly URL_EXCHANGE_REQUEST: string = buildUrl("zpchat/exchange/request")
  public static readonly URL_EXCHANGE_VIDEO_RESUME_REQUEST: string = buildUrl("zpchat/exchange/videoResume/request")
  public static readonly URL_EXCHANGE_ACCEPT_TEST: string = buildUrl("zpchat/exchange/testAccept")
  public static readonly URL_EXCHANGE_AUTH_TEST: string = buildUrl("zpchat/exchange/auth/test")
  public static readonly URL_EXCHANGE_ACCEPT: string = buildUrl("zpchat/exchange/accept")
  public static readonly URL_ZPCHAT_EXCHANGE_REJECT: string = buildUrl("zpchat/exchange/reject")
  public static readonly URL_ZPCHAT_EXCHANGE_ACCEPT_VIDEO_RESUME: string = buildUrl("zpchat/exchange/videoResume/accept")
  public static readonly URL_ZPCHAT_EXCHANGE_REJECT_VIDEO_RESUME: string = buildUrl("zpchat/exchange/videoResume/reject")
  /**
   * 人脉发现更多协议
   * http://api.kanzhun-inc.com/project/30/interface/api/91627
   */
  public static readonly URL_BOSS_GET_GROUP_CHAT_BAR: string = buildUrl("moment/connect/geek/getMoreLink")
  public static readonly URL_ZPCHAT_WX_REMIND: string = buildUrl("zpchat/wechat/save/wxRemind/setting")
  /**
   * 获取用户微信通知信息
   * http://api.kanzhun-inc.com/project/30/interface/api/91914
   */
  public static readonly URL_WX_NOTIFY_SETTING: string = buildUrl("zpchat/wechat/get/WxNotify/setting")
  /**
   * 判断是否关注公众号
   * https://api.weizhipin.com/project/477/interface/api/454353
   */
  public static readonly URL_WECHAT_HASSUBSCRIBED: string = buildUrl("zpchat/wechat/hasSubscribed")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/90850
   * 【兼职专区】职位列表
   */
  public static readonly URL_SEARCH_ZONE_JOB_LIST: string = buildUrl("zpgeek/app/search/topic/joblist")
  /**
   * 820
   * http://api.kanzhun-inc.com/project/30/interface/api/144248
   * 【职位】聚合品牌列表接口
   */
  public static readonly URL_AGGREGATION_BRAND_LIST: string = buildUrl("zpgeek/app/geek/aggregation/brandlist")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/145777
   * 获取牛人家庭住址&期望地址
   */
  public static readonly URL_GEEK_HOMEEXPECTADDRESS_QUERY: string = buildUrl("zpgeek/cvapp/geek/homeexpectaddress/query")
  public static readonly URL_CHAT_CANUSE: string = buildUrl("zpgeek/app/geek/videoresume/chat/canuse")
  public static readonly URL_VIDEO_RESUME_SEND: string = buildUrl("zpgeek/app/geek/videoresume/send")
  public static readonly URL_MOMENT_REPLAY_NOTICE: string = buildUrl("moment/connect/geek/replyNotice")
  /**
   * 806 【视频简历】查询视频简历模板
   * http://api.kanzhun-inc.com/project/30/interface/api/95477
   */
  public static readonly URL_GEEK_VIDEO_RESUME_TEMPLATE: string = buildUrl("zpgeek/cvapp/geek/videoresume/template/querylist")
  /**
   * 806 【视频简历】查询视频简历模板题库
   * http://api.kanzhun-inc.com/project/30/interface/api/95484
   */
  public static readonly URL_GEEK_VIDEO_RESUME_TEMPLATE_QUESTION: string = buildUrl("zpgeek/cvapp/geek/videoresume/templatequestion/querylist")
  /**
   * 806 【视频简历】查看视频简历播放信息
   * http://api.kanzhun-inc.com/project/30/interface/api/96632
   */
  public static readonly URL_GEEK_VIDEO_RESUME_PLAY_INFO: string = buildUrl("zpgeek/cvapp/geek/videoresume/playinfo")
  /**
   * 1018.2【C+B】简历作品优化 -【我的作品】播放作品视频
   */
  public static readonly URL_ZPGEEK_APP_RESUME_DESIGN_PLAYINFO: string = buildUrl("zpgeek/cvapp/resume/design/playinfo")
  /**
   * 806 【蓝领生态桶：扶贫】获取蓝领推荐职位数据
   * hhttp://api.kanzhun-inc.com/project/30/interface/api/96569
   */
  public static readonly URL_GEEK_BLUE_RECOMMEND_LIST: string = buildUrl("zpgeek/app/bluecollarexpect/recommend/list")
  /**
   * 806 【蓝领生态桶：扶贫】保存家乡
   * hhttp://api.kanzhun-inc.com/project/30/interface/api/96569
   */
  public static readonly URL_GEEK_BLUE_HOMETOWN_SAVE: string = buildUrl("zpgeek/cvapp/hometown/update")
  /**
   * 电话直拨筛选灰度
   * http://api.kanzhun-inc.com/project/30/interface/api/100048
   */
  public static readonly URL_GET_DIRECT_CALL_FILTER: string = buildUrl("zpitem/directCall/getDirectCallFilter")
  /**
   * 【特征标签】F1特征填写入口
   * http://api.kanzhun-inc.com/project/30/interface/api/100923
   */
  public static readonly URL_GET_CHARACTER_LABEL_GEEK_ENTRY: string = buildUrl("zpgeek/app/geek/trait/tip")
  /**
   * 【特征标签】F1特征问题列表
   * http://api.kanzhun-inc.com/project/30/interface/api/100923
   */
  public static readonly URL_GET_CHARACTER_LABEL_QUETION_LIST: string = buildUrl("zpgeek/app/geek/trait/questions")
  /**
   * 关闭F1banneer
   * http://api.kanzhun-inc.com/project/30/interface/api/175283
   */
  public static readonly URL_GET_F1_BANNER_CLOSE: string = buildUrl("zpgeek/app/f1/banner/close")
  /**
   * 关闭负反馈的卡片
   */
  public static readonly URL_GET_F1_FEED_BACK_CLOSE: string = buildUrl("zpgeek/app/f1/negativefeedback/banner/close")
  /**
   * 点击负反馈后 请求该接口
   */
  public static readonly URL_GET_F1_FEED_BACK_CARD: string = buildUrl("zpgeek/app/f1/negativefeedback/banner/query")
  /**
   * 商圈保存
   */
  public static readonly URL_GET_F1_DISTRICT_SAVE: string = buildUrl("zpgeek/cvapp/geek/businessdistrict/save")
  /**
   * 【F1】推荐职位列表推荐主题标签词
   * https://api.weizhipin.com/project/30/interface/api/502930
   */

  public static readonly URL_APP_RMCD_TOPIC_WORDLIST: string = buildUrl("zpgeek/app/recommend/topic/wordlist")
  /**
   * 【F1】开聊职位相似职位列表
   * https://api.weizhipin.com/project/30/interface/api/506923
   */

  public static readonly URL_APP_CHAT_SIMILAR_JOBLIST: string = buildUrl("zpgeek/app/chat/similar/joblist/query")
  /**
   * 【F1】开聊职位相似职位列表
   * https://api.weizhipin.com/project/30/interface/api/509896
   */

  public static readonly URL_APP_F1_CHAT_RCMD_QUERY: string = buildUrl("zpgeek/app/f1/chat/recommend/query")
  /**
   * 【F1】投诉后高质量职位推荐查询
   * https://api.weizhipin.com/project/30/interface/api/538255
   */
  public static readonly URL_APP_F1_REPORT_RCMD_QUERY: string = buildUrl("zpgeek/app/f1/highquality/rcdjob/query")
  /**
   * 【特征标签】F1特征问题列表 - 提交选项
   * http://api.kanzhun-inc.com/project/30/interface/api/145023
   */
  public static readonly URL_GET_CHARACTER_LABEL_POST: string = buildUrl("zpgeek/app/geek/trait/save")
  /**
   * 【特征标签】F1特征 - 关闭banner
   * http://api.kanzhun-inc.com/project/30/interface/api/145022
   */
  public static readonly URL_GET_CHARACTER_LABEL_CLOSE: string = buildUrl("zpgeek/app/geek/trait/tip/close")
  /**
   * 删除筛选记忆
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/104129
   */
  public static readonly URL_BOSS_RECOMMEND_SCREEN_MEMORY_DELETE: string = buildUrl("zpblock/rcd/filters/memory/del")
  /**
   * 急聘拨打boss电话预处理
   * http://api.kanzhun-inc.com/project/30/interface/api/104941
   */
  public static readonly URL_QUICK_TOP_PRE_USE: string = buildUrl("zpitem/quickTop/phone/preUse")
  /**
   * 急聘拨打boss电话
   * http://api.kanzhun-inc.com/project/30/interface/api/104969
   */
  public static readonly URL_QUICK_TOP_USE: string = buildUrl("zpitem/quickTop/phone/use")
  /**
   * 获取道具配置
   * http://api.kanzhun-inc.com/project/30/interface/api/105165
   */
  public static readonly URL_ITEM_CONFIG_GET: string = buildUrl("zpitem/item/config/get")
  /**
   * 猜你想看F2展示
   */
  public static readonly URL_ZPRELATION_INTERACTION_GUESSYOURLIKES: string = buildUrl("zprelation/interaction/guessYourLikes")
  /**
   * 升级畅聊版的阻断页面
   */
  public static readonly URL_GET_NORMAL_JOB_UPGRADE: string = buildUrl("zpblock/page/normal/job/upgrade")
  /**
   * 获取订阅列表数据
   * http://api.kanzhun-inc.com/project/30/interface/api/105291
   */
  public static readonly URL_GET_ITEM_SUBSCRIBE_LIST: string = buildUrl("zpitem/subscribe/list")
  /**
   * 获取订阅牛人tab
   * http://api.kanzhun-inc.com/project/30/interface/api/105109
   */
  public static readonly URL_GET_ITEM_SUBSCRIBE_TABS: string = buildUrl("zpitem/subscribe/tabs")
  /**
   * 获取订阅牛人条件
   * http://api.kanzhun-inc.com/project/30/interface/api/105179
   */
  public static readonly URL_SUBSCRIBE_CONDITIONS: string = buildUrl("zpitem/subscribe/conditions")
  /**
   * 添加订阅条件
   * http://api.kanzhun-inc.com/project/30/interface/api/105186
   */
  public static readonly URL_SUBSCRIBE_SAVE: string = buildUrl("zpitem/subscribe/save")
  /**
   * 删除订阅条件
   * http://api.kanzhun-inc.com/project/30/interface/api/105207
   */
  public static readonly URL_SUBSCRIBE_DELETE: string = buildUrl("zpitem/subscribe/del")
  /**
   * 获取关键词
   * http://api.kanzhun-inc.com/project/30/interface/api/106173
   */
  public static readonly URL_SUBSCRIBE_GET_FILTER_CONDITIONS: string = buildUrl("zpitem/subscribe/getFilterConditions")
  public static readonly URL_ADVANCED_SEARCH_CLOSE_AD: string = buildUrl("zpitem/boss/closeAdRecord")
  /**
   * 808 http://api.kanzhun-inc.com/project/30/interface/api/105893
   * B 看c 是否有权限
   */
  public static readonly URL_CHECK_BOSS_OPEN_GEEK_HOMEPAGE: string = buildUrl("moment/get/checkGeekMarkBoss")
  /**
   * 公司主页工作体验引导 点击
   */
  public static readonly URL_COMPANY_WORK_GUIDE_CLICK: string = buildUrl("zpboss/app/brandWorkTasteCopyGuide/incClickNum")
  /**
   * 获取群屏蔽信息
   */
  public static readonly URL_ZPCHAT_GROUP_GET_BLACKUSER: string = buildUrl("zpchat/group/blackUser/get")
  public static readonly URL_ZPCHAT_GROUP_SAVE_BLACKUSER: string = buildUrl("zpchat/group/blackUser/save")
  public static readonly URL_ZPCHAT_GROUP_JOB_LIST: string = buildUrl("zpchat/group/getJobList")
  public static readonly URL_ZPCHAT_GROUP_SEND_JOBCARD: string = buildUrl("zpchat/group/sendJobCard")
  public static readonly URL_ZPCHAT_GROUP_JOIN_CENTRA: string = buildUrl("zpchat/group/joinCentraGroup")
  public static readonly URL_ZPCHAT_GROUP_QUIT_CENTRA: string = buildUrl("zpchat/group/quitCentraGroup")
  public static readonly URL_ZPCHAT_GROUP_CENTRA_LIST: string = buildUrl("zpchat/group/getCentraGroupList")
  public static readonly URL_ZPCHAT_GROUP_CENTRA: string = buildUrl("zpchat/group/getCentraGroup")
  public static readonly URL_ZPCHAT_GROUP_CENTRA_SYNC_LIST: string = buildUrl("zpchat/group/getCentraGroupSyncList")
  public static readonly URL_ZPCHAT_GROUP_MESSAGE_LIST: string = buildUrl("zpchat/group/getMessageList")
  public static readonly URL_ZPCHAT_GROUP_READ_MESSAG: string = buildUrl("zpchat/group/readMessages")
  /**
   * 1109.51 置顶分享的职位
   * https://api.weizhipin.com/project/30/interface/api/553222
   */
  public static readonly URL_GROUP_CHAT_TOP_JOB: string = buildUrl("zpchat/group/topShareJob")
  public static readonly URL_ZPITEM_BOSS_GETCARD: string = buildUrl("zpitem/boss/getF2Card")
  /**
   * 获取用户正在直播的直播间信息
   * http://api.kanzhun-inc.com/project/30/interface/api/109098
   */
  public static readonly URL_GET_BOSS_LIVING_ROOM_INFO: string = buildUrl("zpboss/app/liveRecruit/bossLivingRoomInfo")
  /**
   * 牛人一键发送电话
   * http://api.kanzhun-inc.com/project/30/interface/api/109252
   */
  public static readonly URL_GEEK_SEND_PHONE: string = buildUrl("zpitem/geek/send/phone")
  /**
   * 【809】设置用户可请教开关
   * http://api.kanzhun-inc.com/project/30/interface/api/108301
   */
  public static readonly URL_MOMENT_GET_GEEK_SET_ASK: string = buildUrl("moment/connect/geek/setCanAsk")
  /**
   * 【809】查询用户可请教开关
   * http://api.kanzhun-inc.com/project/30/interface/api/108294
   */
  public static readonly URL_MOMENT_GET_GEEK_CAN_ASK: string = buildUrl("moment/connect/geek/canAsk")
  /**
   * https://api.weizhipin.com/project/30/interface/api/286605
   */
  public static readonly URL_ZPGEEK_APP_RESUME_SECURITY_STATUS_UPDATE: string = buildUrl("zpgeek/cvapp/resume/security/status/update")
  /**
   * 高亮或者取消高亮接口
   * http://api.kanzhun-inc.com/project/30/interface/api/108878
   */
  public static readonly URL_APP_GEEK_HIGHLIGHT: string = buildUrl("zpjob/view/highlight/resume")
  /**
   * 设置上线提醒
   * http://api.kanzhun-inc.com/project/30/interface/api/110795
   */
  public static readonly URL_ONLINE_REMIND_SETUP: string = buildUrl("zpitem/online/setRemind")
  /**
   * https://api.weizhipin.com/project/30/interface/api/296307
   */
  public static readonly URL_GEEK_CALL_REPLY: string = buildUrl("zpitem/search/hunter/geekCallReply")
  /**
   * 发送验证码验证邮箱
   * http://api.kanzhun-inc.com/project/30/interface/api/111039
   */
  public static readonly URL_BOSS_EMAIL_SEND_CODE: string = buildUrl("zpboss/app/boss/email/sendCode")
  /**
   * 获取极验初始化数据
   * http://api.kanzhun-inc.com/project/30/interface/api/111199
   */
  public static readonly URL_BOSS_EMAIL_START_GEET_CAPTCHA: string = buildUrl("zpboss/app/boss/email/startGeetCaptcha")
  /**
   * BOSS主页工作环境
   * http://api.kanzhun-inc.com/project/30/interface/api/110987
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_HOMEPAGE: string = buildUrl("moment/work/enviroment/home")
  /**
   * BOSS主页工作环境 - 保存视频
   * http://api.kanzhun-inc.com/project/30/interface/api/111099
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_SAVE_VIDEO: string = buildUrl("moment/work/enviroment/saveVideo")
  /**
   * 工作环境标签列表
   * http://api.weizhipin.com/project/30/interface/api/209740
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_TAGS: string = buildUrl("moment/work/enviroment/tags")
  /**
   * 工作环境示例图片视频
   * http://api.weizhipin.com/project/30/interface/api/209748
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_PICVIDEOEXAMPLES: string = buildUrl("moment/work/enviroment/picVideoExamples")
  /**
   * BOSS主页工作环境 - 观看视频
   * http://api.kanzhun-inc.com/project/30/interface/api/111279
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_WATCH_VIDEO: string = buildUrl("moment/work/enviroment/watchVideo")
  /**
   * BOSS主页工作环境 - 删除图片
   * http://api.kanzhun-inc.com/project/30/interface/api/111063
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_DELETE_PIC: string = buildUrl("moment/work/enviroment/deletePic")
  /**
   * BOSS主页工作环境 - 保存图片
   * http://api.kanzhun-inc.com/project/30/interface/api/111071
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_SAVE_PIC: string = buildUrl("moment/work/enviroment/savePic")
  /**
   * BOSS主页工作环境 - 删除视频
   * http://api.kanzhun-inc.com/project/30/interface/api/111271
   */
  public static readonly URL_BOSS_WORK_ENVIRONMENT_DELETE_VIDEO: string = buildUrl("moment/work/enviroment/deleteVideo")
  /**
   * 810 大蓝 达成补贴
   * http://api.kanzhun-inc.com/project/30/interface/api/111703
   */
  public static readonly URL_ZPRELATION_GEEK_SUBSIDY: string = buildUrl("zprelation/geek/subsidy")
  /**
   * 810 道具活动弹窗卡片
   * http://api.kanzhun-inc.com/project/30/interface/api/113263
   */
  public static readonly URL_BOSS_ITEM_COLLECTION_WINDOWS: string = buildUrl("zpitem/activity/item/windows")
  /**
   * 810 活动窗口响应接口
   * http://api.kanzhun-inc.com/project/30/interface/api/113267
   */
  public static readonly URL_BOSS_ITEM_COLLECTION_CLOSE: string = buildUrl("zpitem/activity/item/windows/onclick")
  /**
   * 协议更新弹窗接口
   * 810 http://api.kanzhun-inc.com/project/30/interface/api/113571
   */
  public static readonly URL_GET_AGREEMENT_UPDATE_NOTICE: string = buildUrl("zpuser/agreement/update/notice")
  public static readonly URL_ZP_JOB_WELFARE_CHAT_GUIDE: string = buildUrl("zpjob/job/welfare/chat/guide")
  /**
   * C端已读不回
   * 811 http://api.kanzhun-inc.com/project/30/interface/api/113459
   */
  public static readonly URL_GEEK_CHAT_TYPE_QUERY: string = buildUrl("zpgeek/app/geek/chat/type/query")
  /**
   * 811 公司名校验耶路撒冷数据
   * http://api.kanzhun-inc.com/project/30/interface/api/113751
   */
  public static readonly URL_CHECK_COMPANY_JERUSALEM: string = buildUrl("zpboss/app/company/checkJerusalem")
  /**
   * 811 Geek-获取品牌列表
   * http://api.kanzhun-inc.com/project/30/interface/api/113755
   */
  public static readonly URL_GEEK_BRAND_LIST_ALL: string = buildUrl("zpgeek/app/brand/listAll")
  /**
   * 811 Geek-获取品牌推荐列表
   * http://************:8088/project/30/interface/api/113743
   * http://api.kanzhun-inc.com/project/30/interface/api/143118
   */
  public static readonly URL_GEEK_BRAND_RCD_LIST: string = buildUrl("zpgeek/app/brand/discovery/list")
  /**
   * 个人主页 分享接口
   */
  public static readonly URL_BOSS_GEEK_HOMEPAGE_SHARE: string = buildUrl("moment/user/mostUsed/homeShareInfo")
  public static readonly URL_COM_HOMEPAGE_SHARE: string = buildUrl("zpgeek/app/brand/shareminiapp")
  public static readonly URL_COM_HOMEPAGE_SHARE_BOSS: string = buildUrl("zpboss/app/brandInfo/shareBrandInfo")
  /**
   * 811.100
   * http://api.kanzhun-inc.com/project/30/interface/api/115218
   */
  public static readonly URL_GEEK_INTERMEDIARY_EVALUATION_LIST_V2: string = buildUrl("zpgeek/app/intermediary/evaluation/listv2")
  /**
   * 812 获取常用设备
   * http://api.kanzhun-inc.com/project/30/interface/api/123960
   */
  public static readonly URL_GET_COMMON_DEVICE: string = buildUrl("zpuser/get/commonDevice")
  /**
   * 812 移除常用设备
   * http://api.kanzhun-inc.com/project/30/interface/api/123966
   */
  public static readonly URL_REMOVE_COMMON_DEVICE: string = buildUrl("zpuser/remove/commonDevice")
  /**
   * 812
   * http://api.kanzhun-inc.com/project/30/interface/api/145346
   */
  public static readonly URL_GET_LIVE_AD_CARD_TIPS: string = buildUrl("zpgeek/app/live/recruit/banner")
  /**
   * 812.207
   * http://api.kanzhun-inc.com/project/30/interface/api/123888
   */
  public static readonly URL_GEEK_BLUEGEEK_RECOMMEND_LIST: string = buildUrl("zpgeek/app/bluegeek/customjob/recommend/joblist")
  /**
   * 812 常用设备阻断拉取
   * http://api.kanzhun-inc.com/project/30/interface/api/124152
   */
  public static readonly URL_LOGIN_DEVICE_CONFIRM: string = buildUrl("zpuser/get/commonDeviceConfirm")
  /**
   * 812 常用设备阻断页上报
   * http://api.kanzhun-inc.com/project/30/interface/api/124158
   */
  public static readonly URL_LOGIN_DEVICE_REPORT: string = buildUrl("zppassport/commonDevice/login")
  /**
   * 817 阻断页上报埋点
   * http://api.kanzhun-inc.com/project/30/interface/api/145762
   */
  public static readonly URL_LOGIN_DEVICE_REPORT_ACTION: string = buildUrl("zppassport/commonDevice/reportAction")
  /**
   * 公司订阅-获取黄条
   * http://api.kanzhun-inc.com/project/30/interface/api/135720
   */
  public static readonly URL_GEEK_VIP_GET_SUBSCRIBE_YELLOW: string = buildUrl("zpitem/geekVip/getSubscribeYellow")
  /**
   * 公司订阅-关闭黄条
   * http://api.kanzhun-inc.com/project/30/interface/api/135726
   */
  public static readonly URL_GEEK_VIP_CLOSE_SUBSCRIBE_YELLOW: string = buildUrl("zpitem/geekVip/closeSubscribeYellow")
  /**
   * 公司订阅-订阅
   * http://api.kanzhun-inc.com/project/30/interface/api/135732
   */
  public static readonly URL_GEEK_VIP_SUBSCRIBE: string = buildUrl("zpitem/geekVip/subscribe")
  public static readonly URL_USER_SETTING_PHONE: string = buildUrl("zpitem/user/setting/phone")
  /**
   * 经纪人用户等级F1提示
   * http://api.kanzhun-inc.com/project/30/interface/api/136410
   */
  public static readonly URL_GET_AGENT_LEVEL: string = buildUrl("zpboss/app/agent/level/f1")
  /**
   * 添加/更新公司视频
   * http://api.kanzhun-inc.com/project/30/interface/api/140460
   */
  public static readonly URL_BRAND_COMPLETE_VIDEO_ADD_OR_UPDATE: string = buildUrl("zpCompany/brandComplete/video/addOrUpdate")
  /**
   * 删除公司视频
   * http://api.kanzhun-inc.com/project/30/interface/api/140472
   */
  public static readonly URL_BRAND_COMPLETE_VIDEO_DELETE: string = buildUrl("zpCompany/brandComplete/video/delete")
  /**
   * 获取公司视频列表
   * http://api.kanzhun-inc.com/project/30/interface/api/140484
   */
  public static readonly URL_BRAND_COMPLETE_VIDEO_GET_LIST: string = buildUrl("zpCompany/brandComplete/video/getList")
  /**
   * 播放品牌视频增加播放量（boss,geek通用）
   * http://api.kanzhun-inc.com/project/30/interface/api/140526
   */
  public static readonly URL_BRAND_INFO_PLAY_VIDEO: string = buildUrl("zpCompany/brandInfo/playVideo")
  /**
   * 环境视频接口
   */
  public static readonly URL_BRAND_ENVIRONMENT_PLAY_VIDEO: string = buildUrl("moment/work/enviroment/playVideo")
  public static readonly URL_BRAND_MOST_FOCUS: string = buildUrl("moment/geek/brand/getFocusBrandList")
  public static readonly URL_COM_TOP_COLLECT: string = buildUrl("moment/geek/brand/getRcdBrandCollections")
  public static readonly URL_COM_COLLECT_LIST: string = buildUrl("moment/geek/brand/brandCollections/list")
  public static readonly URL_COM_COLLECT_DETAIL_LIST: string = buildUrl("moment/geek/brand/brandCollection/detail")
  public static readonly URL_USER_MARK_BATCH_REJECT: string = buildUrl("zprelation/userMark/batchReject")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/143488
   * 搜索底导航-我的订阅
   */
  public static readonly URL_ADVANCED_SEARCH_SUBSCRIBE_LIST: string = buildUrl("zpitem/subscribe/getSubscribeList")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/143511
   * 搜索底导航-是否已有订阅
   */
  public static readonly URL_ADVANCED_SEARCH_CHECK_HAS_SUBSCRIBE: string = buildUrl("zpitem/subscribe/hasSubscribe")
  /**
   * 【F1】+号期望管理入口引导红点气泡
   * https://api.weizhipin.com/project/30/interface/api/226818
   */
  public static readonly URL_ZPGEEK_APP_GEEK_EXPECTPOSITION_SUGGEST_REDDOT_QUERY: string = buildUrl("zpgeek/app/geek/expectposition/suggest/reddot/query")
  /**
   * 【F3】查询我的在线简历引导信息
   * https://api.weizhipin.com/project/30/interface/api/261459
   */
  public static readonly URL_ZPGEEK_APP_F3_TIP_QUERY: string = buildUrl("zpgeek/cvapp/f3/tip/query")
  /**
   * 【F1】+号期望管理入口引导红点气泡记录展示频次
   * https://api.weizhipin.com/project/30/interface/api/226836
   */
  public static readonly URL_ZPGEEK_APP_GEEK_EXPECTPOSITION_SUGGEST_REDDOT_RECORD: string = buildUrl("zpgeek/app/geek/expectposition/suggest/reddot/record")
  /**
   * 【工作经历】技能标签
   * http://api.kanzhun-inc.com/project/30/interface/api/143459
   */
  public static readonly URL_ZPGEEK_APP_GEEK_WORKEXP_WORKEMPHASIS: string = buildUrl("zpgeek/cvapp/geek/workexp/workemphasis")
  /**
   * 【专业技能】查询推荐技能词列表
   * https://api.weizhipin.com/project/30/interface/api/604379
   */
  public static readonly URL_ZPGEEK_CVAPP_RESUME_PROFESSIONAL_SKILL_RECOMMEND: string = buildUrl("zpgeek/cvapp/resume/professional/skill/recommend")
  /**
   * 获取编辑logo警告信息
   * http://api.kanzhun-inc.com/project/30/interface/api/143592
   */
  public static readonly URL_BOSS_GET_BRAND_LOGO_WARN: string = buildUrl("zpboss/app/brandComplete/getBrandLogoWarn")
  /**
   * 815 电话直拨开启法律文案提示
   * http://api.weizhipin.com/project/30/interface/api/143358
   */
  public static readonly URL_ZPITEM_DIRECT_CALL_LAW_TEXT: string = buildUrl("zpitem/directCall/law/notice")
  /**
   * 发送评测消息
   * http://api.kanzhun-inc.com/project/20/interface/api/143689
   */
  public static readonly URL_BOSS_QUESTION_INVITE_ANSWER: string = buildUrl("moment/quiz/message/inviteAnswer")
  /**
   * 816 用户信息监测
   * http://api.weizhipin.com/project/30/interface/api/144273
   */
  public static readonly URL_USER_INFO_CHECK: string = buildUrl("zpuser/user/check")
  /**
   * 816 【在线简历】查询简历诊断信息
   * http://api.weizhipin.com/project/30/interface/api/144170
   */
  public static readonly URL_ZPGEEK_RESUME_QUERY_SUGGEST_GARBAGE: string = buildUrl("zpgeek/cvapp/geek/resume/garbage/suggest/query")
  public static readonly URL_CHAT_JOB_POI_GET: string = buildUrl("zpjob/chatJob/poi/get")
  /**
   * 牛人一键发送微信
   * http://api.kanzhun-inc.com/project/30/interface/api/144692
   */
  public static readonly URL_ONE_KEY_SEND_WE_CHAT: string = buildUrl("zpitem/geek/send/wechat")
  /**
   * 牛人直通卡下发优惠券接口
   * http://api.kanzhun-inc.com/project/30/interface/api/145106
   */
  public static readonly URL_GET_JOB_TOP_SEND_ACTIVITY: string = buildUrl("zpitem/jobtop/sendActivity")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/145048
   * 电话交换助手-了解更多
   */
  public static readonly URL_BOSS_JOB_PHONE_EXCHANGE_SWTICH_DETAIL: string = buildUrl("zpboss/app/job/phoneExchangeSwitch/detail")
  /**
   * 【在线简历】保存邮箱
   * http://api.kanzhun-inc.com/project/30/interface/api/144999
   */
  public static readonly URL_GEEK_INPUT_EMAIL: string = buildUrl("zpgeek/cvapp/geek/email/save")
  public static readonly URL_GEEK_EVALUATION_FEEDBACK: string = buildUrl("zpgeek/app/boss/evaluation/feedback/query")
  public static readonly URL_GEEK_EVALUATION_SAVE: string = buildUrl("zpgeek/app/boss/evaluation/feedback/save")
  public static readonly URL_GEEK_EVALUATION_ATTACH_SAVE: string = buildUrl("zpgeek/app/intermediary/evaluation/attach/save")
  /**
   * 搜畅底导航提示
   * http://api.kanzhun-inc.com/project/30/interface/api/145832
   */
  public static readonly URL_GET_ITEM_CONFIG_SEARCH_TIP: string = buildUrl("zpitem/item/config/getSearchTip")
  /**
   * 搜畅中间页提示
   * http://api.kanzhun-inc.com/project/30/interface/api/145833
   */
  public static readonly URL_GET_ITEM_CONFIG_SEARCH_TIP_DETAIL: string = buildUrl("zpitem/boss/search/getSearchTipDetail")
  /**
   * 8.18 设置薪资展示开关
   * http://api.kanzhun-inc.com/project/30/interface/api/145742
   */
  public static readonly URL_BOSS_PROFILE_SETTING_VALUE: string = buildUrl("zpjob/boss/profile/settingvalue")
  /**
   * 8.18 搜索畅聊卡批量使用成功页(蓝领)
   * http://api.kanzhun-inc.com/project/30/interface/api/146112
   */
  public static readonly URL_ADVANCED_SEARCH_CARD_USE_SUCCESS: string = buildUrl("zpitem/searchChatCard/batchUseSuccess")
  /**
   * 8.17 获取用户ticket（t2）
   * http://api.kanzhun-inc.com/project/30/interface/api/145834
   */
  public static readonly URL_USER_GET_TICKET: string = buildUrl("zppassport/user/getTicket")
  /**
   * 道具f1弹窗统一控制
   * http://api.kanzhun-inc.com/project/30/interface/api/146707
   */
  public static readonly URL_BOSS_ITEM_F1_POP_WINDOW: string = buildUrl("zpitem/pop/f1window")
  /**
   * f1弹窗响应
   * http://api.kanzhun-inc.com/project/30/interface/api/146709
   */
  public static readonly URL_BOSS_ITEM_F1_POP_WINDOW_CLICK: string = buildUrl("zpitem/pop/f1window/onclick")
  /**
   * 道具节f1牛人弹窗(优先级较低)
   * http://api.kanzhun-inc.com/project/30/interface/api/146777
   */
  public static readonly URL_GEEK_ITEM_F1_POP_WINDOW: string = buildUrl("zpitem/pop/geek/f1window")
  /**
   * 道具节牛人弹窗响应
   * http://api.kanzhun-inc.com/project/30/interface/api/146779
   */
  public static readonly URL_GEEK_ITEM_F1_POP_WINDOW_CLICK: string = buildUrl("zpitem/pop/geek/f1window/onclick")
  /**
   * 【高搜】提交正负反馈
   * http://api.kanzhun-inc.com/project/30/interface/api/147276
   */
  public static readonly URL_ADVANCED_SEARCH_FEEDBACK_SUBMIT: string = buildUrl("zpitem/boss/search/feedback/submit")
  /**
   * 【高搜】正负反馈映射关系列表
   * http://api.kanzhun-inc.com/project/30/interface/api/147278
   */
  public static readonly URL_GET_ADVANCED_SEARCH_FEEDBACK: string = buildUrl("zpitem/boss/search/feedback/code")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/147426
   */
  public static readonly URL_FAST_REPLAY_VIDEO_DELETE: string = buildUrl("zpchat/fastreply/video/delete")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/147411
   */
  public static readonly URL_FAST_REPLAY_VIDEO_LIST: string = buildUrl("zpchat/fastreply/video/list")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/147427
   */
  public static readonly URL_FAST_REPLAY_VIDEO_SEND: string = buildUrl("zpchat/fastreply/video/send")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/147429
   */
  public static readonly URL_FAST_REPLAY_VIDEO_GET_URL: string = buildUrl("zpchat/fastreply/video/getUrl")
  /**
   * 搜索中间页推荐列表
   * https://api.weizhipin.com/project/30/interface/api/148503
   */
  public static readonly URL_GET_BOSS_RECOMMEND_MIDDLE_SEARCH: string = buildUrl("zpitem/boss/searchRecommend")
  /**
   * 搜索中间页新牛人推荐列表
   * https://api.weizhipin.com/project/30/interface/api/559972
   */
  public static readonly URL_GET_MIDDLE_PAGE_NEW_GEEK_LIST: string = buildUrl("zpitem/boss/searchRecommendNewGeek")
  /**
   * 代付转发卡片信息
   * http://api.weizhipin.com/project/30/interface/api/217374
   */
  public static readonly URL_ZPP_APP_AGENT_BZB_ORDER_CARD: string = buildUrl("zpp/app/agentBzbOrder/card")
  /**
   * App内订单转发
   * http://api.weizhipin.com/project/30/interface/api/218074
   */
  public static readonly URL_MATE_SHARE_AGENT_BZB_ORDER_SEND_MESSAGE: string = buildUrl("zpp/app/agentBzbOrder/sendMessage")
  /**
   * 获取微信分享信息
   * http://api.kanzhun-inc.com/project/30/interface/api/148597
   */
  public static readonly URL_GET_WECHAT_ORDER_SHARE_INFO: string = buildUrl("zpblock/order/share/wx/info")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/149466
   */
  public static readonly URL_LIVE_RECRUIT_GEEK_CARD: string = buildUrl("zpboss/app/liveRecruit/interact/geekCardList")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/149543
   */
  public static readonly URL_LIVE_RECRUIT_RECORD_LIST: string = buildUrl("zpboss/app/liveRecruit/interact/bossLiveRecordList")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/149557
   */
  public static readonly URL_LIVE_JOB_LIST: string = buildUrl("zpboss/app/liveRecruit/interact/bossLiveJobList")
  /**
   * 创建付款单
   * http://api.kanzhun-inc.com/project/30/interface/api/150187
   */
  public static readonly URL_MOMENT_ORDER_CREATE: string = buildUrl("moment/circle/order/create")
  /**
   * 直猎邦Pro个人版首页弹窗
   * http://api.weizhipin.com/project/30/interface/api/153498
   */
  public static readonly URL_GET_HUNTER_PERSONAL_HOME_POPUP: string = buildUrl("zpboss/app/hunterPersonal/homePopup")
  /**
   * http://api.kanzhun-inc.com/project/30/interface/api/153673
   */
  public static readonly URL_FAST_REPLAY_VIDEO_CHAT_CONTENTS: string = buildUrl("zpchat/fastreply/video/chatContents")
  /**
   * https://api.weizhipin.com/project/2535/interface/api/556030
   */
  public static readonly URL_PUSHENGINE_CONTENT_TIP: string = buildUrl("pushengine/contentTip")
  /**
   * 更新订阅名称
   * http://api.kanzhun-inc.com/project/30/interface/api/153890
   */
  public static readonly URL_SUBSCRIBE_NAME_EDIT: string = buildUrl("zpitem/subscribe/editSubName")
  /**
   * 902搜索畅聊卡使用前赠送校验
   * http://api.kanzhun-inc.com/project/30/interface/api/153022
   */
  public static readonly URL_ZP_ITEM_SEARCH_CHAT_CARD_PRE_USE: string = buildUrl("zpitem/searchChatCard/preUse")
  public static readonly URL_BOSS_INVITE_FEEDBACK_SUBMIT: string = buildUrl("zpitem/boss/invite/feedback/submit")
  /**
   * 903 付费引导-收藏引导搜畅付费
   * https://api.weizhipin.com/project/30/interface/api/159644
   */
  public static readonly URL_GET_SEARCH_CARD_GUIDE_PAY_DIALOG: string = buildUrl("zpitem/business/guide/use/searchCard")
  /**
   * 903 个体工商户校验并获取限制品牌名称
   * http://api.kanzhun-inc.com/project/30/interface/api/159840
   */
  public static readonly URL_CHECK_INDIVIDUAL_BRAND: string = buildUrl("zpboss/app/brandCom/join/individualLimitBrand")
  /**
   * http://api.weizhipin.com/project/30/interface/api/206985
   * 直播曝光量订单创建
   */
  public static readonly URL_LIVE_PRE_ORDER_CREATE: string = buildUrl("zpboss/app/liveRecruit/order/exposure/createV2")
  /**
   * 【1015-新增】直播中购买流量订单创建
   * https://api.weizhipin.com/project/30/interface/api/445169
   */
  public static readonly URL_BOSS_LIVE_FLOW_CREATE: string = buildUrl("zpboss/app/liveRecruit/order/living/flow/create")
  /**
   * 【1103-新增】付费直播直通卡订单创建
   * https://api.weizhipin.com/project/30/interface/api/512500
   */
  public static readonly URL_BOSS_PASS_THROUGH_CARD_ORDER_CREATE: string = buildUrl("zpboss/app/liveRecruit/passThrough/card/order/create")
  /**
   * 903.69【商业】曝光加强迭代
   * http://api.kanzhun-inc.com/project/30/interface/api/158349
   */
  public static readonly URL_ZP_BLOCK_GIFT_RECEIVE: string = buildUrl("zpblock/gift/receive")
  /**
   * 903 关闭VIP黄条引导
   * http://api.kanzhun-inc.com/project/30/interface/api/161758
   */
  public static readonly URL_VIP_GUIDE_CLOSE: string = buildUrl("zpblock/vip/guide/close")
  /**
   * 904.45【商业】引导收藏
   * http://api.kanzhun-inc.com/project/30/interface/api/164229
   */
  public static readonly URL_ZPITEM_BUSINESS_GUIDE_COLLECT: string = buildUrl("zpitem/business/guide/collect")
  /**
   * 【工作经历】工作经历保存前校验
   * https://api.weizhipin.com/project/30/interface/api/164376
   */
  public static readonly URL_GEEK_WORK_EXP_PRE_CHECK: string = buildUrl("zpgeek/cvapp/geek/workexp/precheck")
  /**
   * 1010.6【牛人简历】项目经历保存前校验
   * https://api.weizhipin.com/project/30/interface/api/355619
   */
  public static readonly URL_GEEK_PROJECT_EXP_PRE_CHECK: string = buildUrl("zpgeek/cvapp/geek/projexp/precheck")
  /**
   * 1010.6【牛人简历】个人优势保存前校验
   * https://api.weizhipin.com/project/30/interface/api/355635
   */
  public static readonly URL_GEEK_ADVANTAGE_PRE_CHECK: string = buildUrl("zpgeek/cvapp/resume/userdesc/precheck")
  /**
   * 904 F2推荐相似牛人
   * http://api.kanzhun-inc.com/project/30/interface/api/167057
   */
  public static readonly URL_GET_INTERACT_SIMILAR_GEEK_LIST: string = buildUrl("zprelation/interaction/geekSimilar")
  /**
   * 905 待开放职位列表引导接口
   * http://api.kanzhun-inc.com/project/30/interface/api/175067
   */
  public static readonly URL_GET_F2_JOB_PRE_GUIDE: string = buildUrl("zpjob/job/pre/guide")
  /**
   * 905 待开放职位引导关闭接口
   * http://api.kanzhun-inc.com/project/30/interface/api/175069
   */
  public static readonly URL_CLOSE_F2_JOB_PRE_GUIDE: string = buildUrl("zpjob/job/pre/guide/close")
  /**
   * 905 boss点击关闭F1道具场景卡片的按钮
   */
  public static readonly URL_CLOSE_ITEM_ADVERTISE_CARD: string = buildUrl("zpitem/click/close")
  /**
   * 905.48 道具-使用成功相似牛人列表
   * http://api.kanzhun-inc.com/project/30/interface/api/175395
   */
  public static readonly URL_ZPITEM_SEARCH_CHAT_CARD_SIMILAR_GEEK: string = buildUrl("zpitem/searchChatCard/similarGeek")
  /**
   * 1008.235【商业】精选开聊赠送高达成牛人
   * https://api.weizhipin.com/project/30/interface/api/323703
   * 注：精选赠送下线了
   */
  public static readonly URL_ZPITEM_REFINEDGEEK_FREE_GEEK_LIST: string = buildUrl("zpitem/refinedGeek/free/geek/list")
  /**
   * 917.232【商业】搜畅达成单价降低
   * http://api.weizhipin.com/project/30/interface/api/215438
   */
  public static readonly URL_ZPITEM_SEARCH_CHAT_CARD_GIVING_GEEK_LIST: string = buildUrl("zpitem/searchChatCard/giving/geek/list")
  /**
   * 917.232【商业】赠送牛人开聊
   * http://api.weizhipin.com/project/30/interface/api/215440
   */
  public static readonly URL_ZPITEM_SEARCH_CHAT_CARD_GIVING_GEEK_CHAT: string = buildUrl("zpitem/searchChatCard/giving/geek/chat")
  /**
   * 915 搜畅相关导航引导关闭
   * http://api.weizhipin.com/project/30/interface/api/208633
   */
  public static readonly URL_ZPITEM_SEARCHCHATCARD_GUIDE_CLOSE: string = buildUrl("zpitem/searchChatCard/guide/close")
  /**
   * 906 获取当月可更换头像的剩余次数，头像
   * http://api.kanzhun-inc.com/project/30/interface/api/177273
   */
  public static readonly URL_GET_USER_AVATAR_LEFT_COUNT: string = buildUrl("zpuser/avatar/leftCount")
  /**
   * 913.904 获取当月可更换微信/姓名/头像的剩余次数，微信
   * http://api.weizhipin.com/project/30/interface/api/206852
   */
  public static readonly URL_GET_USER_WECHAT_LEFT_COUNT: string = buildUrl("zpuser/wechat/leftCount")
  /**
   * 913.904 获取当月可更换微信/姓名/头像的剩余次数，姓名
   * http://api.weizhipin.com/project/30/interface/api/206852
   */
  public static readonly URL_GET_USER_NAME_LEFT_COUNT: string = buildUrl("zpuser/name/leftCount")
  /**
   * 913.904 获取当月可更换微信/姓名/头像的剩余次数，头像+微信+姓名
   * http://api.weizhipin.com/project/30/interface/api/206852
   */
  public static readonly URL_GET_USER_INFO_LEFT_COUNT: string = buildUrl("zpuser/info/leftCount")
  /**
   * 1115.42【面试】金牌获奖感知优化
   * <a href="https://api.weizhipin.com/project/30/interface/api/595202">...</a>
   */
  public static readonly URL_BOSS_INFO_TOP_BAR_REQUEST: string = buildUrl("zpboss/app/boss/top/bar/get")
  /**
   * 1104.180 个人信息页-补充认证详情
   * https://api.weizhipin.com/project/30/interface/api/519602
   */
  public static readonly URL_BOSS_AUTHENTICATION_INFO_REQUEST: string = buildUrl("certification/inspireGuide/complianceCert/getInfo")
  /**
   * 1115.42【面试】金牌获奖感知优化,boss个人信息顶部横条关闭
   * <a href="https://api.weizhipin.com/project/30/interface/api/595210">...</a>
   */
  public static readonly URL_BOSS_INFO_CLOSE_TOP_BAR: string = buildUrl("zpboss/app/boss/top/bar/close")
  /**
   * 1115.42【面试】金牌获奖感知优化（APP端），boss个人信息关闭红点
   * <a href="https://api.weizhipin.com/project/30/interface/api/596610">...</a>
   */
  public static readonly URL_BOSS_F4_CLOSE_RED_POINT: string = buildUrl("zpboss/app/boss/closeRedPoint")
  /**
   * 913.904 【牛人姓名】保存二次校验
   * http://api.weizhipin.com/project/30/interface/api/207220
   */
  public static readonly URL_GEEK_UPDATE_NAME_PRE_CHECK: string = buildUrl("zpgeek/cvapp/name/update/precheck")
  /**
   * 906.43
   * http://api.kanzhun-inc.com/project/30/interface/api/179254
   */
  public static readonly URL_GET_DIFF_CITY_RECRUIT_INFO: string = buildUrl("zpblock/job/acr/info/get")
  /**
   * 906.43
   * http://api.kanzhun-inc.com/project/30/interface/api/179261
   */
  public static readonly URL_CHOOSE_DIFF_CITY_RECRUIT: string = buildUrl("zpblock/job/acr/scope/set")
  /**
   * 906 发现用户数据迁移或者数据克隆调用该接口
   * http://api.kanzhun-inc.com/project/30/interface/api/180003
   */
  public static readonly URL_REPORT_DEVICE_CLONE: string = buildUrl("zppassport/device/report")
  /**
   * 906 更换公司(非首善）
   * http://api.kanzhun-inc.com/project/30/interface/api/181739
   */
  public static readonly URL_CHANGE_BRAND_COM: string = buildUrl("zpboss/app/brandCom/join/change")
  /**
   * 906 加入公司（首善）
   * http://api.kanzhun-inc.com/project/30/interface/api/181746
   */
  public static readonly URL_COMPLETE_BRAND_COM: string = buildUrl("zpboss/app/brandCom/join/complete")
  /**
   * 9.05 第二次扫描二维码
   */
  public static readonly URL_WEB_SECOND_SCAN_EDIT: string = buildUrl("zppassport/qrcode/webSecondScan")
  /**
   * 907 协议更行强提醒接口
   * http://api.kanzhun-inc.com/project/30/interface/api/182446
   */
  public static readonly URL_GET_USER_NOTICE_AGREEMENT_POP_WINDOWS: string = buildUrl("zpuser/agreement/notice/pop/windows")
  /**
   * 法务协议强提醒操作
   * http://api.kanzhun-inc.com/project/30/interface/api/182523
   */
  public static readonly URL_USER_AGREEMENT_NOTICE_ACTION: string = buildUrl("zpuser/agreement/notice/windows/operate")
  /**
   * 曝光实验F2黄条关闭接口
   * http://api.kanzhun-inc.com/project/30/interface/api/182712
   */
  public static readonly URL_CLOSE_F2_EXPOSURE_BAR: string = buildUrl("zpblock/passive/exposure/f2/bar/close")
  /**
   * 搜索品牌聚类接口
   * http://api.kanzhun-inc.com/project/30/interface/api/182992
   */
  public static readonly URL_GET_SEARCH_BRAND_COLLECTION: string = buildUrl("zpitem/boss/search/searchBrandCluster")
  /**
   * 【本地服务】职位列表
   * http://api.kanzhun-inc.com/project/30/interface/api/188585
   */
  public static readonly URL_GET_NATIVE_JOB_LIST: string = buildUrl("zpgeek/app/native/joblist")
  public static readonly URL_BOSS_ADDRESS_JOB_LIST: string = buildUrl("zpjob/boss/address/job/list")
  /**
   * 【908-新增】用户直播权限类型
   * http://api.kanzhun-inc.com/project/30/interface/api/189586
   */
  public static readonly URL_BOSS_LIVE_POWER_TYPE: string = buildUrl("zpboss/app/liveRecruit/livePowerType")
  /**
   * 【908-新增】【外部授权】管理员二维码授权
   * http://api.kanzhun-inc.com/project/30/interface/api/187836
   */
  public static readonly URL_ADMIN_QR_CODE_AUTH: string = buildUrl("zpboss/app/liveRecruit/extraAdmin/adminQRCode/auth")
  /**
   * 【908-新增】本地服务F1气泡入口
   * http://api.kanzhun-inc.com/project/30/interface/api/189159
   */
  public static readonly URL_NATIVE_F1_BUBBLE: string = buildUrl("zpgeek/app/native/f1/bubble")
  /**
   * 【908-新增】关闭本地服务引导气泡
   * http://api.kanzhun-inc.com/project/30/interface/api/189292
   */
  public static readonly URL_NATIVE_F1_BUBBLE_CLOSE: string = buildUrl("zpgeek/app/native/f1/bubble/close")
  public static readonly URL_JOB_CHAT_SWITCH_JOB: string = buildUrl("zpjob/job/chat/switch/job")
  /**
   * 908.560 职位招呼语列表
   * http://api.kanzhun-inc.com/project/30/interface/api/189628
   */
  public static readonly URL_ZPCHAT_GREETING_JOB_GET: string = buildUrl("zpchat/greeting/job/get")
  /**
   * 908.560 职位招呼语保存(编辑)
   * http://api.kanzhun-inc.com/project/30/interface/api/189705
   */
  public static readonly URL_ZPCHAT_GREETING_JOB_SAVE: string = buildUrl("zpchat/greeting/job/save")
  /**
   * 908.560 职位招呼语删除
   * http://api.kanzhun-inc.com/project/30/interface/api/189719
   */
  public static readonly URL_ZPCHAT_GREETING_JOB_DELETE: string = buildUrl("zpchat/greeting/job/delete")
  /**
   * 9.06 游客身份推荐列表
   */
  public static readonly URL_GET_VISITOR_RECOMMEND_GEEK_LIST: string = buildUrl("zpjob/recommend/visitor/geek/list")
  /**
   * 9.06 游客查看简历详情
   */
  public static readonly URL_GET_VISITOR_GEEK_DETAIL: string = buildUrl("zpboss/app/visitor/geek/detail")
  /**
   * 626_游客推荐职位
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/192344
   */
  public static readonly URL_ZPGEEK_APP_VISITOR_JOBLIST: string = buildUrl("zpgeek/app/visitor/recommend/joblist")
  /**
   * 626_职位详情
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/192344
   */
  public static readonly URL_ZPGEEK_VISITOR_JOB_QUERYDETAIL: string = buildUrl("zpgeek/jobapp/visitor/job/querydetail")
  /**
   * 706_游客推荐职位Plus
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/195200
   */
  public static readonly URL_ZPGEEK_APP_VISITOR_JOBLIST_PLUS: string = buildUrl("zpgeek/app/visitor/expect/recommend/joblist")
  /*
* http://api.weizhipin.com/project/30/interface/api/206224
* */
  public static readonly URL_ZPGEEK_APP_SEARCH_ICONLIST: string = buildUrl("zpgeek/app/search/iconlist")
  /*916开口模式每日速配
* http://api.weizhipin.com/project/30/interface/api/209109
* */
  public static readonly URL_ZPGEEK_APP_DAILY_TIP: string = buildUrl("zpgeek/app/daily/recommend/tip")
  /*919 游客模式根据经历自动生成期望
* http://api.weizhipin.com/project/30/interface/api/217904
* */
  public static readonly URL_ZPGEEK_EXPECTPOSITION_INIT: string = buildUrl("zpgeek/app/visitor/expectposition/init")
  public static readonly URL_ZPGEEK_DAILY_JOBLIST: string = buildUrl("zpgeek/app/daily/recommend/joblist")
  public static readonly URL_ZPGEEK_AXB_ZONE: string = buildUrl("zpgeek/app/axb/zone/joblist")
  /**
   * 1110.3 - F1-极速开聊
   * https://api.weizhipin.com/project/30/interface/api/561442
   */
  public static readonly URL_ZPGEEK_QUICK_CHAT_JOBLIST: string = buildUrl("zpgeek/app/f1/quick/chat/joblist")
  /**
   * 909泰坦星F1广告入口 - 关闭
   * <p>
   * http://api.kanzhun-inc.com/project/30/interface/api/195564
   */
  public static readonly URL_RECOMMEND_LISTAD_CLOSE: string = buildUrl("zpgeek/app/recommend/listad/close")
  public static readonly URL_RECOMMEND_LISTAD_CLICK: string = buildUrl("zpgeek/app/recommend/listad/click")
  /**
   * 908 安全F1页面强提示
   * http://api.kanzhun-inc.com/project/30/interface/api/197020
   */
  public static readonly URL_ZPDAC_SAFE_TIP: string = buildUrl("zpdac/safeTip")
  /**
   * 安全F1页面强提示弹出回调
   * http://api.kanzhun-inc.com/project/30/interface/api/197048
   */
  public static readonly URL_ZPDAC_SAFE_CALL: string = buildUrl("zpdac/safeCall")
  public static readonly URL_NOT_OPEN_JOB_TIP_CLICK: string = buildUrl("zpblock/notopen/job/tip/click")
  /**
   * 搜索中间页推荐列表推荐词
   * http://api.weizhipin.com/project/30/interface/api/204722
   */
  public static readonly URL_GET_ADVANCED_SEARCH_RECOMMEND_KEYWORDS: string = buildUrl("zpitem/boss/searchRecommend/keyWords")
  /**
   * 911.57 牛人vip场景化弹窗
   * http://api.weizhipin.com/project/30/interface/api/204582
   */
  public static readonly URL_ZPITEM_GEEK_VIP_SCENE_POPUP: string = buildUrl("zpitem/geek/vip/scene/popup")
  /**
   * 911.23 【教育经历】时间段修改检查
   * http://api.weizhipin.com/project/30/interface/api/204702
   */
  public static readonly URL_APP_GEEK_EDU_EXP_DATE_CHECK: string = buildUrl("zpgeek/cvapp/geek/eduexp/date/check")
  /**
   * 1001.9 【教育经历】教育经历保存前检查
   * https://api.weizhipin.com/project/30/interface/api/231696
   */
  public static readonly URL_APP_GEEK_EDU_EXP_PRE_CHECK: string = buildUrl("zpgeek/cvapp/geek/eduexp/precheck")
  /**
   * 提现发送极验
   * http://api.weizhipin.com/project/30/interface/api/204696
   */
  public static readonly URL_GET_WITHDRAW_START_CAPTCHA: string = buildUrl("zpp/app/user/withdraw/startCaptcha")
  /**
   * 提现发送验证码
   * http://api.weizhipin.com/project/30/interface/api/204697
   */
  public static readonly URL_WITHDRAW_SEND_SMS: string = buildUrl("zpp/app/user/withdraw/sendSms")
  /**
   * 支付宝授权参数加密接口
   * http://api.weizhipin.com/project/30/interface/api/204922
   */
  public static readonly URL_GET_ALI_LOGIN_AUTH_INFO: string = buildUrl("zpp/app/user/withdraw/aliAuthInfoSign")
  /**
   * 获取支付宝授权用户信息
   * http://api.weizhipin.com/project/30/interface/api/205023
   */
  public static readonly URL_GET_REFUND_AUTH_ACCOUNT_INFO: string = buildUrl("zpp/app/user/withdraw/userInfo")
  public static readonly URL_ZP_PASSPORT_PHONE: string = buildUrl("zppassport/phone/getVerifyCode")
  public static readonly URL_QUESTION_NAIRE_GET_QUEST: string = buildUrl("zpuser/questionnaire/getQuest")
  public static readonly URL_QUESTION_NAIRE_SAVE_RESULT_V2: string = buildUrl("zpuser/questionnaire/saveQuestResultV2")
  /**
   * 获取转发牛人简略信息
   * http://api.weizhipin.com/project/30/interface/api/207881
   */
  public static readonly URL_GET_SHARE_SIMPLE_INFO_BY_CIPHER: string = buildUrl("zpboss/app/share/getSimpleInfo")
  /**
   * 公司相册  BC 同一个接口
   * https://api.weizhipin.com/project/30/interface/api/229077
   */
  public static readonly URL_COMPANY_PHOTO_LIST: string = buildUrl("zpboss/app/brandInfo/getBrandFullPicture")
  /**
   * 精选tab弹窗关闭请求
   * http://api.weizhipin.com/project/30/interface/api/208174
   */
  public static readonly URL_GET_REFINED_GEEK_TAB_INFO_CLOSE: string = buildUrl("zpitem/refinedGeek/tab/info/close")
  /**
   * 高搜-牛人关联匿名牛人列表
   * http://api.weizhipin.com/project/30/interface/api/208577
   */
  public static readonly URL_GET_INNER_SEARCH_RESUME_LIST: string = buildUrl("zpitem/boss/relevant/geek/list")
  /**
   * http://api.weizhipin.com/project/30/interface/api/208673
   */
  public static readonly URL_ZPBLOCK_CHECK: string = buildUrl("zpblock/chat/reply/block/check/v2")
  /**
   * http://api.weizhipin.com/project/30/interface/api/208486
   */
  public static readonly URL_ZPBLOCK_CHECK_AND_RELEASE: string = buildUrl("zpblock/chat/reply/block/checkAndRelease/v2")
  /**
   * https://api.weizhipin.com/project/30/interface/api/503275
   */
  public static readonly URL_ZPBLOCK_CHAT_EXPOSURE_PRE_CHECK: string = buildUrl("zpblock/chat/job/exposure/preCheck")
  /**
   * 917 搜索中间页订阅推荐相关推荐简历列表
   * http://api.weizhipin.com/project/30/interface/api/215434
   */
  public static readonly URL_GET_SUBSCRIBE_RELATIVE_RESUME_LIST: string = buildUrl("zpitem/subscribe/subscribeRecommend")
  /**
   * 求职电话卡-点击拨打点击
   * http://api.weizhipin.com/project/30/interface/api/215722
   */
  public static readonly URL_JOB_PHONE_CARD_USE: string = buildUrl("zpitem/jobPhoneCard/use")
  /**
   * 猎头用户召回
   * http://api.weizhipin.com/project/30/interface/api/215970
   */
  public static readonly URL_GET_HUNTER_USER_RECALL: string = buildUrl("zpboss/app/ad/getImageAd")
  /**
   * 工作环境图片 在公司主页 中显示/隐藏
   */
  public static readonly URL_ENVIRONMENT_HIDE_OR_VISIBLE: string = buildUrl("zpboss/app/brandInfo/taste/hideWorkEnvironmentPicture")
  /**
   * 工作环境视频  在公司主页 中显示/隐藏
   */
  public static readonly URL_ENVIRONMENT_HIDE_OR_VISIBLE_VIDEO: string = buildUrl("zpboss/app/brandInfo/taste/hideWorkEnvironmentVideo")
  public static readonly URL_ENVIRONMENT_HIDE_OR_VISIBLE_PIC: string = buildUrl("zpboss/app/brandInfo/taste/hideWorkEnvironmentPicture")
  public static readonly URL_ENVIRONMENT_VIDEO_LIST: string = buildUrl("zpboss/app/brandInfo/taste/getBrandEnvironmentManagerList")
  /**
   * 删除工作环境图片
   */
  public static readonly URL_ENVIRONMENT_DELETE_PIC: string = buildUrl("zpboss/app/brandInfo/taste/deleteWorkEnvironmentPicture")
  /**
   * 917.2【证件照】头像美化功能入口
   * http://api.weizhipin.com/project/30/interface/api/213948
   */
  public static readonly URL_ZPUSER_AVATAR_BEAUTIFY_ENTRANCE: string = buildUrl("zpuser/avatar/beautify/entrance")
  public static readonly URL_ZPITEM_DIRECT_CALL_CANCEL_TIP: string = buildUrl("zpitem/directCall/cancel/tips")
  /**
   * http://api.weizhipin.com/project/30/interface/api/217754
   */
  public static readonly URL_SUBSCRIBE_TIP: string = buildUrl("zpgeek/app/intermediary/subscribe/tip")
  /**
   * http://api.weizhipin.com/project/30/interface/api/217756
   */
  public static readonly URL_SUBSCRIBE_ACTION: string = buildUrl("zpgeek/app/intermediary/subscribe/action")
  /**
   * VIP4特权引导相关信息接口
   * http://api.weizhipin.com/project/30/interface/api/217296
   */
  public static readonly URL_GET_VIP_PRIVILEGE_TIP_GUIDE: string = buildUrl("zpblock/vip/privilege/guide")
  /**
   * 记录是否已经出引导
   * http://api.weizhipin.com/project/30/interface/api/217298
   */
  public static readonly URL_RECORD_VIP_PRIVILEGE_TIP_GUIDE: string = buildUrl("zpblock/vip/privilege/guide/record")
  /**
   * https://api.weizhipin.com/project/30/interface/api/219338
   * 入口页面获取数据接口
   */
  public static readonly URL_ZPJOB_POSTERS_PAGE_DATA: string = buildUrl("zpjob/posters/page/data")
  /**
   * https://api.weizhipin.com/project/30/interface/api/219340
   * 选择职位，获取职位列表接口
   */
  public static readonly URL_ZPJOB_POSTERS_GET_SELECT_JOB: string = buildUrl("zpjob/posters/get/select/job")
  /**
   * https://api.weizhipin.com/project/30/interface/api/219342
   * 选择单个职位更新二维码
   */
  public static readonly URL_ZPJOB_POSTERS_REFRESH_SHARE_URL: string = buildUrl("zpjob/posters/refresh/share/url")
  /**
   * https://api.weizhipin.com/project/30/interface/api/294867
   * 获取职位排序后关键词
   */
  public static readonly URL_ZPJOB_POSTERS_SORT_SKILLS: string = buildUrl("zpjob/posters/sort/skills")
  /**
   * https://api.weizhipin.com/project/30/interface/api/219344
   * 轮询下载数据
   */
  public static readonly URL_ZPJOB_POSTERS_DOWNLOAD_INFO: string = buildUrl("zpjob/posters/download/info")
  /**
   * https://api.weizhipin.com/project/30/interface/api/219352
   * 提交数据操作接口
   */
  public static readonly URL_ZPJOB_POSTERS_SUBMIT_EXPORT_DATA: string = buildUrl("zpjob/posters/submit/export/data")
  /**
   * 919.252【商业】高搜支持列表开聊 - 使用前搜畅卡数量校验
   * http://api.weizhipin.com/project/30/interface/api/217520
   */
  public static readonly URL_ZP_ITEM_SEARCH_CHAT_CARD_PRECHECK: string = buildUrl("zpitem/searchChatCard/preCheck")
  public static readonly URL_ZP_VIP_KEFU_BUY_CHECK: string = buildUrl("zpblock/vip/kefu/check")
  public static readonly URL_ZP_VIP_PRIVILEGE_USED_RECORD: string = buildUrl("zpblock/vip/privilege/used/record")
  public static readonly URL_ZP_CHAT_SESSION_SUGGEST: string = buildUrl("zpchat/session/suggest")
  public static readonly URL_GET_SEARCH_SHADINGS: string = buildUrl("zpitem/boss/getSearchShadings")
  public static readonly URL_SEARCH_HUNTER_EVALUATE_TAG: string = buildUrl("zpitem/search/hunter/evaluation/tags")
  public static readonly URL_SEARCH_HUNTER_EVALUATE_SAVE: string = buildUrl("zpitem/search/hunter/evaluation/save")
  public static readonly URL_USER_DEVICE_LIST: string = buildUrl("zpuser/get/userDeviceList")
  public static readonly URL_USER_REMOVE_DEVICE: string = buildUrl("zpuser/remove/userDevice")
  /**
   * 1008.16 快速填写问卷配置
   * https://api.weizhipin.com/project/30/interface/api/219082
   */
  public static readonly URL_GEEK_RESUME_QUICK_INPUT_CONFIG_QUERY: string = buildUrl("zpgeek/cvapp/resume/assistant/config/query")
  /**
   * 1008.16 快速填写生成内容
   * https://api.weizhipin.com/project/30/interface/api/219644
   */
  public static readonly URL_GEEK_RESUME_QUICK_INPUT_SUBMIT: string = buildUrl("zpgeek/cvapp/resume/assistant/submit")
  /**
   * Boss查看geek简历预览时校验简历是否过期
   * https://api.weizhipin.com/project/30/interface/api/220122
   */
  public static readonly URL_CHECK_GEEK_RESUME_PREVIEW: string = buildUrl("zpgeek/cvapp/geek/resume/preview/check")
  /**
   * https://api.weizhipin.com/project/30/interface/api/306990
   * 【附件简历】附件简历保存前查询信息
   */
  public static readonly URL_ZPGEEK_APP_GEEK_RESUME_PRESAVE_QUERY: string = buildUrl("zpgeek/cvapp/geek/resume/presave/query")
  /**
   * https://api.weizhipin.com/project/20/interface/api/287118
   */
  public static readonly URL_ZPCHAT_CONTACT_VIRTUAL_PHONE_CALL: string = buildUrl("zpchat/contact/virtualPhone/call")
  /**
   * https://api.weizhipin.com/project/30/interface/api/223128
   */
  public static readonly URL_ZP_BLOCK_REMIND_GEEK: string = buildUrl("zpblock/remind/geek")
  /**
   * https://api.weizhipin.com/project/30/interface/api/223146
   */
  public static readonly URL_ZP_BLOCK_GEEK_CHAT_ENTER: string = buildUrl("zpblock/remind/geek/chat/entry")
  /**
   * <<<<<<< HEAD
   * 1001.255 我的订阅-黄条点击
   * https://api.weizhipin.com/project/30/interface/api/226134
   */
  public static readonly URL_SUBSCRIBE_SYSTEM_NOTICE_TIP_ACTION: string = buildUrl("zpitem/subscribe/systemNoticeTip/click")
  /**
   * 搜索结果页-弹窗是否能展示
   * https://api.weizhipin.com/project/30/interface/api/226143
   */
  public static readonly URL_SUBSCRIBE_SYSTEM_NOTICE_POP_CHECK: string = buildUrl("zpitem/subscribe/systemNoticePop/check")
  /**
   * 职位反馈问卷提交
   * https://api.weizhipin.com/project/30/interface/api/225927
   */
  public static readonly URL_ZPCHAT_FEEDBACK_ANSWER_JOB_QUESTION: string = buildUrl("zpchat/feedback/answerJobQuestion")
  /**
   * 看过我道具广告卡片boss点击关闭按钮
   * https://api.weizhipin.com/project/30/interface/api/226350
   */
  public static readonly URL_CLOSE_F2_ADVERTISE_CARD: string = buildUrl("zpitem/click/close/lookMeAd")
  /**
   * 921 获取分享口令对应详情信息
   * https://api.weizhipin.com/project/30/interface/api/228186
   */
  public static readonly URL_GET_SHARE_JUMP_TOKEN: string = buildUrl("zpboss/app/share/jumpToken/getDetail")
  /**
   * 充值渠道异常上报
   * https://api.weizhipin.com/project/30/interface/api/227196
   */
  public static readonly URL_ZPP_APP_USER_BZB_CHANNEL_ERROR: string = buildUrl("zpp/app/user/bzbChannelError")
  /**
   * IPV6探测,单独使用IPV6域名
   */
  public static readonly URL_IPV6_CONFIG: string = "https://api-ipv6.zhipin.com/api/zpCommon/ipv6Config"
  /**
   * 1001.9【教育经历】通过学校查询最低学历
   * https://api.weizhipin.com/project/30/interface/api/226071
   */
  public static readonly URL_GEEK_EDU_EXP_LOW_DEGREE_QUERY: string = buildUrl("zpgeek/cvapp/geek/eduexp/lowdegree/query")
  /**
   * 1013.2【在线简历】在线简历页黄条提醒
   * https://api.weizhipin.com/project/30/interface/api/233793
   */
  public static readonly URL_GEEK_RESUME_TIP_QUERY: string = buildUrl("zpgeek/cvapp/tip/webresume/query")
  /**
   * 1008.16 快速填写引导入口
   * https://api.weizhipin.com/project/30/interface/api/323388
   */
  public static readonly URL_GEEK_RESUME_ASSISTANT_GUIDE: string = buildUrl("zpgeek/cvapp/resume/assistant/guide")
  /**
   * 1013.7 【智能助手】查询智能助手入口
   * https://api.weizhipin.com/project/30/interface/api/422419
   */
  public static readonly URL_GEEK_RESUME_HELPER_ENTRY_QUREY: string = buildUrl("zpgeek/cvapp/resume/helper/query")
  /**
   * 1014.8 【工作经历】预测技能标签
   * https://api.weizhipin.com/project/30/interface/api/434491
   */
  public static readonly URL_GEEK_WORK_EXP_EMPHASIS_PREDICT: string = buildUrl("zpgeek/cvapp/geek/workexp/emphasis/predict")
  /**
   * 1014.9 【在线简历】通过图片读取文字
   * https://api.weizhipin.com/project/30/interface/api/434619
   */
  public static readonly URL_ZPGEEK_RESUME_PICTURE_READ: string = buildUrl("zpgeek/cvapp/resume/picture/read")
  /**
   * 1015.8【作品图片】提交待爬取的网络地址
   * https://api.weizhipin.com/project/30/interface/api/440857
   */
  public static readonly URL_ZPGEEK_RESUME_DESIGN_CRAWL_LINKURL_SUBMIT: string = buildUrl("zpgeek/cvapp/resume/design/crawl/linkurl/submit")
  /**
   * 1015.8【作品图片】查询爬取的网络图片列表-轮询
   * https://api.weizhipin.com/project/30/interface/api/440871
   */
  public static readonly URL_ZPGEEK_RESUME_DESIGN_CRAWL_PICTURE_LIST: string = buildUrl("zpgeek/cvapp/resume/design/crawl/picture/list")
  /**
   * 1008.14 【简历诊断】查询个人优势的诊断提示
   * https://api.weizhipin.com/project/30/interface/api/323190
   */
  //    public static final String URL_ZPGEEK_RESUME_SUGGEST_USERDESC_QUERY = buildUrl("zpgeek/cvapp/geek/resume/suggest/userdesc/query",

  /**
   * 1002.4【C】在线简历屏蔽公司逻辑调整+增加隐私保护入口,【在线简历】在线简历页黄条提醒关闭
   * https://api.weizhipin.com/project/30/interface/api/233847
   */
  public static readonly URL_GEEK_RESUME_CLOSE_TIP_BAR: string = buildUrl("zpgeek/cvapp/tip/webresume/close")
  /**
   * https://api.weizhipin.com/project/30/interface/api/228465
   */
  public static readonly URL_SELF_INTRODUCE_GET: string = buildUrl("zpchat/fastreply/selfIntroduce/get")
  public static readonly URL_WORK_EXP_GUIDE_TOPIC: string = buildUrl("zpboss/app/brandInfo/taste/guideTopics")
  public static readonly URL_BACK_CHECK_CHAT_PAGE: string = buildUrl("zpitem/backgroundCheck/chatPage/entrance")
  public static readonly URL_TALENT_CHAT_PAGE_ENTRANCE: string = buildUrl("zpitem/talent/chatPage/entrance")
  public static readonly URL_CHAT_CHECK_SHOW_BRAND_TAB: string = buildUrl("zpCompany/brandMix/chat/checkShowBrandTab")
  /**
   * 代招抽检客户关系弹窗
   * https://api.weizhipin.com/project/30/interface/api/248337
   */
  public static readonly URL_GET_HUNTER_SPOT_CHECK_POPUP: string = buildUrl("zpboss/app/hunter/spot/check/popup")
  /**
   * 取消资格证书认证
   * https://api.weizhipin.com/project/30/interface/api/248229
   */
  public static readonly URL_GEEK_CERTIFICATION_CANCEL: string = buildUrl("zpgeek/cvapp/geek/certification/cancel")
  /**
   * 1003.318 词典-词条详情页
   * https://api.weizhipin.com/project/30/interface/api/248832
   */
  public static readonly URL_DICTIONARY_DETAIL: string = buildUrl("moment/dict/dict/detail")
  /**
   * 1003.318 词典-词条反馈问题类型
   * https://api.weizhipin.com/project/30/interface/api/248886
   */
  public static readonly URL_DICTIONARY_FEEDBACK_TYPES: string = buildUrl("moment/dict/feedback/types")
  /**
   * 1003.318 词典-词条反馈提交表单
   * https://api.weizhipin.com/project/30/interface/api/248913
   */
  public static readonly URL_DICTIONARY_FEEDBACK_PUBLISH: string = buildUrl("moment/dict/feedback/publish")
  public static readonly URL_ZPGEEK_BOSS_TIP_STATUS_UOPDATE: string = buildUrl("zpgeek/cvapp/handicapped/bosstipstatus/update")
  /**
   * 1003.37 获取公司人才发展信息【编辑使用】
   * https://api.weizhipin.com/project/30/interface/api/248931
   */
  public static readonly URL_DEVELOPMENT_GETINFO: string = buildUrl("zpboss/app/brandComplete/development/getInfo")
  /**
   * 1003.37 更新公司人才发展信息
   * https://api.weizhipin.com/project/30/interface/api/248985
   */
  public static readonly URL_DEVELOPMENT_ADDORUPDATE: string = buildUrl("zpboss/app/brandComplete/development/addOrUpdate")
  /**
   * 1004.282 纯职类用户引导购买vip黄条
   * https://api.weizhipin.com/project/30/interface/api/261891
   */
  public static readonly URL_GET_PURE_VIP_POSITION_GUIDE: string = buildUrl("zpblock/vip/pure/position/guide")
  /**
   * 1004.282 关闭闭纯职类用户职位管理引导购买VIP黄条
   * https://api.weizhipin.com/project/30/interface/api/261900
   */
  public static readonly URL_PURE_VIP_POSITION_GUIDE_CLOSE: string = buildUrl("zpblock/vip/close/pure/position/guide")
  /**
   * 1004.81 【无障碍求职】变更无障碍求职状态
   * https://api.weizhipin.com/project/30/interface/api/260298
   */
  public static readonly URL_ZPGEEK_HANDICAPPED_STATUS_UPDATE: string = buildUrl("zpgeek/cvapp/handicapped/status/update")
  /**
   * 1019.4 【在线简历】查询在线简历页弹窗
   * https://api.weizhipin.com/project/30/interface/api/503782
   */
  public static readonly URL_ZPGEEK_WEBRESUME_DIALOG_QUERY: string = buildUrl("zpgeek/cvapp/webresume/dialog/query")
  /**
   * 1020.12 【设计作品】查询作品导入的配置信息
   * https://api.weizhipin.com/project/30/interface/api/505591
   */
  public static readonly URL_ZPGEEK_RESUME_DESIGN_OPTION_QUERY: string = buildUrl("zpgeek/cvapp/resume/design/option/query")
  /**
   * 1103.8 【作品图片】查询可抓取图片地址列表
   * https://api.weizhipin.com/project/30/interface/api/512140
   */
  public static readonly URL_ZPGEEK_CVAPP_RESUME_DESIGN_CRAWL_LINKURL_LIST: string = buildUrl("zpgeek/cvapp/resume/design/crawl/linkurl/list")
  /**
   * 1020.9 【作品图片】置顶设计作品组
   * https://api.weizhipin.com/project/30/interface/api/505855
   */
  public static readonly URL_ZPGEEK_RESUME_DESIGN_TOP: string = buildUrl("zpgeek/cvapp/resume/design/top")
  /**
   * 1020.9 【作品图片】删除设计作品组
   * https://api.weizhipin.com/project/30/interface/api/505870
   */
  public static readonly URL_ZPGEEK_RESUME_DESIGN_DELETE: string = buildUrl("zpgeek/cvapp/resume/design/delete")
  /**
   * 牛人电话直拨期望引导
   * https://api.weizhipin.com/project/30/interface/api/261531
   */
  public static readonly URL_ZPITEM_DIRECTCALL_EXPECTGUIDE: string = buildUrl("zpitem/directCall/expectGuide")
  public static readonly URL_MESSAGE_HISTORY_MSG: string = buildUrl("zpchat/message/historyMsg")
  /**
   * 【账户|优惠券统计】账户、优惠券信息
   */
  public static readonly URL_GET_USER_WALLET_ACCOUNT_INFO: string = buildUrl("zpp/app/user/accountInfo")
  /**
   * 搜畅中间页提示
   * https://api.weizhipin.com/project/30/interface/api/145833
   */
  public static readonly URL_SEARCH_CARD_ACTIVATION: string = buildUrl("zpitem/searchChatCard/activation")
  /**
   * 1005.43 面试管理优化 属于b端我的模块
   * https://api.weizhipin.com/project/30/interface/api/285687
   */
  public static readonly URL_INTERVIEW_REDPOT: string = buildUrl("zpinterview/boss/interview/f3/feedbackRedPot")
  /**
   * 1005.6 【NLP简历解析】删除解析记录
   * https://api.weizhipin.com/project/30/interface/api/292779
   */
  public static readonly URL_ZPGEEK_NLP_RESUME_PARSER_DELETE: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/delete")
  /**
   * 1005.123 获取底部Icon
   * https://api.weizhipin.com/project/30/interface/api/286020
   */
  public static readonly URL_GET_MAIN_TAB_ICON_CONFIG: string = buildUrl("zpuser/tabBarIcon/getConfig")
  /**
   * 主题配置获取
   * https://api.weizhipin.com/project/30/interface/api/498436
   */
  public static readonly URL_ZPUSER_TAB_BAR_ICON_GET_THEME_CONFIG: string = buildUrl("zpuser/tabBarIcon/getThemeConfig")
  /**
   * 1006.211【商业】充值活动
   * https://api.weizhipin.com/project/30/interface/api/295956
   */
  public static readonly URL_GET_RECHARGE_ACTIVITY: string = buildUrl("zpp/app/activity/getRechargeActivity")
  /**
   * 1006.211 活动充值详情页信息
   * https://api.weizhipin.com/project/30/interface/api/295965
   */
  public static readonly URL_GET_RECHARGE_ORDER_INFO: string = buildUrl("zpp/app/user/beanOrderInfo")
  /**
   * https://api.weizhipin.com/project/30/interface/api/521165
   */
  public static readonly URL_GET_RECHARGE_GOODS_INFO: string = buildUrl("zpp/app/bean/promotion/productList")
  public static readonly URL_GET_RECHARGE_PROMOTION_PRODUCT_DISCOUNT: string = buildUrl("zpp/app/bean/promotion/productDiscount")
  /**
   * https://api.weizhipin.com/project/30/interface/api/296991
   */
  public static readonly URL_ZP_RELATATION_GEEK_FILTER_LIST: string = buildUrl("zprelation/friend/geekFilterList")
  /**
   * https://api.weizhipin.com/project/30/interface/api/298359
   * 关闭高级筛选引导卡片
   */
  public static readonly URL_CLOSE_VIP_RECOMMEND_FILTER_GUIDE: string = buildUrl("zpjob/recommend/guide/filter/close")
  /**
   * https://api.weizhipin.com/project/30/interface/api/300159
   * BOSS F2筛选弹窗
   */
  public static readonly URL_F2_FILTER_GUIDE: string = buildUrl("zpjob/filter/guide")
  /**
   * https://api.weizhipin.com/project/30/interface/api/300168
   */
  public static readonly URL_F2_FILTER_GUIDE_EXPOSE: string = buildUrl("zpjob/filter/guide/exposure")
  /**
   * https://api.weizhipin.com/project/20/interface/api/300177
   */
  public static readonly URL_F2_FILTER_GUIDE_CLOSE: string = buildUrl("zpjob/filter/guide/close")
  /**
   * https://api.weizhipin.com/project/30/interface/api/301833
   */
  public static readonly URL_FAST_REPLY_SUGGEST_TEMPLATE: string = buildUrl("zpchat/fastreply/suggest/template")
  /**
   * https://api.weizhipin.com/project/30/interface/api/301851
   */
  public static readonly URL_ZPGEEK_WRITE_SUGGEST_QUERY: string = buildUrl("zpgeek/cvapp/content/write/suggest/query")
  /**
   * 【填写助手】内容模版列表
   * https://api.weizhipin.com/project/30/interface/api/306450
   */
  public static readonly URL_ZPGEEK_CONTENT_TEMPLATE_QUERY: string = buildUrl("zpgeek/cvapp/content/template/query")
  /**
   * 1011.11【智能助手】查询职业对比
   * https://api.weizhipin.com/project/30/interface/api/390987
   */
  public static readonly URL_ZPGEEK_POSITION_COMPARE_INFO_QUERY: string = buildUrl("zpgeek/cvapp/vocation/words/query")
  /**
   * 1103.16创建问答回话
   * https://api.weizhipin.com/project/30/interface/api/510592
   */
  public static readonly URL_ZPGEEK_RESUME_BOT_CREATE_SESSION: string = buildUrl("zpgeek/cvapp/resume/bot/create/session")
  /**
   * 【智能助手】查询参考词句
   * https://api.weizhipin.com/project/30/interface/api/378203
   */
  public static readonly URL_ZPGEEK_REFERENCE_WORDS_QUERY: string = buildUrl("zpgeek/cvapp/reference/words/query")
  /**
   * https://api.weizhipin.com/project/30/interface/api/300519
   */
  public static readonly URL_SIMILAR_COMPANY_JOB: string = buildUrl("zpgeek/app/brand/industry/similar/joblist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/300483
   */
  public static readonly URL_SIMILAR_COMPANY_LIST: string = buildUrl("zpgeek/app/brand/industry/similar/brandlist")
  /**
   * https://api.weizhipin.com/project/30/interface/api/300582
   */
  public static readonly URL_SIMILAR_COMPANY: string = buildUrl("zpgeek/app/brand/industry/page/detail")
  /**
   * https://api.weizhipin.com/project/30/interface/api/297936
   */
  public static readonly URL_GET_REJECT_INTRODUCE: string = buildUrl("zpboss/app/brandComplete/getRejectIntroduceInfo")
  public static readonly URL_GET_ZPKE_VIDEO_URL: string = buildUrl("zpke/app/get/video/url")
  public static readonly URL_COMPANY_INVITE_COMPLETE: string = buildUrl("zpgeek/app/brandInfo/invitecomplete")
  /**
   * 1007.255 高搜-搜索中间页拉取剩余次数
   * https://api.weizhipin.com/project/30/interface/api/308385
   */
  public static readonly URL_GET_ADVANCED_MIDDLE_PAGE_SEARCH_CARD_INFO: string = buildUrl("zpitem/boss/search/middle/pageInfo")
  /**
   * 网络通话导航问题
   * https://api.weizhipin.com/project/30/interface/api/307359
   */
  public static readonly URL_GET_CUSTOM_SERVICE_PHONE_NAVIGATION_LIST: string = buildUrl("kefu/app/audio/navigationList")
  /**
   * https://api.weizhipin.com/project/30/interface/api/310743
   */
  public static readonly URL_ZPCHAT_FAST_REPLY_CHASE_CHAT_SEAVE: string = buildUrl("zpchat/fastreply/keepTalking/save")
  // 面试表达接口, 属于聊天模块
  public static readonly URL_ZPCHAT_FAST_REPLY_INTERVIEWrEMID_SEND: string = buildUrl("zpchat/fastreply/interviewRemind/saveAndSend")
  /**
   * https://api.weizhipin.com/project/20/interface/api/311526
   */
  public static readonly URL_ZPCHAT_FAST_REPLY_SEND_REPLY_MSG: string = buildUrl("zpchat/fastreply/sendReplyMsg")
  /**
   * 1011.47 牛人获取面试评价tag 首页batch接口依赖
   * https://api.weizhipin.com/project/30/interface/api/375787
   */
  public static readonly URL_GEEK_INTERVIEW_SECURITY_FEEDBACK: string = buildUrl("zpinterview/geek/interview/question/get2")
  /**
   * https://api.weizhipin.com/project/30/interface/api/313092
   */
  public static readonly URL_CHAT_AGAIN_BATCH_DETAIL_INFO: string = buildUrl("zpitem/chatAgainBatch/detail/info")
  /**
   * https://api.weizhipin.com/project/30/interface/api/313101
   */
  public static readonly URL_CHAT_AGAIN_GEEK_LIST: string = buildUrl("zpitem/chatAgainBatch/geek/list")
  /**
   * https://api.weizhipin.com/project/30/interface/api/313137
   */
  public static readonly URL_CHAT_AGAIN_USE_FREE: string = buildUrl("zpitem/chatAgainBatch/use/free")
  public static readonly URL_ZP_ITEM_SEARCH_HUNTER_GEEK_CALL_REPLY_SHOW: string = buildUrl("zpitem/search/hunter/geekCallReplyShow")
  /**
   * 1008.033 新公司介绍页面-看看别人怎么写例子
   * https://api.weizhipin.com/project/30/interface/api/323739
   */
  public static readonly URL_BRAND_INTRODUCE_TEMPLATE: string = buildUrl("zpboss/app/brandComplete/introduce/getTemplateList")
  /**
   * 1008.255
   * https://api.weizhipin.com/project/30/interface/api/324036
   */
  public static readonly URL_GET_ADS_SUBSCRIBE_SIMILAR_RESUME_LIST: string = buildUrl("zpitem/subscribe/subscribeSimilarList")
  /**
   * https://api.weizhipin.com/project/30/interface/api/324351
   */
  public static readonly URL_GEEK_FILTER_LAYOUTV2: string = buildUrl("zprelation/friend/geekFilterLayout")
  /**
   * https://api.weizhipin.com/project/30/interface/api/326205
   */
  public static readonly URL_FAST_REPLY_GET_TEMPLATEV2: string = buildUrl("zpchat/fastreply/getTemplateV2")
  /**
   * https://api.weizhipin.com/project/30/interface/api/325539
   */
  public static readonly URL_FAST_REPLY_KEYWORDS: string = buildUrl("zpchat/fastreply/keywords")
  /**
   * https://api.weizhipin.com/project/30/interface/api/324063
   */
  public static readonly URL_FOCUS_INDUSTRY: string = buildUrl("zpgeek/app/focus/industryatlas/add")
  /**
   * https://api.weizhipin.com/project/30/interface/api/326745
   */
  public static readonly URL_FAST_REPLY_SELF_INTRODUCE_GUIDE: string = buildUrl("zpchat/fastreply/selfIntroduce/guide")
  /**
   * https://api.weizhipin.com/project/30/interface/api/326745
   * 客户端双聊触发接口,替换 #URL_FAST_REPLY_SELF_INTRODUCE_GUIDE
   */
  public static readonly URL_SESSION_GEEK_REPLY_REACH_BOTH_CHAT: string = buildUrl("zpchat/session/geekReply/reachBothChat")
  /**
   * https://api.weizhipin.com/project/30/interface/api/334719
   */
  public static readonly URL_ZPCHAT_SHORT_MSG_CLOSE: string = buildUrl("zpchat/shortmsg/close")
  /**
   * 1009.080【C】公司主页『我也想看』拓展公司业务信息标注
   * https://api.weizhipin.com/project/30/interface/api/337859
   */
  public static readonly URL_BRAND_INDUSTRY_LABEL_COMPLETE: string = buildUrl("zpboss/app/brandInfo/industryLabelComplete")
  /**
   * 1019.4 求职吉利成就 接口
   */
  public static readonly URL_ACHIEVEMENT_URL: string = buildUrl("zpgeek/app/job/achievement/query")
  /**
   * 删除期望地址
   */
  public static readonly URL_DELETE_EXPECT_ADDRESS: string = buildUrl("zpgeek/cvapp/geek/expectaddress/delete")
  /**
   * 1010.16【简历解析】查询授权信息
   * https://api.weizhipin.com/project/30/interface/api/370163
   */
  public static readonly URL_QUERY_ATTACHMENT_AUTH_INFO: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/auth/query")
  /**
   * 【简历诊断】查询简历诊断提示
   * https://api.weizhipin.com/project/30/interface/api/409619
   */
  public static readonly URL_GEEK_RESUME_SUGGEST_QUERY: string = buildUrl("zpgeek/cvapp/geek/resume/suggest/query")
  /**
   * 1010.13【F3】点击我的在线简历引导信息
   * https://api.weizhipin.com/project/30/interface/api/376611
   */
  public static readonly URL_GEEK_F3_RESUME_EDIT_CLICK: string = buildUrl("zpgeek/cvapp/f3/tip/click")
  /**
   * 1010.16【简历解析】提交解析简历授权
   * https://api.weizhipin.com/project/30/interface/api/366723
   */
  public static readonly URL_ATTACHMENT_AUTH_SUBMIT: string = buildUrl("zpgeek/cvapp/nlp/resume/parser/auth/submit")
  /**
   * 1010.259 f4牛人VIP卡片弹窗点击上报
   * https://api.weizhipin.com/project/30/interface/api/368419
   */
  public static readonly URL_GEEK_VIP_POP_WINDOW_CLICK: string = buildUrl("zpitem/geek/vip/popWindowClick")
  public static readonly URL_COMPANY_HOT_BRAND: string = buildUrl("zpCompany/leaderboard/hotBrandLeaderboard")
  public static readonly URL_FEED_BACK_ANSWER_JOB_QUESTION: string = buildUrl("zpchat/feedback/answerJobQuestionV2")
  /**
   * 1012 安全提醒弹框
   * https://api.weizhipin.com/project/30/interface/api/412611
   */
  public static readonly URL_GET_CERT_VIOLATE_RULE: string = buildUrl("certification/violate/rule/prompt/dialog")
  /**
   * 1012 安全提醒弹框关闭
   * https://api.weizhipin.com/project/30/interface/api/412619
   */
  public static readonly URL_CLOSE_CERT_VIOLATE_RULE: string = buildUrl("certification/violate/rule/prompt/dialog/cancel")
  /**
   * 1012 警告弹窗
   * https://api.weizhipin.com/project/20/interface/api/410427
   */
  public static readonly URL_GET_CERT_VIOLATE_WARNING: string = buildUrl("certification/violate/rule/warning/dialog")
  /**
   * 1012 警告弹窗关闭
   * https://api.weizhipin.com/project/20/interface/api/410451
   */
  public static readonly URL_CLOSE_CERT_VIOLATE_WARNING: string = buildUrl("certification/violate/rule/warning/dialog/cancel")
  /**
   * 1012 F2道具使用中提示
   * https://api.weizhipin.com/project/30/interface/api/412219
   */
  public static readonly URL_GET_F2_ITEM_USING_ENTRANCE: string = buildUrl("zpitem/itemUsing/f2tips")
  /**
   * 1012 关闭上报接口
   * https://api.weizhipin.com/project/30/interface/api/412227
   */
  public static readonly URL_CLOSE_F2_ITEM_USING_ENTRANCE_RED_DOT: string = buildUrl("zpitem/operate/close")
  /**
   * 1012 获取品牌公司相关信息
   * https://api.weizhipin.com/project/30/interface/api/410355
   */
  public static readonly URL_QUERY_BRAND_COM_INFO: string = buildUrl("zpgeek/jobapp/jobdetail/brandcominfo/query")
  public static readonly URL_QUERY_JD_POPUPGUIDES: string = buildUrl("zpgeek/jobapp/jobdetail/popupguides/query")
  public static readonly URL_ZPITEM_GEEK_JOBDETAIL: string = buildUrl("zpitemGeek/geek/jobDetail")
  public static readonly URL_ZPITEM_GEEK_COMPETITIVE: string = buildUrl("zpitemGeek/geek/competitiveInfo")
  public static readonly URL_FEED_BACK_GET_JOB_QUESTION: string = buildUrl("zpchat/feedback/getJobQuestion")
  /**
   * 【1012-新增】查询直播间的直播状态
   * https://api.weizhipin.com/project/30/interface/api/416539
   */
  public static readonly URL_BOSS_LIVE_STATE_QUERY: string = buildUrl("zpboss/app/liveRecruit/liveState/query")
  /**
   * 1013.500 app-引流是否弹窗
   */
  public static readonly URL_GET_HUNTER_REQUIREMENT_POP_UP_WINDOW: string = buildUrl("zpboss/app/hunter/customer/requirement/showPopUpWindow")
  /**
   * 1013.500 app-弹窗不再提示
   */
  public static readonly URL_CLOSE_HUNTER_REQUIREMENT_POP_UP_WINDOW: string = buildUrl("zpboss/app/hunter/customer/requirement/dontPopWindowAgain")
  /**
   * 1013.275【商业】沟通钥匙到期不退 - 沟通钥匙激活接口
   */
  public static readonly URL_ZPBLOCK_CHAT_CHATKEY_ACTIVATE: string = buildUrl("zpblock/chat/chatkey/activate")
  /**
   * https://api.weizhipin.com/project/30/interface/api/453457
   */
  public static readonly URL_ZPGEEK_RESUME_CONTENT_OPTIMIZE: string = buildUrl("zpgeek/cvapp/resume/content/optimize")
  /**
   * 1013.83 【行业社区】内容列表
   */
  public static readonly URL_INDUSTRY_COMMUNITY_CONTENT_LIST: string = buildUrl("moment/industryatlas/content/list")
  public static readonly URL_FRIEND_PROGRESS_GET: string = buildUrl("zprelation/friend/progress/get")
  public static readonly URL_FRIEND_PROGRESS_UPDATE: string = buildUrl("zprelation/friend/progress/update")
  public static readonly URL_FRIEND_PROGRESS_CANCEL: string = buildUrl("zprelation/friend/progress/cancel")
  /**
   * 1013.254 获取搜索推荐热门词
   * https://api.weizhipin.com/project/30/interface/api/428603
   */
  public static readonly URL_GET_ADS_RELATIVE_HOT_WORD: string = buildUrl("zpitem/boss/getHotWord")
  public static readonly URL_ZP_BLOCK_CHAT_KEY_LIST: string = buildUrl("zpblock/job/not/chatkey/list")
  public static readonly URL_FRIEND_GET_HIGH_QUALITY_GEEKS: string = buildUrl("zprelation/friend/getHighQualityGeeks")
  /**
   * https://api.weizhipin.com/project/30/interface/api/434971
   */
  public static readonly URL_BRAND_COM_SPLIT: string = buildUrl("zpCompany/brandCom/brandSplit")
  public static readonly URL_SELF_INTRODUCE_CLOSE: string = buildUrl("zpchat/fastreply/selfIntroduce/close")
  public static readonly URL_QUICK_UPLOAD: string = buildUrl("zpupload/quicklyUpload")
  public static readonly URL_SIGN_REFRESH_UPLOAD: string = buildUrl("zpupload/oss/sign/refresh")
  public static readonly URL_DELETE_HOME_ADDRESS: string = buildUrl("zpgeek/cvapp/geek/homeaddress/delete")
  /**
   * 1014.234【商业】精选列表售卖方式调整 - 道具操作-点击
   * https://api.weizhipin.com/project/30/interface/api/435875
   */
  public static readonly URL_ZPITEM_OPERATE_CLICK: string = buildUrl("zpitem/operate/click")
  public static readonly URL_CONFIG_GET_BY_KEY: string = buildUrl("zpitem/item/config/getByKey")
  public static readonly URL_MESSAGE_ACTION_CLICLK: string = buildUrl("zpgeek/app/message/action/click")
  public static readonly URL_BOTH_WAY_CALL_PREUSER: string = buildUrl("zpitem/bothWayCall/preUse")
  public static readonly URL_BOTH_WAY_CALL_USE: string = buildUrl("zpitem/bothWayCall/use")
  /**
   * 1018.601 牛人简历页气泡按钮展示频率控制
   */
  public static readonly URL_ZPJOB_CARD_VIEW_GEEK_BUBBLE_DISAPPEAR: string = buildUrl("zpjob/card/view/geek/bubble/disappear")
  /**
   * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=157174422
   * https://api.weizhipin.com/project/1502/interface/api/297369
   */

  public static readonly URL_BOSS_HP_COM_JOB_LIST: string = buildUrl("zpgeek/app/geek/brand/queryjoblist")
  public static readonly URL_GREETING_GET_ANSWER_EXTRA: string = buildUrl("zpchat/greeting/job/question/getAnswerDialogInfo")
  public static readonly URL_GREETING_QUESTION_ANSWER: string = buildUrl("zpchat/greeting/job/question/answer")
  public static readonly URL_GREETING_QUESTION_ANSWERS: string = buildUrl("zpchat/greeting/job/question/getAnswers")
  public static readonly URL_GREETING_QUESTION_GET_ANSWER: string = buildUrl("zpchat/greeting/job/question/getAnswer")
  public static readonly URL_GREETING_DEL_ANSWER: string = buildUrl("zpchat/greeting/job/question/deleAnswer")
  public static readonly URL_GREETING_JOB_QUESTION_LIST: string = buildUrl("zpchat/greeting/job/question/list")
  public static readonly URL_GREETING_JOB_QUESTION_GET_MAPPINGS: string = buildUrl("zpchat/greeting/job/question/getMappingsV2")
  public static readonly URL_GREETING_JOB_QUESTION_DELETE_BIND_QUESTION: string = buildUrl("zpchat/greeting/job/question/deleBindQuestion")
  public static readonly URL_GREETING_JOB_QUESTION_JOB_LIST: string = buildUrl("zpchat/greeting/job/question/jobList")
  public static readonly URL_GREETING_JOB_QUESTION_BIND: string = buildUrl("zpchat/greeting/job/question/bind")
  public static readonly URL_GREETING_JOB_QUESTION_ANSWER_BOARD: string = buildUrl("zpchat/greeting/job/question/answerBoard")
  public static readonly URL_GREETING_JOB_QUESTION_CONFIG_ENTRANCE: string = buildUrl("zpchat/greeting/job/question/configEntrance")
  /**
   * 1018.241【道具】邀约红包引导增益最大道具 - app-item场景化优惠-上报
   */
  public static readonly URL_ZP_ITEM_SCENEACTIVITY_REPORT: string = buildUrl("zpitem/sceneActivity/report")
  /**
   * 1019.276 解锁曝光
   */
  public static readonly URL_ZPBLOCK_CHAT_CHATKEY_RELEASE: string = buildUrl("zpblock/chat/chatkey/release")
  /**
   * 1020.9 牛人作品集点赞
   */
  public static readonly URL_ZPJOB_VIEW_PRODUCT_LIKE: string = buildUrl("zpjob/view/product/like")
  /**
   * 1020.9 牛人作品集取消点赞
   */
  public static readonly URL_ZPJOB_VIEW_PRODUCT_LIKE_CANCEL: string = buildUrl("zpjob/view/product/like/cancel")
  public static readonly URL_ZPRELATION_RECOMMEND_GEEK_GET_JOBS: string = buildUrl("zprelation/recommend/geekGetJobs")
  public static readonly URL_ZPCHAT_HANDLE_APPLE: string = buildUrl("zpchat/blue/onekey/handleApply")
  public static readonly URL_CHAT_GET_BRAND_CARD: string = buildUrl("zpCompany/brandMix/chat/getBrandCard")
  public static readonly URL_CHAT_SEND_BRAND_CARD: string = buildUrl("zpCompany/brandMix/chat/sendBrandCard")
  public static readonly URL_CHAT_ANXINBAO_JOBS: string = buildUrl("zpchat/message/getAnXinBaoJobs")
  /**
   * 1104.061
   * https://api.weizhipin.com/project/30/interface/api/519145
   * 获取聊天满意度收集弹窗模板
   */
  public static readonly URL_CHAT_FEEDBACK_SATISFACTION_CARD: string = buildUrl("zpchat/feedback/getSatisfactionCard")
  /**
   * 1120.603
   * https://api.weizhipin.com/project/30/interface/api/629273
   * 羚羊计划评价模板
   */
  public static readonly URL_ANTELOPE_EVALUATE: string = buildUrl("moment/publicWelfare/antelope/evaluate/template")
  /**
   * 1120.603
   * https://api.weizhipin.com/project/30/interface/api/629270
   * 羚羊计划提交评价模板
   */
  public static readonly URL_ANTELOPE_EVALUATE_ADD: string = buildUrl("moment/publicWelfare/antelope/relation/evaluate/add")
  //https://api.weizhipin.com/project/30/interface/api/508282
  public static readonly URL_GEEK_HONOR_LIST: string = buildUrl("zpgeek/cvapp/geek/honor/config/list")
  public static readonly URL_ZPGEEK_APP_RECOMMEND_EXP_QUERY: string = buildUrl("zpgeek/cvapp/recommend/exp/query")
  /**
   * 1011 举报埋点相关接口
   */
  public static readonly URL_ZPUSER_USER_REPORT_PROCESS_ID: string = buildUrl("zpuser/user/report/processId")
  /**
   * 1102.033 收藏标准品牌、集团引导弹窗
   */
  public static readonly URL_BRAND_INFO_FOCUS_GUIDE_QUERY: string = buildUrl("zpgeek/app/brandInfo/aggregation/focusguide/query")
  /**
   * v1101.265 弹窗曝光接口
   */
  public static readonly URL_ZPBLOCK_PAGE_DIALOG_TOAST: string = buildUrl("zpblock/page/dialog/toast")
  /**
   * 1102 道具action上报
   */
  public static readonly URL_ZPITEM_ACTION_REPORT: string = buildUrl("zpitem/action/report")
  /**
   * 【1103-新增】面试题搜索-综合
   * https://api.weizhipin.com/project/30/interface/api/511972
   */

  public static readonly URL_MOMENT_INTERVIEW_QUESTION_SEARCH_ALL_TYPE: string = buildUrl("moment/interview/question/searchAllType")
  /**
   * 【1103-新增】面试题搜索
   * https://api.weizhipin.com/project/30/interface/api/512038
   */

  public static readonly URL_MOMENT_INTERVIEW_QUESTION_SEARCH: string = buildUrl("moment/interview/question/search")
  /**
   * 1103【品牌主页】获取公司主营业务标签跳转地址
   */
  public static readonly URL_COMPANY_BRAND_BUSINESS_LABEL_CLICK: string = buildUrl("zpgeek/app/brandbusiness/label/click")
  /**
   * https://api.weizhipin.com/project/30/interface/api/519110
   */
  public static readonly URL_ZPCHAT_WECHAT_MARK_WX_LIN: string = buildUrl("zpchat/wechat/markWxLink")
  /**
   * https://api.weizhipin.com/project/30/interface/api/22913
   */
  public static readonly URL_ZPCHAT_WECHAT_NOTIFY_TOGGLE: string = buildUrl("zpchat/wechat/notify/toggle")
  /**
   * 查看牛人详情用户对收起来的图标点击
   * https://api.weizhipin.com/project/30/interface/api/520263
   */
  public static readonly URL_ZPJOB_CARD_VIEW_GEEK_FOLDING_CLICK: string = buildUrl("zpjob/card/view/geek/folding/click")
  /**
   * 查看牛人详情对图标收起来文案提示出现
   */
  public static readonly URL_ZPJOB_CARD_VIEW_GEEK_FOLDING_SHOW: string = buildUrl("zpjob/card/view/geek/folding/show")
  /**
   * https://api.weizhipin.com/project/30/interface/api/520265
   */
  public static readonly URL_ZPJOB_CARD_VIEW_GEEK_DISLIKE_SHOW: string = buildUrl("zpjob/card/view/geek/dislike/show")
  /**
   * 1105.603 F1通知弹窗引导 批量开启开关
   * https://api.weizhipin.com/project/30/interface/api/520941
   */
  public static readonly URL_GEEK_BATCH_OPEN_PUSH: string = buildUrl("zpchat/notify/setting/batchOpenPush")
  public static readonly URL_GET_RESUME_MSG_DESIGN_PIC: string = buildUrl("zpchat/message/getResumeMsgDesignPic")
  public static readonly URL_CHAT_KEY_SHOW_PRE_EXPIRE_CARD: string = buildUrl("zpblock/chatKey/show/pre/expire/card")
  public static readonly URL_USER_MARK_GET_LABELS_SEARCH: string = buildUrl("zprelation/userMark/getLabels4Search")
  /**
   * 1106.3【资格证书】职位详情证书引导选择弹窗
   * https://api.weizhipin.com/project/30/interface/api/526829
   */
  public static readonly URL_JD_POPUP_GUIDE_CERT_LIST: string = buildUrl("zpgeek/app/jobdetail/popupguides/certification/list")
  public static readonly URL_GET_F3_ITEM_ADVERTS: string = buildUrl("zpitem/sceneActivity/getF3ItemAdverts")
  public static readonly URL_USER_MARK_GET_JOB_REJECT_REASON: string = buildUrl("zprelation/userMark/getJobRejectReason")
  public static readonly URL_APP_NAGATIVE_FEED_BACK_REASON_V2: string = buildUrl("zpgeek/app/negativefeedback/reasons/v2")
  public static readonly URL_GEEK_HANDI_ZONE_RECOMMEND_QUERY: string = buildUrl("zpgeek/app/handicapped/zone/recommend/query")
  public static readonly URL_GPT_CREATE_SESSION: string = buildUrl("zpchat/robot/createSession")
  public static readonly URL_GPT_GET_JOB_RECOMMED: string = buildUrl("zpchat/robot/getJobRecommend")
  /**
   * 1109.104 举报处置职位列表
   * https://api.weizhipin.com/project/30/interface/api/552061
   */
  public static readonly URL_GET_BOSS_REPORT_DISPOSE_JOB_LIST: string = buildUrl("zpjob/report/dispose/job/list")
  /**
   * 1109.104 举报处置职位列表在APP上展示后
   * https://api.weizhipin.com/project/30/interface/api/552070
   */
  public static readonly URL_BOSS_REPORT_DISPOSE_DIALOG_SHOW: string = buildUrl("zpjob/report/dispose/shown")
  public static readonly URL_GPT_COMMUNICATE_OFFICER_TAB: string = buildUrl("moment/communicate/officer/tab")
  public static readonly URL_MOMENT_SHORT_VIDEO_BOTTOM_TAB: string = buildUrl("moment/discover/shortVideoBottomTab")
  public static readonly URL_GPT_ZPGEEK_CVAPP_GEEK_EDUEXP_COURSE_LIST: string = buildUrl("zpgeek/cvapp/geek/eduexp/course/list")
  public static readonly URL_GPT_TTS_RESULT: string = buildUrl("zpchat/robot/getTtsResult")
  public static readonly URL_GPT_CREATE_TTS_TASK: string = buildUrl("zpchat/robot/createTtsTask")
  /**
   * 【一键投递】交换信息预检查
   */
  public static readonly URL_GEEK_EXCHANGE_PRECHECK_QUERY: string = buildUrl("zpgeek/app/exchange/precheck/query")
  /**
   * c - 职位描述页面缺失项卡片关闭
   */
  public static readonly URL_ZPJOB_JOB_MISSING_JOB_CARD_CLOSE: string = buildUrl("zpjob/job/missing/job/card/close")
  public static readonly URL_ZPITEM_ONLINE_USE_GUIDE: string = buildUrl("zpitem/online/clickDeliveryGuide")
  public static readonly URL_ZPCHAT_FAST_REPLY_SNED_REPLY_MSG: string = buildUrl("zpchat/fastreply/sendReplyMsg")
  public static readonly URL_ZPCHAT_FAST_REPLY_SNED_REPLY_CARD: string = buildUrl("zpchat/fastreply/sendFastReplyCard")
  /**
   * 【期望】根据学生虚拟L2code获取感兴趣L3code列表
   */
  public static readonly URL_GEEK_EXPECT_POSITION_L3: string = buildUrl("zpgeek/cvapp/geek/expectposition/interestposition/config")
  public static readonly URL_ZPCHAT_CHAT_UNSUITABLE_STATUS: string = buildUrl("zpblock/chat/unSuitable/status")
  public static readonly URL_CHANCE_INDUSTRY_INFO: string = buildUrl("zpgeek/app/geek/chance/industry/info")
  public static readonly URL_DETAIL_RECOMMEND_BRAND: string = buildUrl("zpgeek/app/geek/chance/daily/recommend/brand/list")
  public static readonly URL_ZPGEEK_QUERY_RESUME: string = buildUrl("zpgeek/cvapp/geek/resume/preview/url/query")
  public static readonly URL_ZPCHAT_MESSAGE_GET_ANXINBAO_WEICHAT_DATA: string = buildUrl("zpchat/message/getAnXinBaoWechatMessage")
  /**
   * 支付服务灰度接口
   * https://api.weizhipin.com/project/30/interface/api/612512
   */
  public static readonly URL_GET_ZPP_GRAY: string = buildUrl("zpp/app/gray/forF4")
  public static readonly URL_ZPCHAT_GEEK_HUNTER_RECOMMEND_PAGE_URL: string = buildUrl("hunter/app/geek/hunterRecommend/getRecommendPageUrl")
  public static readonly URL_ZPCHAT_RELATION_FRIEND_SORE_LIST: string = buildUrl("zprelation/friend/greetSortList")
  public static readonly URL_ZPCHAT_IMPROPER_SHOW_REJECT_TIP: string = buildUrl("zpchat/improper/showRejectTip")
  public static readonly URL_ZPCHAT_IMPROPER_BOSS_CHANGE_TEMPLATE: string = buildUrl("zpchat/improper/bossChangeTemplate")
  public static readonly URL_ZPCHAT_IMPROPER_BOSS_SAVE_TEMPLATE: string = buildUrl("zpchat/improper/bossSaveTemplate")
  public static readonly URL_ZPCHAT_IMPROPER_BOSS_UPDATE_TEMPLATE: string = buildUrl("zpchat/improper/bossUpdateTemplate")
  public static readonly URL_ZPCHAT_CHAT_AGAIN_BATCH_CHAT_GPT_CREATE: string = buildUrl("zpitem/chatAgainBatch/chatGPT/message/create")
  public static readonly URL_ZPCHAT_CHAT_GREETING_JOB_QUESTION_NOTICE: string = buildUrl("zpchat/greeting/job/question/notice")
  public static readonly URL_ZPCHAT_CHAT_CUSTOM_ADD_OR_UPDATE: string = buildUrl("zpchat/greeting/job/question/custom/addOrUpdate")
  public static readonly URL_ZPCHAT_CHAT_CUSTOM_CHECK_UPDATE: string = buildUrl("zpchat/greeting/job/question/custom/checkUpdate")
  public static readonly URL_ZPCHAT_CHAT_CUSTOM_DELETE: string = buildUrl("zpchat/greeting/job/question/custom/delete")
  public static readonly URL_ZPCHAT_GREETING_JOB_JOBLIST_V2: string = buildUrl("zpchat/greeting/job/question/jobListV2")
  public static readonly URL_ZPCHAT_GREETING_JOB_CLOSE_GEEK_SELF_ANSWER: string = buildUrl("zpchat/greeting/job/question/closeGeekSelfAnswer")
  public static readonly URL_ZPCHAT_REPORT_CLOSE_BANNER: string = buildUrl("zpchat/greeting/job/question/reportCloseBanner")
  public static readonly URL_ZPCHAT_JOB_QUESTION_GPT_ANSWER: string = buildUrl("zpchat/greeting/job/question/getGPTAnswer")
  public static readonly URL_HUNTER_APP_INTENTTION_BOSS_CHAT_ENTRANCE: string = buildUrl("hunter/app/intention/boss/chat/entrance")
  public static readonly URL_HUNTER_APP_INTENTTION_BOSS_QUERY_RECOMMEND: string = buildUrl("hunter/app/intention/boss/recommend/queryRecommend")
  public static readonly URL_HUNTER_APP_INTENTTION_BOSS_MARK_COLLECT: string = buildUrl("hunter/app/intention/boss/recommend/markCollect")
  public static readonly URL_ZPBLOCK_VVIP_BATCH_CHAT_BATCH_USE: string = buildUrl("zpblock/vvip/batch/chat/batchUse")
  public static readonly URL_ZPCHAT_ANTELOPE_GET_INQUIRE_INFO: string = buildUrl("zpchat/antelope/getInquiryInfos")
  public static readonly URL_ZPCHAT_ANTELOPE_UPDATE_GREETING_STATUS: string = buildUrl("zpchat/antelope/updateGreetingStatus")
  public static readonly URL_ZPCHAT_ANTELOPE_UPDATE: string = buildUrl("zpchat/antelope/updateInquiry")
  public static readonly URL_ZPCHAT_ANTELOPE_SAVE: string = buildUrl("zpchat/antelope/saveInquiry")
  public static readonly URL_ZPCHAT_ANTELOPE_DELETE: string = buildUrl("zpchat/antelope/delInquiry")
  public static readonly URL_ZPCHAT_ANTELOPE_RESORTE: string = buildUrl("zpchat/antelope/resortInquiry")
  /**
   * 1121.62 【驻外岗位偏好】查询驻外偏好信息
   */
  public static readonly URL_ZPGEEK_OVERSEASTRAITOPTIONS_QUERY: string = buildUrl("zpgeek/cvapp/overseastraitoptions/collectinformation/query")
  /**
   * 1121.62 【驻外岗位偏好】保存驻外偏好信息
   */
  public static readonly URL_ZPGEEK_OVERSEASTRAITOPTIONS_SAVE: string = buildUrl("zpgeek/cvapp/overseastraitoptions/collectinformation/save")
  /**
   * 1121.62 【驻外岗位偏好】删除驻外偏好信息
   */
  public static readonly URL_ZPGEEK_OVERSEASTRAITOPTIONS_DELETE: string = buildUrl("zpgeek/cvapp/overseastraitoptions/collectinformation/delete")
  /**
   * 1122.166 开场问题GPT内测申请弹窗
   */
  public static readonly URL_ZPCHAT_GET_GPT_APPLY_ALERT: string = buildUrl("zpchat/greeting/job/question/getGptApplyAlert")
  /**
   * 1122.166 开场问题GPT内测申请
   */
  public static readonly URL_ZPCHAT_GET_GPT_APPLY: string = buildUrl("zpchat/greeting/job/question/gptApply")
  public static readonly URL_HUNTER_H5_CALL_CONTACT_STATUS: string = buildUrl("hunter/app/call/search/hunter/geek/contactStatus")
  public static readonly URL_HUNTER_H5_CALL_GET_TEL_FAST: string = buildUrl("hunter/app/call/search/hunter/geek/getTelFast")
  public static readonly URL_GREETING_GET_GPT: string = buildUrl("zpchat/greeting/getSuggestGreeting")
  public static readonly URL_ZPMSG_HISTORY_PULL: string = buildUrl("zpmsg/history/pull")
  public static readonly URL_ZPMSG_HISTORY_FIRSTPULLSECURITYID: string = buildUrl("zpmsg/history/getFirstPullSecurityId")
  public static readonly URL_HUNTER_H5_CLOSE_CHAT_TIP: string = buildUrl("hunter/app/call/search/hunter/geek/closeChatTip")
  public static readonly URL_HUNTER_H5_GET_HUNTER_PHONE: string = buildUrl("hunter/app/call/search/hunter/geek/getHunterPhone")
  /**
   * 1122.602【原1121.607】【BC】MBTI测试 - 列表展示
   */
  public static readonly URL_INSIGHT_ASSESS_MBTI_GEEK_SELECT_LIST: string = buildUrl("insight/assess/mbti/geek/selectList")
  /**
   * 1122.602【原1121.607】【BC】MBTI测试 - C 在线简历页 添加修改删除 展示结果
   */
  public static readonly URL_INSIGHT_ASSESS_MBTI_GEEK_SETTING: string = buildUrl("insight/assess/mbti/geek/setting")
  /**
   * 1122.602【原1121.607】【BC】MBTI测试 - C看自己的在线简历页 测评的结果展示
   */
  public static readonly URL_INSIGHT_ASSESS_MBTI_GEEK_VIEW_INFO: string = buildUrl("insight/assess/mbti/geek/view/info")
  /**
   * 1124.31【牛人偏好】实习偏好数据查询
   * https://api.weizhipin.com/project/30/interface/api/644455
   */
  public static readonly URL_STU_INTENT_PREFER: string = buildUrl("zpgeek/cvapp/preference/practice/query")
  /**
   * 1124.31 【牛人偏好】保存实习偏好
   * https://api.weizhipin.com/project/30/interface/api/644557
   */
  public static readonly URL_STU_INTENT_PREFER_SAVE: string = buildUrl("zpgeek/cvapp/preference/practice/save")
  /**
   * 1124.31 【牛人偏好】实习偏好选项
   * https://api.weizhipin.com/project/30/interface/api/644558
   */
  public static readonly URL_STU_INTENT_PREFER_DATA: string = buildUrl("zpgeek/cvapp/preference/practice/filter")
  /**
   * 1125.604 羚羊群下发文件消息
   */
  public static readonly URL_GROUP_CHAT_SEND_FILE_MSG: string = buildUrl("zpchat/group/antelope/sendFileMsg")
  /**
   * 1125.604 羚羊群更新文件消息
   */
  public static readonly URL_GROUP_CHAT_UPDATE_FILE_MSG: string = buildUrl("zpchat/group/antelope/updateFileMsg")
  public static readonly URL_FRIEND_ROBOT_FILTER_SHOW: string = buildUrl("zprelation/friend/greetRobotFilter/show")
  public static readonly URL_FRIEND_ROBOT_FILTER_SUBMIT: string = buildUrl("zprelation/friend/greetRobotFilter/submit")
  public static readonly URL_FRIEND_ROBOT_FILTER_GET_RECORD_ID: string = buildUrl("zprelation/friend/greetRobotFilter/getRecord")
  public static readonly URL_FRIEND_ROBOT_FILTER_GET_RECORD_INFOS: string = buildUrl("zprelation/friend/greetRobotFilter/getGeekInfos")
  public static readonly URL_ZPCHAT_FAST_REPLY_KEEP_TALKING_CHANGE: string = buildUrl("zpchat/fastreply/keepTalking/change")
  /**
   * 1125.169 获取推荐反馈助手预填信息
   * https://api.weizhipin.com/project/30/interface/api/650613
   */
  public static readonly URL_CHAT_BOSS_F1_RECOMMEND_DEMO: string = buildUrl("zpjob/assistant/demo")
  /**
   * 1125.169 推荐反馈助手聊天
   * https://api.weizhipin.com/project/30/interface/api/650620
   */
  public static readonly URL_CHAT_BOSS_F1_RECOMMEND_SUBMIT: string = buildUrl("zpjob/assistant/chat")
  /**
   * 【在线简历】获取参加工作时间初始化信息
   * <p>
   * https://api.weizhipin.com/project/30/interface/api/519577
   */
  public static readonly URL_ZPGEEK_CVAPP_STARTWORKDATE_PRECHECK: string = buildUrl("zpgeek/cvapp/startworkdate/precheck")

  /**【基础数据升级】- 获取筛选豁免行业
   * https://api.weizhipin.com/project/30/interface/api/667511
   * https://zhishu.zhipin.com/wiki/ZE9wCy3fSxp
   * F1筛选专用 - 全部行业页面
   * */
  public static readonly URL_CONFIG_INDUSTRY_EXEMPTION: string = buildUrl("zpCommon/config/industryFilterExemption")





  /**
   * 线上环境使用logapi.zhipin.com域名的api集合
   */
  public static readonly LOG_HOST_URL_LIST: Array<string> =
    [
      URLConfig.URL_COMMON_BATCH_STATISTICS,
      URLConfig.URL_COMMON_BATCH_USTATISTICS,
      URLConfig.URL_LOG_COLLECTOR
    ]
  /**
   * 线上环境使用https://www.dianzhangzhipin.com域名的api集合
   */
  public static readonly DIAN_ZHANG_HOST_URL_LIST: Array<string> =
    [
      URLConfig.URL_DZ_LOGIN_CODE,
      URLConfig.URL_DZ_REGSEND_CODE,
      URLConfig.URL_DZ_MACHINE_VALIDATION
    ]
  /**
   * 不需要Token的Http请求URL集合
   */
  public static readonly NONE_TOKEN_URL_LIST: Array<string> = [
    HostConfig.getUrlPath(URLConfig.URL_GENERATE_DEVICE_ID),
    HostConfig.getUrlPath(URLConfig.URL_RECOMMEND_BOSS),
    HostConfig.getUrlPath(URLConfig.URL_BATCH_RUN),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_GET),
    HostConfig.getUrlPath(URLConfig.URL_DATA_GET),
    HostConfig.getUrlPath(URLConfig.URL_GET_SKILL_WORD),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_DISTANCE),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_SUBWAY),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_BUSINESS_DISTRICT),

    HostConfig.getUrlPath(URLConfig.URL_CONFIG_CITY),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_DEGREE),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_BOSS_FILTER),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_DISTANCE_FILTER),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_EXPERIENCE),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_GEEK_FILTER),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_INDUSTRY),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_INTERN),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_SALARY),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_SCALE),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_SCHOOL_SEARCH),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_STAGE),
    HostConfig.getUrlPath(URLConfig.URL_CONFIG_PARTIME_PAYTYPE),


    HostConfig.getUrlPath(URLConfig.URL_REGSEND_CODE),
    HostConfig.getUrlPath(URLConfig.URL_GET_IMAGE_CODE),
    HostConfig.getUrlPath(URLConfig.URL_LOGIN_CODE),
    HostConfig.getUrlPath(URLConfig.URL_LOGIN_AUTO),
    HostConfig.getUrlPath(URLConfig.URL_LOGIN),
    HostConfig.getUrlPath(URLConfig.URL_THIRDPART_LOGIN),
    HostConfig.getUrlPath(URLConfig.URL_USER_COUNTTRY_CODE),
    HostConfig.getUrlPath(URLConfig.URL_PHONE_VERIFY),
    HostConfig.getUrlPath(URLConfig.URL_RESET_PASSWORD),
    HostConfig.getUrlPath(URLConfig.URL_UPGRADE_CHECK),
    HostConfig.getUrlPath(URLConfig.URL_BOSS_UNLOGIN_SEARCH_HOT_WORD),
    HostConfig.getUrlPath(URLConfig.URL_GEEK_UNLOGIN_SEARCH_HOT_WORD),
    HostConfig.getUrlPath(URLConfig.URL_BOSS_UNLOGIN_SEARCH_GEEK),
    HostConfig.getUrlPath(URLConfig.URL_GEEK_UNLOGIN_SEARCH_JOB),
    HostConfig.getUrlPath(URLConfig.URL_SCREEN_ADVERT_GET),
    HostConfig.getUrlPath(URLConfig.URL_COMMON_BATCH_USTATISTICS),
    HostConfig.getUrlPath(URLConfig.URL_GET_CITY_POSITION),
    HostConfig.getUrlPath(URLConfig.URL_GET_ADVERTISE_V2),
    HostConfig.getUrlPath(URLConfig.URL_COMMON_APP_ACTIVATE),
    HostConfig.getUrlPath(URLConfig.URL_GEEK_DETAIL_MIX_UNLOGIN),
    HostConfig.getUrlPath(URLConfig.URL_PHOTO_UPLOAD_NO_LOGIN),

    HostConfig.getUrlPath(URLConfig.URL_CHECK_PHONE),
    HostConfig.getUrlPath(URLConfig.URL_THIRDPART_BIND_PHONE),
    HostConfig.getUrlPath(URLConfig.URL_VERIFY_PWD),
    HostConfig.getUrlPath(URLConfig.URL_MACHINE_VALIDATION),
    HostConfig.getUrlPath(URLConfig.URL_PHONE_MOBILE_SDK_INFO),
    HostConfig.getUrlPath(URLConfig.URL_PHONE_MOBILE_VERIFY),
    HostConfig.getUrlPath(URLConfig.URL_DZ_MACHINE_VALIDATION),
    HostConfig.getUrlPath(URLConfig.URL_DZ_REGSEND_CODE),
    HostConfig.getUrlPath(URLConfig.URL_DZ_LOGIN_CODE),
    HostConfig.getUrlPath(URLConfig.URL_USER_JUDGE),
    HostConfig.getUrlPath(URLConfig.URL_USER_SWITCH),
    HostConfig.getUrlPath(URLConfig.URL_USER_CREATE),
    HostConfig.getUrlPath(URLConfig.URL_COMMON_CONFIG),
    HostConfig.getUrlPath(URLConfig.URL_GET_MAIN_TAB_ICON_CONFIG),
    HostConfig.getUrlPath(URLConfig.URL_ZPPASSPORT_PHONE_SDK_INFO),
    HostConfig.getUrlPath(URLConfig.URL_GET_VISITOR_RECOMMEND_GEEK_LIST),
    HostConfig.getUrlPath(URLConfig.URL_GET_VISITOR_GEEK_DETAIL),

    HostConfig.getUrlPath(URLConfig.URL_ZPGEEK_VISITOR_JOB_QUERYDETAIL),
    HostConfig.getUrlPath(URLConfig.URL_ZPGEEK_APP_VISITOR_JOBLIST),
    HostConfig.getUrlPath(URLConfig.URL_GET_VISITOR_GEEK_DETAIL),
    HostConfig.getUrlPath(URLConfig.URL_ZPGEEK_APP_VISITOR_JOBLIST_PLUS),

    HostConfig.getUrlPath(URLConfig.URL_IPV6_CONFIG),
    // 店长直聘授权登录相关接口不需要登录
    HostConfig.getUrlPath(URLConfig.URL_DIRECT_HIRES_TICKET_LOGIN),
    HostConfig.getUrlPath(URLConfig.URL_DIRECT_HIRES_LOGIN_SEND_SMS),
    HostConfig.getUrlPath(URLConfig.URL_DIRECT_HIRES_CODE_LOGIN),
    // HostConfig.getUrlPath(URLConfig.URL_AB_TEST),
    HostConfig.getUrlPath(URLConfig.URL_ZPUSER_TAB_BAR_ICON_GET_THEME_CONFIG)
  ]
}

