/**
 * 公参
 */
import { encodeRequest, signature } from '@ohos/signer/src/main/ets/signer';
import hilog from '@ohos.hilog';
import URL from '@ohos.url';
import util from '@ohos.util';
import TextUtils from '../../utils/TextUtils';
import { IZPHttpProvider } from '../ZPHttpProvider';
import { isNeedLoginUrl } from './URLConfig';
import { HostConfig } from './HostConfig';

export const DATA_NOT_COMPUTE_TOKEN = "DATA_NOT_COMPUTE_TOKEN";

export class HttpCommonParams {
  v?: string;
  uniqid?: string;
  curidentity?: string;
  app_id?: string;
  channel?: string
  model?: string;
  ssid?: string;
  bssid?: string;
  imei?: string;
  longitude?: number
  dzt?: number;
  latitude?: number;
  version?: string;
  did?: string;
  oaid?: string;
  sub_source?: string;
  work_mark?: string;
  umid?: string;
  hasLocPer?: number;
  isNeedLocationInfo?: boolean;
  isNeedDeviceInfo?: boolean;
  is_bg_req?: number;
  tourist?: boolean; //游客模式
  req_time?: string
  client_info?: Object
}

export class ZPHttpParams {
  sp?: string
  sig?: string
  app_id?: string
}

export class BaseParams {
  curidentity?: string
  v?: string
  req_time?: string
  uniqid?: string
  client_info?: string
}


/**
 * 获取公参
 */
export function getCommonParams(provider: IZPHttpProvider, fullUrl: string, params: Map<string, string>): ZPHttpParams {
  let httpCommonParams: HttpCommonParams = getHttpCommonParams(provider)

  // 是否需要先登录
  let isNeedLogin = isNeedLoginUrl(fullUrl)
  if (params.get(DATA_NOT_COMPUTE_TOKEN)) {
    isNeedLogin = false
  }
  const secretKey: string | null = isNeedLogin ? provider.getSecretKey() : null

  // 获取公共参数
  let baseParams = new BaseParams()
  baseParams.curidentity = httpCommonParams.curidentity
  baseParams.v = httpCommonParams.v
  baseParams.req_time = Date.now().toString()
  baseParams.uniqid = httpCommonParams.uniqid

  const clientInfoMap: Record<string, string | number | undefined> = getClientInfoMap(httpCommonParams)
  baseParams.client_info = JSON.stringify(clientInfoMap)

  // 拼接全量参数
  Object.entries(baseParams).forEach((value: [string, Object]) => {
    params.set(value[0], value[1]?.toString())
  })
  let zpHttpParams: ZPHttpParams = getZPHttpParams(params, fullUrl, secretKey)

  zpHttpParams.app_id = httpCommonParams.app_id

  return zpHttpParams
}

/**
 * 获取公参
 */
export function getHttpCommonParams(provider: IZPHttpProvider, isNeedLocationInfo?: boolean): HttpCommonParams {

  const common = new HttpCommonParams()

  common.v = provider.getVersion();
  common.curidentity = provider.getUserRole();
  common.app_id = provider.getAppId();
  common.version = provider.getBuildVersion();
  common.channel = provider.getChannel();
  common.model = provider.getDeviceType();

  //定位权限是否开启
  common.hasLocPer = provider.checkAccessFineLocation() ? 1 : 0;
  common.isNeedLocationInfo = isNeedLocationInfo; // 业务需要定位相关信息
  common.isNeedDeviceInfo = provider.canDeviceInfoStatus(); // 业务需要设备相关信息
  common.uniqid = provider.getUniqId();
  if (common.isNeedDeviceInfo) {
    common.oaid = provider.getOAID();
    common.did = provider.getDid();
  }

  if (1 == common.hasLocPer) {
    if (common.isNeedLocationInfo) {
      hilog.info(0x0000, 'HttpCommonParams', 'case %{public}d add', 2);
      common.ssid = provider.getWifiSSID();
      common.bssid = provider.getWifiBSSID();
      common.longitude = provider.getLongitude();
      common.latitude = provider.getLatitude();
    }
  }

  common.dzt = provider.getDzt();
  common.sub_source = provider.getSubSource();
  common.is_bg_req = provider.isForeground() ? 0 : 1;
  common.tourist = provider.isTourist();

  return common
}

export function getZPHttpParams(allParams: Map<string, string>, fullUrl: string, secretKey: string | null): ZPHttpParams {

  const params = new ZPHttpParams()

  const urlMap: Map<string, string> = new Map()

  // allParams.keys().sort((a: string, b: string) => {
  //   return a.localeCompare(b, undefined, { sensitivity: "base" });
  // });
  //
  // let sortedEntries = Array.from(myMap.entries()).sort(([keyA], [keyB]) => keyA.localeCompare(keyB));

  // 按key的首字母排序
  // const sortedKeys:Array<string> = Array.from(allParams.keys())
  //   .sort((a: string, b: string) => a[0].localeCompare(b[0]));
  // const sortedMap: Map<string, string> = new Map(
  //   sortedKeys.map((key: string) => [key, allParams.get(key)]));

  Array.from(allParams.keys())
    .sort((a: string, b: string) => a[0].localeCompare(b[0]))
    .forEach((key: string) => {
      urlMap.set(key, allParams.get(key) ?? '')
    })
  // .map((key: string) => [key, allParams.get(key)])

  // Object.entries(allParams)
  //   .sort((a: [string, string], b: [string, string]) => {
  //     return a[0].localeCompare(b[0])
  //   }).forEach((value: [string, string]) => {
  //   urlMap.set(value[0], value[1])
  // })

  const urlParams = new URL.URLParams()
  urlMap.forEach((value: string, key: string) => {
    urlParams.append(key, value)
  })

  let urlStr = urlParams.toString()
  let encode = new util.TextEncoder()
  let array: Uint8Array = encode.encodeInto(urlStr)
  const encodeReq: string | null = array ? encodeRequest(array.buffer as ArrayBuffer, secretKey) : null

  params.sp = encodeReq ?? ''

  if (urlStr?.length > 5000) {
    urlStr = urlStr.substring(0, 5000);
  }

  const signBase: string = HostConfig.getUrlPath(fullUrl) + urlStr
  const encodeSign: Uint8Array = new util.TextEncoder().encodeInto(signBase)
  const signBuff: ArrayBuffer | null = encodeSign ? signature(encodeSign.buffer as ArrayBuffer, secretKey) : null
  const signStr = signBuff ? util.TextDecoder.create('utf-8').decodeWithStream(new Uint8Array(signBuff)) : ''

  params.sig = signStr

  return params
}


// export function getClientInfoMap(common: HttpCommonParams): Map<string, string | number | undefined> {
//   let map: Map<string, string | number | undefined> = new Map()
//
//   // map["version"] = "7.1.2" //TODO Build.VERSION.RELEASE
//   map.set("version", "7.1.2")
//   map.set("os", "Android")
//   map.set("start_time", Date.now().toString()) //TODO
//   map.set("resume_time", Date.now().toString()) //TODO
//   map.set("channel", common.channel ?? "0") //TODO
//   map.set("model", common.model)
//   map.set("dzt", common.dzt)
//   map.set("loc_per", common.hasLocPer) //是否有位置权限，公参
//   if (common.isNeedLocationInfo && 1 == common.hasLocPer) {
//     map.set("ssid", common.ssid)
//     map.set("bssid", common.bssid)
//     map.set("longitude", common.longitude)
//     map.set("latitude", common.latitude)
//   }
//   if (common.uniqid) {
//     map.set("uniqid", common.uniqid)
//   }
//   if (common.isNeedDeviceInfo) {
//     map.set("oaid", common.oaid)
//     if (common.did) {
//       map.set("did", common.did)
//     }
//     if (common.umid) {
//       map.set("umid", common.umid)
//     }
//   }
//
//   if (common.work_mark) {
//     map.set("work_mark", common.work_mark)
//   }
//   if (common.sub_source) {
//     map.set("sub_source", common.sub_source)
//   }
//   map.set("is_bg_req", common.is_bg_req)
//   map.set("network", "WIFI") //TODO
//
//   if (common.tourist) {
//     map.set("operator", "WIFI name") //TODO
//   }
//   map.set("abi", "arm") //TODO
//
//   return map
// }


export function getClientInfoMap(common: HttpCommonParams): Record<string, string | number | undefined> {
  let map: Record<string, string | number | undefined> = {}

  map["version"] = "7.1.2" //TODO Build.VERSION.RELEASE
  map["os"] = "HarmonyOS"
  map["start_time"] = Date.now().toString() //TODO
  map["resume_time"] = Date.now().toString() //TODO
  map["channel"] = common.channel ?? "0" //TODO
  map["model"] = common.model
  map["dzt"] = common.dzt
  map["loc_per"] = common.hasLocPer //是否有位置权限，公参
  if (common.isNeedLocationInfo && 1 == common.hasLocPer) {
    map["ssid"] = common.ssid
    map["bssid"] = common.bssid
    map["longitude"] = common.longitude
    map["latitude"] = common.latitude
  }
  if (common.uniqid) {
    map["uniqid"] = common.uniqid
  }
  if (common.isNeedDeviceInfo) {
    map["oaid"] = common.oaid
    if (common.did) {
      map["did"] = common.did
    }
    if (common.umid) {
      map["umid"] = common.umid
    }
  }

  if (common.work_mark) {
    map["work_mark"] = common.work_mark
  }
  if (common.sub_source) {
    map["sub_source"] = common.sub_source
  }
  map["is_bg_req"] = common.is_bg_req
  map["network"] = "WIFI" //TODO

  if (common.tourist) {
    map["operator"] = "WIFI name" //TODO
  }
  map["abi"] = "arm" //TODO

  return map
}

export function getClientInfoStr(provider: IZPHttpProvider): string {
  let httpCommonParams: HttpCommonParams = getHttpCommonParams(provider)
  const clientInfoMap: Record<string, string | number | undefined> = getClientInfoMap(httpCommonParams)
  return JSON.stringify(clientInfoMap)
}