import { AxiosHeaders, AxiosResponse } from '@ohos/axios'
import { Compress, Encoding, Encryption, ReqHeader } from '../constant/Constant'
import { IZPHttpProvider } from '../ZPHttpProvider'

export const HEADER_ENCRYPT_KEY: string = "content-encrypt";

export function hasEncodeHeader(response: AxiosResponse): boolean {
  return response?.headers?.[HEADER_ENCRYPT_KEY]?.toString()?.toLowerCase() === 'yes';
}

export function getCommonHeaders(provider: IZPHttpProvider, traceIdStr: string, isEncrypt: boolean): AxiosHeaders {
  const headers: AxiosHeaders = new AxiosHeaders()
  //TODO mrf 测试AxiosHeaders，，，
  getCommonHeader(provider, traceIdStr, isEncrypt)?.forEach((value: string, key: string) => {
    /*注: 不能使用headers.set(key, value)，t2加不进去*/
    headers[key] = value
  })
  return headers;
}

/**
 * 保持原有逻辑，原来下载资源的时候没有设置编码之类的header
 */
export function getCommonHeader(provider: IZPHttpProvider, traceIdStr: string, isEncrypt: boolean): Map<string, string> {
  let headerMap: Map<string, string> = new Map();
  /*User-Agent*/
  headerMap.set(ReqHeader.HEADER_USER_AGENT, provider.getHeaderFullUserAgent());
  /*traceId*/
  if (traceIdStr?.length > 0) {
    headerMap.set(ReqHeader.HEADER_TRACE_ID, traceIdStr);
  }
  /*zp-tag*/
  let zpTag: string | null = provider.getHeaderZPTagText();
  if (zpTag && zpTag.length > 0) {
    headerMap.set(ReqHeader.HEADER_ZP_TAG, zpTag);
  }
  /*t2*/
  let t2: string | undefined = provider.getHeaderToken()
  if (t2 && t2.length > 0) {
    headerMap.set(ReqHeader.HEADER_T2, t2);
  }
  /*isEncrypt*/
  if (isEncrypt) {
    headerMap.set(ReqHeader.HEADER_ENCODING, Encoding.getSupport());
    headerMap.set(ReqHeader.HEADER_ENCRYPTION, Encryption.getSupport());
    headerMap.set(ReqHeader.HEADER_COMPRESS, Compress.getSupport());
  }
  return headerMap;
}
