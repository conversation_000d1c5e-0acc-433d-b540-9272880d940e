import axios, {
  AxiosError,
  AxiosHeaders,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  FormData,
  HttpStatusCode,
  InternalAxiosRequestConfig,
  AxiosProgressEvent
} from '@ohos/axios'
import { HostConfig } from './config/HostConfig'
import { getCommonParams, ZPHttpParams, getClientInfoStr } from './config/HttpParameters'
import util from '@ohos.util'
import { parseConfigDataList, ZPBatchParams, ZPReqData, ComplexReqData } from './bean/ReqParams'
import { isNeedLoginUrl, transformUrlHost, URLConfig } from './config/URLConfig'
import TextUtils from '../utils/TextUtils'
import Url from '@ohos.url'
import { Compress, Encoding, Encryption, ProcessingMethod, RspHeader } from './constant/Constant'
import HttpResponse, {
  isNeedVerify,
  isRespSuccessful,
  isServerCommonError,
  isSigError,
  isSuccess,
  isTokenExpired
} from './bean/HttpResponse'
import { decodeContent, decodeContent2 } from '@ohos/signer/src/main/ets/signer'
import { IZPHttpProvider, ZPHttpProvider } from './ZPHttpProvider'
import { getCommonHeaders, hasEncodeHeader } from './config/HeaderConfig'
import HttpLog from '../utils/HttpLog'
import ZPHttpError, { obtainHttpError } from './bean/ZPHttpError'
import common from '@ohos.app.ability.common'
import { IZPHttpErrorProvider, ZPHttpErrorProvider } from './ZPHttpErrorProvider'


const TAG = "TAG_ZP_HTTP"

export enum ZPBody {
  FormBody, JsonBody, MultipartBody, BatchBody
}

export interface ZPAxiosRequestConfig extends AxiosRequestConfig<ZPReqData | ZPReqData[]> {
  zpBody?: ZPBody

  params?: ZPReqData | ZPReqData[]

  httpProvider?: IZPHttpProvider

  isHeaderEncrypt?: boolean
}

function isNotJson(data?: string | null): boolean {
  let ret = false
  if (data && data?.length > 2) {
    ret = (data.charAt(0) !== '{' || data?.charAt(data?.length - 1) !== '}')
  }
  return ret;
  // return data && data?.length > 2 && (data?.charAt(0) !== '{' || data?.charAt(data?.length - 1) !== '}');
}

/** 创建请求实例 */
function createService(): AxiosInstance {

  /*创建一个 axios 实例命名为 service*/
  const service = axios.create()

  /*请求拦截*/
  service.interceptors.request.use(/*请求配置*/
    (config: InternalAxiosRequestConfig<ZPReqData>) => {
      formatConfigInfoLog("请求参数", config)
      return config
    },
    // 发送失败
    (error: Error) => {
      HttpLog.error(TAG, `发送失败：${error?.toString()}`);
      Promise.reject(obtainHttpError(error))
    }
  )
  // 响应拦截（可根据具体业务作出相应的调整）
  // (response: AxiosResponse<Object, ZPReqData | ZPReqData[]>) => {
  service.interceptors.response.use(// (response: AxiosResponse) => {
    (response: AxiosResponse<Object, ZPReqData | ZPReqData[]>) => {
      /*返回结果*/
      let resultData: string = '{}';
      if (isRespSuccessful(response?.status)) {
        // apiData 是 api 返回的数据
        let apiData: Object = response.data

        let fullUrl = transformUrlHost(TextUtils.joinStrings('', response.config.baseURL, response.config.url))
        let isNeedLogin = isNeedLoginUrl(fullUrl)
        const reqConfig: ZPAxiosRequestConfig = response.config
        let secretKey: string | null = isNeedLogin ? getSecretKey(reqConfig) : null

        let method = getProcessingMethod(response)
        if (method != null) { // 解码、解密、解压缩
          if (response?.data instanceof ArrayBuffer) {
            // 二进制数据则直接返回
            // const responseType: string | undefined = response.config?.responseType
            /**/
            HttpLog.debug(TAG, 'interceptors batchAllResponse', 'resp data type:', typeof apiData)

            /*需要登录，但是没有key（一般发生在被踢、退出登录、切换身份）但是还有请求回来的接口*/
            if (isNeedLogin && (!secretKey || secretKey.length <= 0)) {
              HttpLog.error(TAG, 'interceptors batchAllResponse', 'request url: ', fullUrl)
            }
            /*正常结果*/
            else {
              let decryptedBuffer = decodeContent2(apiData as ArrayBuffer, secretKey, method.encoding, method.encryption, method.compress)
              resultData = util.TextDecoder.create('utf-8').decodeWithStream(new Uint8Array(decryptedBuffer))
              if (isNotJson(resultData)) {
                HttpLog.error(TAG, 'interceptors batchAllResponse', 'resultData: ', fullUrl, resultData)
              }
            }
            HttpLog.debug(TAG, '响应结果: ', resultData)
          }
        } else if (hasEncodeHeader(response)) {
          if (apiData) {
            const apiStr: string = apiData as string //util.TextDecoder.create('utf-8').decodeWithStream(new Uint8Array(apiData as ArrayBuffer))
            if (apiStr && apiStr.length > 0) {
              let decryptedBuffer: ArrayBuffer | null = decodeContent(apiStr, secretKey)
              resultData = decryptedBuffer ? util.TextDecoder.create('utf-8')
                .decodeWithStream(new Uint8Array(decryptedBuffer)) : resultData
            }
          }
        } else {
          resultData = util.TextDecoder.create('utf-8').decodeWithStream(new Uint8Array(apiData as ArrayBuffer))
        }

        HttpLog.error(TAG, `响应结果：\n status = ${response.status} \n headers = ${JSON.stringify(response.headers)} \n body = ${resultData}`)

        const jsonObj: HttpResponse<Object> = JSON.parse(resultData)
        const code: number | undefined = jsonObj?.code
        const message: string | undefined = jsonObj?.message
        const httpStatus: number = response.status

        if (code === undefined) {
          return Promise.reject(ZPHttpError.obj(ZPHttpError.CODE_DEFAULT,
            ZPHttpError.ERROR_CODE + response?.status, response, response.status))
        }
        /*code === 0 来表示没有业务错误*/
        else if (isSuccess(code)) {
          response.data = jsonObj
          return response
        }
        /*T 票过期*/
        else if (isTokenExpired(code)) {
          const error = ZPHttpError.obj(code, message ?? `登录过期：${code}`, response, httpStatus)
          defErrorProvider.handleTokenExpired(error)
          return Promise.reject(error)
        }
        /*需要验证*/
        else if (isNeedVerify(code)) {
          const error = ZPHttpError.obj(code, message ?? `需要验证：${code}`, response, httpStatus)
          defErrorProvider.handleNeedVerify(error)
          return Promise.reject(error)
        }
        /*服务器公共类异常*/
        else if (isServerCommonError(code)) {
          return Promise.reject(ZPHttpError.obj(code, message ?? `服务器公共异常：${code}`, response, httpStatus))
        }
        else {
          if (isSigError(code)) {
            HttpLog.error(TAG, 'sig error')
          }

          return Promise.reject(ZPHttpError.obj(code, message ?? `服务器业务异常：${code}`, response, httpStatus))
        }
      } else {
        return Promise.reject(ZPHttpError.obj(ZPHttpError.CODE_DEFAULT,
          ZPHttpError.ERROR_SERVER_FAILED + response?.status, response, response.status))
      }

    },
    (error: ZPHttpError) => {
      HttpLog.error(TAG, `返回错误：${error?.toString()}`);
      return Promise.reject(ZPHttpError.obj(error.code ?? ZPHttpError.CODE_DEFAULT, error.message,
        error.response, error.response?.status, error.config, error.stack))
    }
  )
  return service
}

//TODO mrf
function getSecretKey(reqConfig?: ZPAxiosRequestConfig): string | null {
  return reqConfig?.httpProvider?.getSecretKey() ?? (defHttpProvider?.getSecretKey() ?? null)
}

function getProcessingMethod(response: AxiosResponse): ProcessingMethod | null {
  if (response.data != null && response.headers != null) {
    let encode: string = response.headers[RspHeader.HEADER_ENCODING];
    let encrypt: string = response.headers[RspHeader.HEADER_ENCRYPTION];
    let compress: string = response.headers[RspHeader.HEADER_COMPRESS];
    if (encode && encrypt && compress) {
      let method: ProcessingMethod = new ProcessingMethod()
      method.encoding = Encoding.getEncoding(encode);
      method.encryption = Encryption.getEncryption(encrypt);
      method.compress = Compress.getCompress(compress);
      if (method.isNothing()) { //数据不做任何处理,返回null，避免走上报逻辑
        return null;
      }
      return method;
    }
  }
  return null
}

function formatConfigInfoLog(msg: string, config: AxiosRequestConfig) {
  HttpLog.error(TAG, '=========%s============>>>', msg);
  HttpLog.error(TAG, `baseURL：${config.baseURL}`);
  HttpLog.error(TAG, `url：${config.url}`);
  HttpLog.error(TAG, `method：${config.method}`);
  HttpLog.error(TAG, 'headers： ', Object.entries(config.headers ?? "")?.toString());
  if (config.params != null) {
    const params = config.params as Record<string, Object>
    HttpLog.error(TAG, 'params： ', Object.entries(params)?.toString());
  }

  // HttpLog.error(TAG, 'data： ', typeof config.data === 'string' ? config.data : Object.entries(config.data ?? {
  // })?.toString());
  HttpLog.error(TAG, `timeout ${config.timeout}`);
  HttpLog.error(TAG, `responseType ${config.responseType}`);
  HttpLog.error(TAG, '<<<=========%s============', msg);
}


const FORM_URL_ENCODED_HEADER: AxiosHeaders = new AxiosHeaders({
  'Content-type': 'application/x-www-form-urlencoded;charset=utf-8'
})

function handleConfig(config: ZPAxiosRequestConfig): ZPAxiosRequestConfig {

  const traceUUID = util.generateRandomUUID(false)
  /**/
  const httpProvider: IZPHttpProvider = config.httpProvider ?? defHttpProvider

  const zpBody = config.zpBody ?? ZPBody.FormBody
  /**/
  const baseUrl = HostConfig.getHost()
  /*禁用BaseUrl*/
  const disableBaseURL = config.url?.startsWith(baseUrl)
  /*公共headers*/
  let commonHeaders: AxiosHeaders = getCommonHeaders(httpProvider, traceUUID, config.isHeaderEncrypt ?? true)

  /*默认Header*/
  const defHeader: AxiosHeaders = new AxiosHeaders()
  defHeader.set('Accept', 'application/json')
  defHeader.set(config.headers as AxiosHeaders, true)
  defHeader.set(commonHeaders, true)
  config.headers = defHeader

  // let result = ''
  // configParams.forEach((value: string, key: string) => {
  //   result += `${key}: ${configParams[key]} , `;
  // })
  // Logger.debug(TAG, '请求入参 ==> : %s', result)

  let mergeConfig: ZPAxiosRequestConfig = config
  mergeConfig.baseURL = disableBaseURL ? '' : baseUrl;
  if (!mergeConfig.timeout) {
    mergeConfig.timeout = 30000
  }
  mergeConfig.responseType = 'array_buffer'

  const fullUrl = transformUrlHost(TextUtils.joinStrings('', mergeConfig.baseURL, config.url))

  /*外部参数*/
  const configParams: Map<string, string> = new Map()
  parseConfigDataList(config.params).forEach((value: string, key: string) => {
    configParams.set(key, value)
  })
  parseConfigDataList(config.data).forEach((value: string, key: string) => {
    configParams.set(key, value)
  })
  const httpParams: ZPHttpParams = getCommonParams(httpProvider, fullUrl, configParams)

  if ("POST" === mergeConfig.method || "post" === mergeConfig.method) {
    /*JSON*/
    if (ZPBody.JsonBody === zpBody) {
      mergeConfig.data = httpParams
    }
    /*Multipart*/
    else if (ZPBody.MultipartBody === zpBody) {
      let formData: FormData = new FormData();
      Object.entries(httpParams).forEach((value: [string, Object]) => {
        formData.append(value[0], value[1]?.toString());
      })
      Object.entries(config.data as ComplexReqData).forEach((value: [string, Object]) => {
        if (value[1] instanceof ArrayBuffer) {
          formData.append(value[0], value[1]);
        }
      })
      mergeConfig.data = formData as ZPReqData

      (mergeConfig.headers as AxiosHeaders).set('Content-Type', 'multipart/form-data')
      mergeConfig.context = AppStorage.get<common.Context>('context')
      mergeConfig.onUploadProgress = (progressEvent: AxiosProgressEvent): void => {
        HttpLog.debug(TAG, 'upload progress: ' + progressEvent && progressEvent.loaded && progressEvent.total ? Math.ceil(progressEvent.loaded / progressEvent.total * 100) + '%' : '0%');
      }
    }
    /*Form*/
    else {
      const formParams = new Url.URLParams();
      Object.entries(httpParams).forEach((value: [string, Object]) => {
        formParams.append(value[0], value[1]?.toString());
      })
      mergeConfig.data = formParams.toString() as ZPReqData
      (mergeConfig.headers as AxiosHeaders).set(FORM_URL_ENCODED_HEADER, true)
    }
  } else {
    mergeConfig.params = httpParams
  }

  return mergeConfig
}


function createRequest<T>(service: AxiosInstance, config: ZPAxiosRequestConfig): Promise<HttpResponse<T>> {

  return new Promise((resolve: (value: HttpResponse<T>) => void, reject: (reason?: ZPHttpError) => void) => {

    const mergeConfig: ZPAxiosRequestConfig = handleConfig(config)

    service(mergeConfig).then(
      (response: AxiosResponse) => {
        try {
          resolve(response.data)
        } catch (e) {
          reject(ZPHttpError.obj(ZPHttpError.CODE_DEFAULT, e?.toString() ?? '', response, response?.status))
        }
      },
      (error: Error) => {
        reject(obtainHttpError(error))
      }
    )
  })
}


/*提供外部数据*/
let defHttpProvider: IZPHttpProvider = new ZPHttpProvider()

let defErrorProvider: IZPHttpErrorProvider = new ZPHttpErrorProvider()

export function bindHttpProvider(provider: IZPHttpProvider) {
  defHttpProvider = provider
}

export function bindErrorProvider(provider: IZPHttpErrorProvider) {
  defErrorProvider = provider
}

/** 用于网络请求的实例 */
const service = createService()

export function initHttp(provider?: IZPHttpProvider, errorProvider?: IZPHttpErrorProvider) {
  HostConfig.init(HostConfig.QA.type)
  if (provider) {
    bindHttpProvider(provider)
  }
  if (errorProvider) {
    bindErrorProvider(errorProvider)
  }
}

export function GET<T>(url: string, params?: ZPReqData | ZPReqData[]): Promise<HttpResponse<T>> {
  return createRequest<T>(service, {
    url,
    method: "get",
    params: params,
    httpProvider: defHttpProvider,
    isHeaderEncrypt: true
  })
}

export function GET_SYNC<T>(url: string, params?: ZPReqData | ZPReqData[]): Promise<HttpResponse<T>> {
  return new Promise<HttpResponse<T>>((resolve) => {
    createRequest<T>(service, {
      url,
      method: "get",
      params: params,
      httpProvider: defHttpProvider,
      isHeaderEncrypt: true
    }).then((resp: HttpResponse<T>) => {
      resolve(resp);
    }).catch((reason: ZPHttpError) => {
      const errorResponse = new HttpResponse<T>()
      const errorCode = reason?.errorCode
      if (typeof errorCode == 'number') {
        errorResponse.code = errorCode ?? 0
      } else if (typeof errorCode == 'string') {
        errorResponse.code = Number.parseInt(errorCode, -1)
      } else {
        errorResponse.code = -1
      }
      errorResponse.message = reason?.errorReason
      resolve(errorResponse);
    })
  });
}

export function POST<T>(url: string, data?: ZPReqData | ZPReqData[]): Promise<HttpResponse<T>> {
  return createRequest<T>(service, {
    url,
    method: "post",
    zpBody: ZPBody.FormBody,
    data: data,
    httpProvider: defHttpProvider,
    isHeaderEncrypt: true
  })
}

export function UPLOAD<T>(url: string, data: ComplexReqData): Promise<HttpResponse<T>> {
  return createRequest<T>(service, {
    url,
    method: "post",
    zpBody: ZPBody.MultipartBody,
    data: data,
    httpProvider: defHttpProvider,
    isHeaderEncrypt: true
  })
}

export function POST_SYNC<T>(url: string, data?: ZPReqData | ZPReqData[]): Promise<HttpResponse<T>> {
  return new Promise<HttpResponse<T>>((resolve) => {
    createRequest<T>(service, {
      url,
      method: "post",
      zpBody: ZPBody.FormBody,
      data: data,
      httpProvider: defHttpProvider,
      isHeaderEncrypt: true
    }).then((resp: HttpResponse<T>) => {
      resolve(resp);
    }).catch((reason: ZPHttpError) => {
      const errorResponse = new HttpResponse<T>()
      const errorCode = reason?.errorCode
      if (typeof errorCode == 'number') {
        errorResponse.code = errorCode ?? 0
      } else if (typeof errorCode == 'string') {
        errorResponse.code = Number.parseInt(errorCode, -1)
      } else {
        errorResponse.code = -1
      }
      errorResponse.message = reason?.errorReason
      resolve(errorResponse);
    })
  });
}

export function POST_JSON<T>(url: string, data?: ZPReqData | ZPReqData[]): Promise<HttpResponse<T>> {
  return createRequest<T>(service, {
    url,
    method: "post",
    zpBody: ZPBody.JsonBody,
    data: data,
    httpProvider: defHttpProvider,
    isHeaderEncrypt: true
  })
}

export function BATCH<T>(batchParams: ZPBatchParams | Array<ZPBatchParams | null>, extraData?: ZPReqData | ZPReqData[]): Promise<HttpResponse<T>> {

  const batchList: Array<ZPBatchParams> = []

  const runnable = (item: ZPBatchParams | null) => {
    if (item instanceof ZPBatchParams) {
      batchList.push(item)
    } else if (item) {
      const entry: [string, T][] = Object.entries(item)
      batchList.push(ZPBatchParams.obj(entry[0]['url'], entry[0]['params']))
    }
  }

  if (Array.isArray(batchParams)) {
    batchParams.forEach(runnable)
  } else if (batchParams) {
    runnable(batchParams)
  }

  return createRequest<T>(service, {
    url: URLConfig.URL_BATCH_RUN,
    zpBody: ZPBody.BatchBody,
    method: "get",
    params: batchList,
    data: extraData,
    httpProvider: defHttpProvider,
    isHeaderEncrypt: true
  })
}

export function BATCH_SYNC<T>(batchParams: ZPBatchParams | ZPBatchParams[], extraData?: ZPReqData | ZPReqData[]): Promise<HttpResponse<T>> {

  const batchList: Array<ZPBatchParams> = []

  const runnable = (item: ZPBatchParams) => {
    if (item instanceof ZPBatchParams) {
      batchList.push(item)
    } else {
      const entry: [string, T][] = Object.entries(item)
      batchList.push(ZPBatchParams.obj(entry[0]['url'], entry[0]['params']))
    }
  }

  if (Array.isArray(batchParams)) {
    batchParams.forEach(runnable)
  } else if (batchParams) {
    runnable(batchParams)
  }

  return new Promise<HttpResponse<T>>((resolve) => {
    createRequest<T>(service, {
      url: URLConfig.URL_BATCH_RUN,
      zpBody: ZPBody.BatchBody,
      method: "get",
      params: batchList,
      data: extraData,
      httpProvider: defHttpProvider,
      isHeaderEncrypt: true
    }).then((resp: HttpResponse<T>) => {
      resolve(resp);
    }).catch((reason: ZPHttpError) => {
      const errorResponse = new HttpResponse<T>()
      const errorCode = reason?.errorCode
      if (typeof errorCode == 'number') {
        errorResponse.code = errorCode ?? 0
      } else if (typeof errorCode == 'string') {
        errorResponse.code = Number.parseInt(errorCode, -1)
      } else {
        errorResponse.code = -1
      }
      errorResponse.message = reason?.errorReason
      resolve(errorResponse);
    })
  });
}


export function getClientInfo(): string {
  return getClientInfoStr(defHttpProvider);
}

export function getDefHttpProvider(): IZPHttpProvider {
  return defHttpProvider
}