import ZPHttpError from './bean/ZPHttpError'

export interface IZPHttpErrorProvider {
  /**
   * token过期
   */
  handleTokenExpired(error: ZPHttpError): void

  /**
   * 需要验证
   */
  handleNeedVerify(error: ZPHttpError): void
}

export class ZPHttpErrorProvider implements IZPHttpErrorProvider {
  handleNeedVerify(error: ZPHttpError<Object, Object>): void {
  }


  handleTokenExpired(error: ZPHttpError<Object, Object>): void {
  }
}