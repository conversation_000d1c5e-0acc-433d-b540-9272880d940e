import { getBatchMethodName } from '../config/URLConfig';
import Url from '@ohos.url';
import HashMap from '@ohos.util.HashMap';
import NetLog from '../../utils/HttpLog';
import { FormData } from '@ohos/axios';
import HttpResponse from './HttpResponse';
import { constant } from '@kit.ConnectivityKit';
import TextUtils from '../../utils/TextUtils';

export type ComplexReqData = Object | HashMap<string, string | number> | Map<string, string | number> | Record<string, string | number>

type DefReqData = ComplexReqData | string | FormData

/**
 * 请求入参类型
 */
export type ZPReqData = DefReqData | ZPBatchParams;

/**
 * Batch接口请求入参
 */
export class ZPBatchParams {
  public url: string
  public params?: DefReqData | DefReqData[]

  constructor(url: string, params?: DefReqData | DefReqData[]) {
    this.url = url
    this.params = params
  }

  public static obj(url: string, params?: DefReqData | DefReqData[]): ZPBatchParams {
    return new ZPBatchParams(url, params)
  }

  // public static get<T>(key: string, data?: Object): T | null {
  //   return ((data as Record<string, HttpResponse<Object>>)?.[key]?.zpData as T) ?? null
  // }

  // public static getBatchCode(key: string, data?: Object): number | undefined {
  //   return (data as Record<string, HttpResponse<Object>>)?.[key]?.code
  // }
  //
  // public static getBatchMsg(key: string, data?: Object): string | undefined {
  //   return (data as Record<string, HttpResponse<Object>>)?.[key]?.message
  // }

  public static getResponse<T>(data?: Object, ...keys: string[]): HttpResponse<T> | null {
    let key = data ? keys.find((key: string) =>!!(data as Record<string, Object>)?.[key]) : null
    return!!key ? ((data as Record<string, HttpResponse<Object>>)?.[key] as HttpResponse<T>) ?? null : null
  }

  public static get<T>(data?: Object, ...keys: string[]): T | null {
    let key = data ? keys.find((key:string)=> !!(data as Record<string, Object>)?.[key]) : null
    return !!key ? ((data as Record<string, HttpResponse<Object>>)?.[key]?.zpData as T) ?? null : null
  }

  public static getBatchCode(key: string, data?: Object): number | undefined {
    return (data as Record<string, HttpResponse<Object>>)?.[key]?.code
  }

  public static getBatchMsg(key: string, data?: Object): string | undefined {
    return (data as Record<string, HttpResponse<Object>>)?.[key]?.message
  }



  public static getWithAlternativeKeys<T>(keys: string[], data?: Object): T | null {
    let retVal: T | null = null
    keys.forEach((key: string) => {
      const value = (data as Record<string, HttpResponse<Object>>)?.[key]?.zpData as T
      if (value != null) {
        retVal = value
      }
    })
    return retVal;
  }
}


/**
 * 解析请求入参
 * @param reqData 入参
 * @param urlEncode 是否encode
 */
export function parseConfigDataList(reqData?: ZPReqData | ZPReqData[], urlEncode?: boolean): Map<string, string> {

  let result: Map<string, string> = new Map<string, string>()
  let batchList: Array<string> = []

  const runnable = (item: ZPReqData) => {
    const parseData: [Map<string, string>, string] = parseConfigData(item, urlEncode)

    parseData[0]?.forEach((value: string, key: string) => {
      result.set(key, value)
    })

    if (parseData[1]) {
      batchList.push(parseData[1])
    }
  }

  if (Array.isArray(reqData)) {
    reqData.forEach(runnable)
  } else if (reqData) {
    runnable(reqData)
  }

  if (batchList.length > 0) {
    result.set('batch_method_feed', "[" + batchList.join(", ") + "]")
  }

  // const formattedResult = Object.entries(result)
  //   .map((item:[string,string]) => `${item[0]}:${Array.isArray(item[1]) ? Object.entries(item) : item}`)
  //   .join(",");
  // NetLog.error('结果 result：%{public}s', formattedResult)

  return result
}

function parseConfigData(reqData?: ZPReqData, encode?: boolean): [Map<string, string>, string] {

  const urlMap: Map<string, string> = new Map<string, string>();
  let batchKeyValues: string = ''

  if (typeof reqData === "string") {

    try {
      const json = decodeURIComponent(reqData)

      Object.entries(JSON.parse(json) as Object).forEach((item: [string, Object]) => {
        if (item[0]) {
          urlMap.set(item[0], item[1]?.toString())
        }
      })
    } catch (e) {
      NetLog.error(`parseConfigData catch , string , ${reqData}`)
    }

  } else if (reqData instanceof HashMap) {

    reqData.forEach((value: Object, key: string) => {
      urlMap.set(key, encode ? encodeURI(value?.toString()) : value?.toString())
    })

  } else if (reqData instanceof Map) {

    reqData.forEach((value: Object, key: string) => {
      urlMap.set(key, encode ? encodeURI(value?.toString()) : value?.toString())
    })

  } else if (reqData instanceof ZPBatchParams) {

    const method: string = getBatchMethodName(reqData.url)
    const result: Map<string, string> = parseConfigDataList(reqData.params, true)

    let keyAndValue = new Url.URLParams()
    result.forEach((value: string, key: string) => {
      keyAndValue.append(key, value)
    })

    batchKeyValues = '"' + 'method=' + method + "&" + keyAndValue + '"'

  } else if (reqData) {

    try {
      Object.entries(reqData).forEach((item: [string, Object]) => {
        if (item[0]) {
          urlMap.set(item[0], encode ? encodeURI(item[1]?.toString()) : item[1]?.toString())
        }
      })
    } catch (e) {
      NetLog.error(`parseConfigData catch, obj , ${reqData}`)
    }
  }

  return [urlMap, batchKeyValues]
}
