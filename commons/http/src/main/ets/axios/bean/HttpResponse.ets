import router from '@ohos.router';

export const CODE_SUCCESS: number = 0

export const CODE_VALIDATION_ERROR: number = 5;

export const CODE_TOKEN_ERROR: number = 7;

//ExceptionUtils.postCatchedException(new TWLException(11114, new Exception("sig error")));
export const CODE_SIG_ERROR: number = 8;

//您的IP地址存在异常行为，已暂时被禁止访问
export const CODE_TOKEN_ERROR_31: number = 31;

//您的账户存在异常行为，已暂时被禁止使用
export const CODE_TOKEN_ERROR_32: number = 32;

export const CODE_SERVER_ERROR_CLASSIFY: number = 1000;

//您当前访问的ip地址存在异常访问行为
export const CODE_IP_NEED_VERIFY: number = 35;

// 您当前使用的账户存在异常访问行为
export const CODE_ACCOUNT_NEED_VERIFY: number = 36;

/**
 * 接口成功
 */
export function isRespSuccessful(status: number): boolean {
  return status >= 200 && status < 300;
}

/**
 * 业务请求成功
 */
export function isSuccess(code: number): boolean {
  return code == CODE_SUCCESS;
}

/**
 * 每天每个手机号码只允许验证5次
 */
export function isValidationOverTimes(code: number): boolean {
  return code === CODE_VALIDATION_ERROR;
}

/**
 * T 票过期
 */
export function isTokenExpired(code: number): boolean {
  return code === CODE_TOKEN_ERROR || code === CODE_TOKEN_ERROR_31 || code === CODE_TOKEN_ERROR_32;
}

/**
 * 服务器公共类异常
 */
export function isServerCommonError(code: number): boolean {
  return code > 0 && code < CODE_SERVER_ERROR_CLASSIFY;
}


export function isNeedVerify(code: number): boolean {
  return code === CODE_IP_NEED_VERIFY || code === CODE_ACCOUNT_NEED_VERIFY;
}

export function isSigError(code: number): boolean {
  return code === CODE_SIG_ERROR;
}

export function isNonBusinessError(code: number) {
  return isNeedVerify(code) || isTokenExpired(code) || isServerCommonError(code) || isSigError(code)
}

export default class HttpResponse<T> {
  code: number = 0;

  message: string = '';

  zpData?: T
}

export interface SimpleResponse {
  result: boolean
}

export interface SuccessResponse {
  toast: string
}