import { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from '@ohos/axios';

export function obtainHttpError(error: Error): ZPHttpError {
  let target: ZPHttpError
  if (error instanceof ZPHttpError) {
    target = error
  } else if (error instanceof AxiosError) {
    target = ZPHttpError.obj(error.code ?? ZPHttpError.CODE_DEFAULT, error.message,
      error.response, error.response?.status, error.config, error.request, error.stack)
  } else {
    target = ZPHttpError.obj(ZPHttpError.CODE_DEFAULT, error?.message).setStack(error?.stack)
  }
  return target
}

export default class ZPHttpError<T = Object, D = Object> extends AxiosError<T, D> {
  public static readonly CODE_DEFAULT: number = -99;
  public static readonly ERROR_NETWORK_FAILED: string = "网络异常，请检查网络后重试";
  public static readonly ERROR_SERVER_FAILED: string = "服务端异常，请稍后重试";
  public static readonly ERROR_JSON: string = "数据解析失败，请稍后重试";
  public static readonly ERROR_DOWNLOAD: string = "文件下载失败，请稍后重试";
  public static readonly ERROR_UNKNOWN: string = "未知异常，请稍后重试";
  public static readonly ERROR_CODE: string = "数据解析失败，不支持的数据格式";

  errorCode: string | number
  errorReason: string

  constructor(
    code: string | number,
    message: string,
    httpStatus?: number,
    config?: InternalAxiosRequestConfig<D>,
    request?: Object,
    response?: AxiosResponse<T, D>,
    stack?: string
  ) {
    super(message, code?.toString(), config, request, response);
    this.errorCode = code
    this.errorReason = message
    this.status = httpStatus
    this.stack = stack
  }

  static obj<T = Object, D = Object>(
    code: string | number,
    message: string,
    response?: AxiosResponse<T, D>,
    httpStatus?: number,
    config?: InternalAxiosRequestConfig<D>,
    request?: Object,
    stack?: string
  ): ZPHttpError<T, D> {
    return new ZPHttpError<T, D>(code, message, httpStatus, config, request, response, stack)
  }

  setStack(stack?: string): ZPHttpError<T, D> {
    this.stack = stack
    return this
  }

  getErrorCode(): string | number {
    return this.errorCode
  }

  getErrorReason(): string {
    return this.errorReason
  }
}