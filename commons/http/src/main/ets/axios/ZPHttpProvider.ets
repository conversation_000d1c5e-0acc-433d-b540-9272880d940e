export interface IZPHttpProvider {
  getDid(): string

  getOAID(): string

  getSubSource(): string

  getUniqId(): string

  getDeviceType(): string

  getChannel(): string

  getHeaderFullUserAgent(): string

  getHeaderToken(): string

  getHeaderZPTagText(): string | null

  getSecretKey(): string | null

  getVersion(): string

  getUserRole(): string

  getAppId(): string

  getBuildVersion(): string

  checkAccessFineLocation(): boolean

  canDeviceInfoStatus(): boolean

  getWifiSSID(): string

  getWifiBSSID(): string

  getLongitude(): number

  getLatitude(): number

  getDzt(): number

  isForeground(): boolean

  isTourist(): boolean
}

export class ZPHttpProvider implements IZPHttpProvider {
  getOAID(): string {
    return "OAID"
  }

  getDid(): string {
    return "Did"
  }

  getSubSource(): string {
    return "1.0.0"
  }

  getUniqId(): string {
    return "511f541a-8f83-46ec-9525-4df8f97b35d9"
  }

  getDeviceType(): string {
    return "xiaomi" + "||" + "Redmi 5 Plus";
  }

  getChannel(): string {
    return '200'
  }

  getHeaderFullUserAgent(): string {
    let ua: string = "NetType/wifi";
    let width: number = 1080; // TODO: get width
    let height: number = 1920; // TODO: get height
    const version: string = this.getVersion()
    const otherUserAgent = `Screen/${width}X${height} BossZhipin/${version} Android 12`
    return ua + otherUserAgent
  }

  /*T2*/
  getHeaderToken(): string {
    return 'DJr_qE2Esgu0uRhB_2elr9LrKl2y_Ppf4mclPu6ggT5Isz9uY_bUuu246J4XQAxlvYBHa2w5-aDbMEv-zyefE6Q~~'
  }

  getHeaderZPTagText(): string | null {
    return ''
  }

  /*secretKey*/
  getSecretKey(): string | null {
    return 'c6ff050262d450530619316b42840e4f'
  }

  getVersion(): string {
    return "12.03"
  }

  getUserRole(): string {
    return "1"
  }

  getAppId(): string {
    return "1005"
  }

  getBuildVersion(): string {
    return ""
  }

  checkAccessFineLocation(): boolean {
    return false;
  }

  canDeviceInfoStatus(): boolean {
    return false
  }

  getWifiSSID(): string {
    return ""
  }

  getWifiBSSID(): string {
    return ""
  }

  getLongitude(): number {
    return 0
  }

  getLatitude(): number {
    return 0
  }

  getDzt(): number {
    return 0
  }

  isForeground(): boolean {
    return false
  }

  isTourist(): boolean {
    return false
  }
}