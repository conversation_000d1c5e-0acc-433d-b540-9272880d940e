// export default class Constant {

export class ReqHeader {
  public static readonly HEADER_ENCODING: string = "zp-accept-encoding";
  public static readonly HEADER_ENCRYPTION: string = "zp-accept-encrypting";
  public static readonly HEADER_COMPRESS: string = "zp-accept-compressing";
  public static readonly HEADER_ZP_TAG: string = "zp-tag";
  public static readonly HEADER_TRACE_ID: string = "traceId";
  public static readonly HEADER_USER_AGENT: string = "User-Agent";
  public static readonly HEADER_T2: string = "t2";
}

export class RspHeader {
  public static readonly HEADER_ENCODING: string = "zp-encoding";
  public static readonly HEADER_ENCRYPTION: string = "zp-encrypting";
  public static readonly HEADER_COMPRESS: string = "zp-compressing";
}

/**
 * Encoding:
 * 1 Base64 (0001)
 * 2 Base24 (0010)
 * 4 保留  (0100)
 * 8 保留  (1000)
 * <p>
 * Encrypting:
 * 1 RC4  (0001)
 * 2 RC5  (0010)
 * 4 保留  (0100)
 * 8 保留  (1000)
 * <p>
 * <p>
 * Client Req:
 * zp-accept-encoding:  3 (0011) -> 接受Base64 and base24 编码
 * zp-accept-encrypting: 1 (0001) -> 接受RC4加密
 * <p>
 * Server Resp:
 * zp-encoding: 0 (0000) -> 没有编码
 * zp-encrypting: 1 (0001) -> RC4加密
 * <p>
 * <p>
 * 客户端需要同时判断 content-encrypt 和 zp-encoding 与 zp-encrypting；
 * 优先判断 zp-encoding 与 zp-encrypting，如果没有这两个头，则判断 content-encrypt头（老逻辑）
 */
export class Encoding {
  public static readonly BASE64: string = "1";

  public static getSupport(): string {
    return Encoding.BASE64;
  }

  public static getEncoding(encoding: string): number {
    switch (encoding) {
      case Encoding.BASE64:
        return 1;
    }
    return 0;
  }
}

export class Encryption {
  public static readonly RC4: string = "1";

  public static getSupport(): string {
    return Encryption.RC4;
  }

  public static getEncryption(encryption: string): number {
    switch (encryption) {
      case Encryption.RC4:
        return 1;
    }
    return 0;
  }
}

export class Compress {
  public static readonly SNAPPY: string = "1";
  public static readonly LZ4: string = "2";

  public static getSupport(): string {
    return "3";
  }

  public static getCompress(compress: string): number {
    switch (compress) {
      case Compress.SNAPPY:
        return 1;
      case Compress.LZ4:
        return 2;
    }
    return 0;
  }
}

export class Reporter {
  public static readonly ACTION_DNS: string = "action_dns";
  public static readonly TYPE_NO_IPV6: string = "type_no_ipv6";
  public static readonly TYPE_NO_IPV4: string = "type_no_ipv4";
  public static readonly TYPE_IP_IS_NULL: string = "type_ip_is_null";
  public static readonly TYPE_NOT_IP: string = "type_not_ip";
  public static readonly TYPE_CONN_ERROR: string = "type_conn_error";
  public static readonly TYPE_CONN_FAIL: string = "type_conn_fail";
  public static readonly TYPE_DNS_EXCEPTION: string = "type_dns_exception";
  public static readonly TYPE_TEST_IPV6_ONLY: string = "type_test_ipv6_only";
  public static readonly TYPE_HTTP_DNS_FAILED: string = "type_http_dns_failed";
}

export class ProcessingMethod {
  public encoding: number = 0;
  public encryption: number = 0;
  public compress: number = 0;

  public toString(): string {
    return "ProcessingMethod{" +
      "encoding=" + this.encoding +
      ", encryption=" + this.encryption +
      ", compress=" + this.compress +
      '}';
  }

  public isNothing(): boolean {
    return this.encoding == 0 && this.encryption == 0 && this.compress == 0;
  }
}