export default class TextUtils {
  static isEmpty(str: string): boolean {
    return !str || str.length == 0
  }

  static filterInvalidateCharacter(str: string): string {
    let result = "";
    if (!TextUtils.isEmpty(str)) {
      result = str.replace("\u0000", "");
    }
    return result.trim();
  }

  static joinStrings(separator: string = '', ...strings: string[]): string {
    const filteredStrings = strings.filter(str => str !== ""); // 过滤掉空字符串
    return filteredStrings.join(separator); // 使用指定的分隔符拼接非空字符串
  }

  static equal(text1: string, text2: string) :boolean{
    if (this.isEmpty(text1) || this.isEmpty(text2)) {
      return false;
    } else if (text1 === text2) {
      return true;
    }

    return false;
  }
}
