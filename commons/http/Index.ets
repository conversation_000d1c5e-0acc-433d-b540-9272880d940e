/**
 * Http
 */
export { default as HttpResponse, isNonBusinessError, SimpleResponse } from './src/main/ets/axios/bean/HttpResponse'

export { GET,
  POST,
  POST_JSON,
  BATCH,
  ZPAxiosRequestConfig,
  ZPBody,
  getClientInfo,
  UPLOAD,
  getDefHttpProvider,
  BATCH_SYNC } from './src/main/ets/axios/service'

export { URLConfig, buildUrl } from './src/main/ets/axios/config/URLConfig'

export { HostConfig } from './src/main/ets/axios/config/HostConfig'

export { default as ZPHttpError } from './src/main/ets/axios/bean/ZPHttpError'

export { ZPReqData } from './src/main/ets/axios/bean/ReqParams'
