import { Level } from '@ohos/hypium'
import { LevelBean } from '@ohos/zp_api/Index'


export class StageEntity {
  dataVersion: number = 0
  stage: LevelBean[] = []
}


export class ScaleEntity {
  dataVersion: number = 0
  scale: LevelBean[] = []
}

export class IndustryEntity {
  dataVersion: number = 0
  industryConfig2: LevelBean[] = []
}


export class ExperienceEntity {
  dataVersion: number = 0
  experience: LevelBean[] = []
}

export class DegreeEntity {
  dataVersion: number = 0
  degree: LevelBean[] = []
}

export class SalaryEntity {
  dataVersion: number = 0
  salary: LevelBean[] = []
}


export class PositionEntity {
  positionVersion: number = 0
  cityCode: number = 0
  position: LevelBean[] = []
}

export class InternPositionEntity {
  dataVersion: number = 0
  cityCode: number = 0
  internPosition: LevelBean[] = []
}


export class CityEntity {
  dataVersion: number = 0
  city: Array<LevelBean> = []
  hotCity: Array<LevelBean> = []
  citylv2: Array<LevelBean> = []
}


export class ExtraEntity {
  dataVersion: number = 0
  dataKey: string = ''
  dataValue: string = ''
}


export class ExtraDataBean {

  GeekReports: Array<LevelBean> = []
  chargeMax: number = 0
  BossLurekeyword: Array<string> = []
  customerPhone: string = ''
  hotJobServiceHint: string = ''
  SeekerLurekeyword: Array<string> = []
  hotline: string = ''
  loginHotline: ExtraLoginHotLine = new ExtraLoginHotLine()
  maxJobCount: number = 0
  redMin: number = 0
  beian:ExtraLoginBeiAn = new ExtraLoginBeiAn()
  GeekInterviewReason: Array<LevelBean> = []
  withdrawMax: number = 0
  GeekInterviewComment: Array<LevelBean> = []
}

export class DistrictForGeekBean {
  businessDistrict:Array<LevelBean> = []
  dataVersion:number = 0
}

export class SubwayBean {
  subway:Array<LevelBean> = []
  dataVersion:number = 0
}


export class ExtraLoginHotLine { //热线电话

  customer: string = '************' //客服电话 =
  oldPeople: string = '************' //老年人直连热线
  chaoyangSocial: string = '(010)57596212,(010)65099938' //
  recommendAndMinority: string = '************'
}

export class ExtraLoginBeiAn { //备案信息
  name: string = '京ICP备14013441号-23A' //备案
  url: string = 'https://beian.miit.gov.cn' //链接
}

