import buffer from '@ohos.buffer';
import common from '@ohos.app.ability.common';
import { BasicDataConfig, CityIndex, LocalFile } from '../contants/Constant';

import { LevelBean } from '@ohos/zp_api/Index';
import {
  CityEntity,
  DegreeEntity,
  DistrictForGeekBean,
  ExperienceEntity,
  ExtraDataBean,
  ExtraEntity,
  ExtraLoginBeiAn,
  ExtraLoginHotLine,
  IndustryEntity,
  InternPositionEntity,
  PositionEntity,
  SalaryEntity,
  ScaleEntity,
  StageEntity,
  SubwayBean
} from '../module/Entity';
import { Level } from '@ohos/hypium';
import { TextUtils } from '@ohos/utils/Index';
import { Context } from '@kit.AbilityKit';


export namespace BasicDataUtils {


  /*城市数据*/
  export namespace City {

    /**
     * 获取封装好的带A-Z索引的城市
     * @context:上下文
     * */
    export function sortCityList(needLocationCity: boolean, cityList: Array<LevelBean>, hotList?: Array<LevelBean>): Array<LevelBean> {

      let result: Array<LevelBean> = getParentList();

      // 处理其它城市
      result = handlerOtherCity(result, cityList);

      if (hotList && hotList.length > 0) {
        // 热门城市处理
        result = handlerHotCity(result, hotList);
      }

      return result.filter(item => item.subLevelModelList.length > 0);
    }

    function getParentList(): Array<LevelBean> {
      let parentList: Array<LevelBean> = new Array<LevelBean>();
      for (let index = 0; index < CityIndex.INDEX_CITY_GROUP.length; index++) {
        const item: string = CityIndex.INDEX_CITY_GROUP[index];
        let bean: LevelBean = new LevelBean();
        bean.name = item;
        parentList.push(bean);
      }
      return parentList;
    }

    function handlerHotCity(result: Array<LevelBean>, hotCities: Array<LevelBean>): Array<LevelBean> {
      if (hotCities == null || hotCities.length == 0) {
        return result;
      }
      let hot: LevelBean = new LevelBean();
      hot.name = CityIndex.INDEX_HOT_CITY;
      if (hot.subLevelModelList == null) {
        hot.subLevelModelList = new Array<LevelBean>();
      }
      for (let item of hotCities) {
        hot.subLevelModelList.push(item);
      }
      result.unshift(hot);
      return result;
    }

    function handleLocalCity(result: Array<LevelBean>, hotCities: Array<LevelBean>): Array<LevelBean> {
      if (hotCities == null || hotCities.length == 0) {
        return result;
      }
      let local: LevelBean = new LevelBean();
      local.name = CityIndex.INDEX_LOCAL_CITY;
      if (local.subLevelModelList == null) {
        local.subLevelModelList = new Array<LevelBean>();
      }
      for (let item of hotCities) {
        local.subLevelModelList.push(item);
      }
      result.unshift(local);
      return result;
    }


    function handlerOtherCity(result: Array<LevelBean>, cityList: Array<LevelBean>): Array<LevelBean> {
      if (!cityList || cityList.length <= 0) return result;
      for (let city of cityList) {
        if (city === null || city.name === null) continue;
        let headChar: string = city.firstChar;
        result = handlerOtherCityHead(result, headChar, city);
      }
      return result;
    }

    function handlerOtherCityHead(result: Array<LevelBean>, headChar: String, city: LevelBean): Array<LevelBean> {

      let c: string = headChar.toUpperCase();
      if (c.length > 0) {
        let ch: number = c.charCodeAt(0);
        let index: number = ch - 'A'.charCodeAt(0);
        if (index < result.length) {
          let bean: LevelBean = result[index];
          if (bean != null && c === bean.name) {
            bean.subLevelModelList.push(city);
            return result;
          }
        }

        for (let bean of result) {
          if (bean === null || bean.name === null) continue;
          if (c == bean.name) {
            bean.subLevelModelList.push(city);
            break;
          }
        }
      }

      return result;
    }


    /**
     * 获取所有热门城市数据
     * @context:上下文
     * @fromLocal:是否从本地获取
     * */

    export function getHotCityList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {
        REQUEST_CITY(context, from).then((result: CityEntity) => {
          try {
            let cities: Array<LevelBean> = new Array<LevelBean>();
            for (let hotItem of result.hotCity) {
              cities.push(hotItem)
            }
            resolve(cities);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }

    /**
     * 获取所有城市数据,按照首字母排序
     * @context:上下文
     * @fromLocal:是否从本地获取
     * */
    export function getSortedCityList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {
        REQUEST_CITY(context, from).then((result: CityEntity) => {
          try {
            let cities: Array<LevelBean> = new Array<LevelBean>();
            for (let first of result.city) {
              if (first.subLevelModelList != null) {
                for (let second of first.subLevelModelList) {
                  if (second.cityType == 0) {
                    cities.push(second)
                  }
                }
              }
            }
            cities.sort((a: LevelBean, b: LevelBean) => {
              return a?.firstChar > b?.firstChar ? 1 : 0;
            })
            resolve(cities);

          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }
  }

  /**
   * 公司相关数据包括：
   * 规模Scale 融资Stage
   * **/

  export namespace Company {
    /**
     * 规模Scale
     * **/
    export function getScaleList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {
        REQUEST_SCALE(context, from).then((result: ScaleEntity) => {
          try {
            let scaleList: Array<LevelBean> = new Array<LevelBean>();
            for (let scaleItem of result.scale) {
              scaleList.push(scaleItem)
            }
            resolve(scaleList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }
  }

  /**
   * 融资阶段
   * **/
  export function getStageList(context: Context, from: number): Promise<Array<LevelBean>> {

    return new Promise((resolve, reject) => {
      REQUEST_STAGE(context, from).then((result: StageEntity) => {
        try {
          let stageList: Array<LevelBean> = new Array<LevelBean>();
          for (let stageItem of result.stage) {
            stageList.push(stageItem)
          }
          resolve(stageList);
        } catch (e) {
          reject(e);
        }
      }).catch((e: Error) => {
        reject(e);
      });
    });
  }


  /**
   * 职位相关数据
   * 二级行业 industry
   * 三级职类(包括实习）position internPosition
   * 行业信息 ：industry
   * 经验要求：experience
   * 学历要求：degree
   * 薪资：salary
   *
   * **/
  export namespace Position {


    /*获取二级行业数据*/
    export function getIndustryList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {

        REQUEST_INDUSTRY(context, from).then((result: IndustryEntity) => {
          try {
            let indusryList: Array<LevelBean> = new Array<LevelBean>();
            for (let item of result.industryConfig2) {
              indusryList.push(item)
            }
            resolve(indusryList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }


    /*获取三级结构的职类数据*/
    export function getPositionList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {

        REQUEST_POSITION(context, from).then((result: PositionEntity) => {
          try {
            let positionList: Array<LevelBean> = new Array<LevelBean>();
            for (let positionItem of result.position) {
              positionList.push(positionItem)
            }
            resolve(positionList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }

    /*获取三级结构的职类数据 : 实习职类*/
    export function getInternPositionList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {

        REQUEST_INTERN_POSITION(context, from).then((result: InternPositionEntity) => {
          try {
            let positionList: Array<LevelBean> = new Array<LevelBean>();
            for (let positionItem of result.internPosition) {
              positionList.push(positionItem)
            }
            resolve(positionList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }


    //薪资数据
    export function getSalaryList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {
        REQUEST_SALARY(context, from).then((result: SalaryEntity) => {
          try {
            let salaryList: Array<LevelBean> = new Array<LevelBean>();
            for (let salaryItem of result.salary) {
              salaryList.push(salaryItem)
            }
            resolve(salaryList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }

    /*获取学历要求*/
    export function getDegreeList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {

        REQUEST_DEGREE(context, from).then((result: DegreeEntity) => {
          try {
            let degreeList: Array<LevelBean> = new Array<LevelBean>();
            for (let item of result.degree) {
              degreeList.push(item)
            }
            resolve(degreeList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }

    /*获取经验要求*/
    export function getExperienceList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {

        REQUEST_EXPERIENCE(context, from).then((result: ExperienceEntity) => {
          try {
            let experienceList: Array<LevelBean> = new Array<LevelBean>();
            for (let item of result.experience) {
              experienceList.push(item)
            }
            resolve(experienceList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }
  }

  export namespace ExtraData {


    /**获取热线电话*
     * return ExtraLoginHotLine.class
     * customer 客服
     * oldPeople 老年人直连
     * chaoyangSocial 朝阳人社局
     * recommendAndMinority 推荐算法举报与未成年人举报
     * */

    export function getLoginHotline(context: Context, from: number): Promise<ExtraLoginHotLine> {
      let loginHotline: ExtraLoginHotLine = new ExtraLoginHotLine();
      return new Promise((resolve, reject) => {

        getExtraDataBean(context, from).then((result: ExtraDataBean) => {
          try {

            if (result != null && result.loginHotline != null) {
              loginHotline = result.loginHotline;
            }
            resolve(loginHotline);
          } catch (e) {
            resolve(loginHotline);
          }
        }).catch((e: Error) => {
          resolve(loginHotline);
        });
      });
    }

    export function getBeiAnInfo(context: Context, from: number): Promise<ExtraLoginBeiAn> {
      let beiAnInfo: ExtraLoginBeiAn = new ExtraLoginBeiAn();
      return new Promise((resolve, reject) => {

        getExtraDataBean(context, from).then((result: ExtraDataBean) => {
          try {
            if (result != null && result.beian != null) {
              beiAnInfo = result.beian
            }
            resolve(beiAnInfo)
          } catch (e) {
            resolve(beiAnInfo)
          }
        }).catch((e: Error) => {
          resolve(beiAnInfo)
        });
      });
    }
  }

  export namespace Resume {


    /*获取学历列表*/
    export function getDegreeList(context: Context, from: number): Promise<Array<LevelBean>> {

      return new Promise((resolve, reject) => {

        REQUEST_DEGREE(context, from).then((result: DegreeEntity) => {
          try {
            let degreeList: Array<LevelBean> = new Array<LevelBean>();
            for (let item of result.degree) {
              degreeList.push(item)
            }
            resolve(degreeList);
          } catch (e) {
            reject(e);
          }
        }).catch((e: Error) => {
          reject(e);
        });
      });
    }
  }


  /*获取ExtraData的数据*/
  export function getExtraDataBean(context: Context, from: number): Promise<ExtraDataBean> {

    return new Promise((resolve, reject) => {

      REQUEST_EXTRA_DATA(context, from).then((result: ExtraEntity) => {
        try {
          let extraData: ExtraDataBean = new ExtraDataBean();
          if (result != null && !TextUtils.isEmpty(result.dataValue)) {
            extraData = json2Module<ExtraDataBean>(result.dataValue);
            resolve(extraData);
          }
        } catch (e) {
          reject(e);
        }
      }).catch((e: Error) => {
        reject(e);
      });
    });
  }

  /*获取C端城市商圈列表*/
  export function getDistrictForGeekList(context: Context, from: number): Promise<Array<LevelBean>> {

    return new Promise((resolve, reject) => {

      REQUEST_BUSINESS_DISTINCT_FOR_GEEK(context, from).then((result: DistrictForGeekBean) => {
        try {
          let experienceList: Array<LevelBean> = new Array<LevelBean>();
          for (let item of result.businessDistrict) {
            experienceList.push(item)
          }
          resolve(experienceList);
        } catch (e) {
          reject(e);
        }
      }).catch((e: Error) => {
        reject(e);
      });
    });
  }

  /*获取地铁列表*/
  export function getSubwayList(context: Context, from: number): Promise<Array<LevelBean>> {
    return new Promise((resolve, reject) => {
      REQUEST_SUBWAY(context, from).then((result: SubwayBean) => {
        try {
          let experienceList: Array<LevelBean> = new Array<LevelBean>();
          for (let item of result.subway) {
            experienceList.push(item)
          }
          resolve(experienceList);
        } catch (e) {
          reject(e);
        }
      }).catch((e: Error) => {
        reject(e);
      });
    });
  }

}


function REQUEST_STAGE(context: Context, from: number): Promise<StageEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<StageEntity>(context, LocalFile.JSON_STAGE);
  } else {
    return getModuleData<StageEntity>(context, LocalFile.JSON_STAGE);
  }
}


/**
 * 获取本地薪资数据
 * @context:上下文
 * @fromLocal:是否从本地获取
 * */

function REQUEST_SALARY(context: Context, from: number): Promise<SalaryEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<SalaryEntity>(context, LocalFile.JSON_SALARY);
  } else {
    return getModuleData<SalaryEntity>(context, LocalFile.JSON_SALARY);
  }
}

/**
 * 获取本地薪资数据
 * @context:上下文
 * @fromLocal:是否从本地获取
 * */

function REQUEST_EXPERIENCE(context: Context, from: number): Promise<ExperienceEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<ExperienceEntity>(context, LocalFile.JSON_EXPERIENCE);
  } else {
    return getModuleData<ExperienceEntity>(context, LocalFile.JSON_EXPERIENCE);
  }
}

/**
 * 获取本地学历要求
 * @context:上下文
 * @fromLocal:是否从本地获取
 * */

function REQUEST_DEGREE(context: Context, from: number): Promise<DegreeEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<DegreeEntity>(context, LocalFile.JSON_DEGREE);
  } else {
    return getModuleData<DegreeEntity>(context, LocalFile.JSON_DEGREE);
  }
}


/**
 * 获取公司规模数据
 * @context:上下文
 * @fromLocal:是否从本地获取
 * */

function REQUEST_SCALE(context: Context, from: number): Promise<ScaleEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<ScaleEntity>(context, LocalFile.JSON_SCALE);
  } else {
    return getModuleData<ScaleEntity>(context, LocalFile.JSON_SCALE);
  }
}

/**
 * 获取二级职行业
 * @context:上下文
 * @fromLocal:是否从本地获取
 * */
function REQUEST_INDUSTRY(context: Context, from: number): Promise<IndustryEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<IndustryEntity>(context, LocalFile.JSON_INDUSTRY);
  } else {
    return getModuleData<IndustryEntity>(context, LocalFile.JSON_INDUSTRY);
  }
}


/**
 * 获取三级职类数据
 * @context:上下文
 * @fromLocal:是否从本地获取
 * */
function REQUEST_POSITION(context: Context, from: number): Promise<PositionEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<PositionEntity>(context, LocalFile.JSON_POSITION);
  } else {
    return getModuleData<PositionEntity>(context, LocalFile.JSON_POSITION);
  }
}

/**
 * 获取三级职类数据
 * @context:上下文
 * @fromLocal:是否从本地获取
 * */
function REQUEST_INTERN_POSITION(context: Context, from: number): Promise<InternPositionEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<InternPositionEntity>(context, LocalFile.JSON_POSITION_INTERN);
  } else {
    return getModuleData<InternPositionEntity>(context, LocalFile.JSON_POSITION_INTERN);
  }
}


function REQUEST_CITY(context: Context, from: number): Promise<CityEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<CityEntity>(context, LocalFile.JSON_CITY);
  } else {
    return getModuleData<CityEntity>(context, LocalFile.JSON_CITY);
  }
}

function REQUEST_EXTRA_DATA(context: Context, from: number): Promise<ExtraEntity> {

  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<ExtraEntity>(context, LocalFile.JSON_EXTRA_DATA);
  } else {
    return getModuleData<ExtraEntity>(context, LocalFile.JSON_EXTRA_DATA);
  }
}

function REQUEST_BUSINESS_DISTINCT_FOR_GEEK(context: Context,from:number): Promise<DistrictForGeekBean> {
  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<DistrictForGeekBean>(context, LocalFile.JSON_BUSINESS_DISTINCT_FOR_GEEK);
  } else {
    return getModuleData<DistrictForGeekBean>(context, LocalFile.JSON_BUSINESS_DISTINCT_FOR_GEEK);
  }
}

function REQUEST_SUBWAY(context: Context,from:number): Promise<SubwayBean> {
  if (from == BasicDataConfig.FROM_LOCAL) {
    return getModuleData<SubwayBean>(context, LocalFile.JSON_SUBWAY);
  } else {
    return getModuleData<SubwayBean>(context, LocalFile.JSON_SUBWAY);
  }
}

function getModuleData<T>(context: Context, fileName: string): Promise<T> {

  return new Promise((resolve, reject) => {

    getRawJsonString(context, fileName).then((jsonStr: string) => {
      try {
        const result: T = json2Module<T>(jsonStr);
        resolve(result);
      } catch (e) {
        reject(e);
      }
    }).catch((e: Error) => {
      reject(e);
    });
  });

}


function getRawJsonString(context: Context, fileName: string): Promise<string> {

  return new Promise((resolve, reject) => {
    context.resourceManager.getRawFileContent(fileName).then((fileArray: Uint8Array) => {
      try {
        const jsonStr: string = buffer.from(fileArray.buffer).toString('utf8');
        resolve(jsonStr);
      } catch (e) {
        reject(e);
      }
    }).catch((e: Error) => {
      reject(e);
    });
  });
}

function json2Module<T>(jsonString: string): T {

  const result: T = JSON.parse(jsonString)
  return result;

}
