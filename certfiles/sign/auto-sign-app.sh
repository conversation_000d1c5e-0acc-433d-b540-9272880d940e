#!/bin/bash
# 获取脚本所在的目录
script_dir=$(dirname "$(realpath "$0")")
# 切换到脚本所在的目录
cd "$script_dir" || exit

keyAlias="BZ"
signAlg="SHA256withECDSA"
#keyPwd="动态输入"
#keystorePwd="动态输入"
appCertFile="../BossZhipin_Release.cer"
profileFile="../Zhipin-all-devicesDebug.p7b"
keystoreFile="../zhipin_release.p12"


# 检查参数数量是否正确
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <xxx.jar absolute path> <aa.app relative path> <signing password>"
    exit 1
fi

# 获取参数
signToolPath="$1"
signing_password="$2"
unSignFilePath="$3"


# 检查 hap-sign-tool.jar 是否存在
if [ ! -f "$signToolPath" ]; then
    echo "Error: hap-sign-tool.jar file not found at $signToolPath"
    exit 1
fi


# 解压 输入的.app文件到 到 app 文件夹
un_sign_file_path=$(dirname "$unSignFilePath")
tmp_dir="$un_sign_file_path/app"
if [ -d "$tmp_dir" ]; then
    rm -rf "$tmp_dir"
fi

mkdir -p "$tmp_dir"
unzip -q "$unSignFilePath" "*.hap" "*.hsp" -d "$tmp_dir"

# 签名 .hap 或 .hsp 文件并删除原文件
#find "$tmp_dir" -type f \( -name "*.hap" -o -name "*.hsp" \) -print0 |
#while IFS= read -r -d '' file; do
find "$tmp_dir" -type f \( -name "*.hap" -o -name "*.hsp" \) |
while IFS= read -r file; do
    abs_file=$(realpath "$file")

    abs_dir=$(realpath "$(dirname "$file")")
    file_name=$(basename -- "$file" | sed 's/\.[^.]*$//')
    file_extension="${file##*.}"  # 获取文件后缀
    abs_signed_file="${abs_dir}/$file_name-signed.$file_extension"
    echo "-abs_dir =  $abs_dir -inFile =  $abs_file  -outFile =  ${abs_signed_file}"
    
    java -jar "$signToolPath" sign-app -keyAlias "$keyAlias" -signAlg "$signAlg" -mode "localSign" -appCertFile "$appCertFile" -profileFile "$profileFile" -inFile "$abs_file" -keystoreFile "$keystoreFile" -outFile "$abs_signed_file" -keyPwd "${signing_password}" -keystorePwd "${signing_password}" -signCode "1"
    rm "$file"
done

#exit
# 获取 .app 文件的基本名称（不含路径）
input_file_name=$(basename "$unSignFilePath" .app)

# 压缩 tmp 文件夹为输入文件名加上-resign后缀的 zip 包
cd "$un_sign_file_path"
zip -r "${input_file_name}-resigned.zip" "app"
rm -rf app

echo "Signing and compression completed successfully."
